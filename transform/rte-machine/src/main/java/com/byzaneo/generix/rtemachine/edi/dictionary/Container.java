package com.byzaneo.generix.rtemachine.edi.dictionary;

import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @param <T> type of DictNode contained in this container.
 */
public class Container<T extends DictNode> implements Iterable<T> {

  private final Map<String, T> CHILDREN = new LinkedHashMap<>();
  private final Map<String, Integer> INDEXES = new HashMap<>();

  protected final void add(T node) {
    int index = nextIndex(node.getName());
    node.setKey(index);
    CHILDREN.put(node.getKey(), node);
  }

  private int nextIndex(String name) {
    Integer lastIndex = INDEXES.getOrDefault(name, 0);
    Integer newIndex = lastIndex + 1;
    INDEXES.put(name, newIndex);
    return newIndex;
  }

  public T get(String key) {
    return CHILDREN.get(key);
  }

  public int getPosition(String key) {
    T t = CHILDREN.get(key);
    // search position of the segment in the group
    int i = 0;
    for (T t1 : CHILDREN.values()) {
      i++;
      if (t1.equals(t)) return i;
    }
    return -1;
  }

  @Override
  public Iterator<T> iterator() {
    return CHILDREN.values()
        .iterator();
  }
}
