package com.byzaneo.generix.rtemachine.context;

import static com.byzaneo.generix.rtemachine.builtinfunction.TimeFunction.DEFAULT_TIME_PATTERN;
import static com.byzaneo.generix.rtemachine.context.FileVariable.Type.DIRECTORY;
import static com.byzaneo.generix.rtemachine.context.FileVariable.Type.REGULAR;
import static com.byzaneo.generix.rtemachine.context.FileVariable.Type.SPECIAL;
import static com.byzaneo.generix.rtemachine.context.FileVariable.Type.UNKNOWN;
import static java.math.BigDecimal.ZERO;
import static java.math.BigDecimal.valueOf;
import static java.nio.file.Files.lines;
import static java.nio.file.Files.readAttributes;
import static java.nio.file.Paths.get;
import static org.apache.commons.lang3.StringUtils.isBlank;

import java.io.*;
import java.math.BigDecimal;
import java.nio.file.*;
import java.nio.file.attribute.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Stream;

import org.apache.commons.lang3.StringEscapeUtils;
import org.slf4j.*;

import com.byzaneo.generix.rtemachine.bean.FormattedString;

public class FileVariable extends RteVariable<FormattedString> {
  private static final long serialVersionUID = 2266915787447694988L;

  private static final Logger LOGGER = LoggerFactory.getLogger(FileVariable.class);

  private final SimpleDateFormat DEFAULT_FORMAT = new SimpleDateFormat(DEFAULT_TIME_PATTERN);
  private static final Date RTE_EPOCH = new Date(0);
  private static final String UNIX_CTIME = "unix:ctime";

  public enum Type {
    REGULAR,
    DIRECTORY,
    SPECIAL,
    UNKNOWN
  };

  private Path path;
  private BasicFileAttributes attributes;

  public FileVariable(String identifier, Path value) {
    super(identifier);
    this.path = value;
  }

  public boolean exists() {
    return path != null && Files.exists(path);
  }

  public String getName() {
    return path == null
        ? null
        : path.getFileName()
            .toString();
  }

  public String getPath() {
    return Optional.ofNullable(path)
        .map(Path::toAbsolutePath)
        .map(Path::normalize)
        .map(Path::getParent)
        .map(Path::toString)
        .map(StringEscapeUtils::escapeJava)
        .orElse(null);
  }

  public String getFullname() {
    return Optional.ofNullable(path)
        .map(Path::toAbsolutePath)
        .map(Path::normalize)
        .map(Path::toString)
        .map(StringEscapeUtils::escapeJava)
        .orElse(null);
  }

  public BigDecimal getLines(Appendable appendable) {
    if (this.exists()) {
      try (Stream<String> lines = lines(this.path)) {
        return valueOf(lines.count());
      }
      catch (IOException e) {
        throw new UncheckedIOException(e);
      }
    }
    displayWarning(appendable);
    return ZERO;
  }

  public String getAtime(Appendable appendable) {
    if (attributes == null) {
      displayWarning(appendable);
      return DEFAULT_FORMAT.format(RTE_EPOCH);
    }
    else {
      return DEFAULT_FORMAT.format(attributes.lastAccessTime()
          .toMillis());
    }
  }

  public String getMtime(Appendable appendable) {
    if (attributes == null) {
      displayWarning(appendable);
      return DEFAULT_FORMAT.format(RTE_EPOCH);
    }
    else {
      return DEFAULT_FORMAT.format(attributes.lastModifiedTime()
          .toMillis());
    }
  }

  public String getCtime(Appendable appendable) {
    if (attributes == null) {
      displayWarning(appendable);
      return DEFAULT_FORMAT.format(RTE_EPOCH);
    }
    else {
      try {
        return DEFAULT_FORMAT.format(((FileTime) Files.getAttribute(path, UNIX_CTIME)).toMillis());
      }
      catch (UnsupportedOperationException | IOException e) {
        LOGGER.debug("Not an unix system");
        return DEFAULT_FORMAT.format(attributes.creationTime()
            .toMillis());
      }
    }
  }

  public String getOwner(Appendable appendable) {
    if (this.exists()) {
      try {
        return Files.getOwner(path)
            .getName();
      }
      catch (IOException e) {
        throw new UncheckedIOException(e);
      }
    }
    displayWarning(appendable);
    return null;
  }

  public BigDecimal getSize(Appendable appendable) {
    if (attributes == null) {
      displayWarning(appendable);
      return BigDecimal.ZERO;
    }
    return valueOf(attributes.size());
  }

  public boolean isReadable() {
    return path != null && Files.isReadable(path);
  }

  public boolean isWritable() {
    return path != null && Files.isWritable(path);
  }

  public boolean isExecutable() {
    return path != null && Files.isExecutable(path);
  }

  public Type getType(Appendable appendable) {
    if (attributes == null) {
      displayWarning(appendable);
      return UNKNOWN;
    }
    if (attributes.isDirectory()) {
      return DIRECTORY;
    }
    if (attributes.isRegularFile()) {
      return REGULAR;
    }
    return SPECIAL;
  }

  /** @see com.byzaneo.generix.rtemachine.context.RteVariable#setValue(java.lang.Object) */
  @Override
  public void setValue(FormattedString value) {
    if (isBlank(value.getValue())) {
      this.path = null;
      return;
    }
    this.path = get(value.getValue());
    if (this.exists()) {
      try {
        attributes = readAttributes(this.path, BasicFileAttributes.class);
      }
      catch (IOException e) {
        throw new UncheckedIOException(e);
      }
    }
  }

  /** @see com.byzaneo.generix.rtemachine.context.RteVariable#getValue() */
  @Override
  public FormattedString getValue() {
    return path == null
        ? null
        : new FormattedString(path.toAbsolutePath()
            .toString());
  }

  private void displayWarning(Appendable appendable) {
    try {
      appendable.append("WARNING : Failed to retrieve file information for \"")
          .append(path == null ? "null" : this.path.toAbsolutePath()
              .toString())
          .append("\" (error code 2)\n");
    }
    catch (IOException ex) {
      throw new UncheckedIOException(ex);
    }
  }
}
