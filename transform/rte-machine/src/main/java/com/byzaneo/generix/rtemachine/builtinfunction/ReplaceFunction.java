package com.byzaneo.generix.rtemachine.builtinfunction;

import static java.util.Optional.ofNullable;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;

import com.byzaneo.generix.rtemachine.RteRuntime;
import com.byzaneo.generix.rtemachine.bean.FormattedString;
import com.byzaneo.generix.rtemachine.exception.RteException;

/**
 * <AUTHOR> Aboulaye <<EMAIL>>
 * @company Generix group
 * @date 3 mars 2016
 */
public class ReplaceFunction implements BuiltInFunction {

  @Override
  public Object call(RteRuntime runtime, List<Object> args, int statementInputFileLine) throws RteException {

    ofNullable(args)
        .filter(list -> list.size() == 3)
        .orElseThrow(() -> new RteException("3 arguments expected " + args));

    String originalStr = args.get(0) instanceof FormattedString ? ((FormattedString) args.get(0)).getValue() : (String) args.get(0);
    String tOriginal = StringEscapeUtils.unescapeJava(originalStr);

    final String tRemoved = StringEscapeUtils.unescapeJava(((FormattedString) args.get(1)).getValue());
    final String tReplacements = StringEscapeUtils.unescapeJava(((FormattedString) args.get(2)).getValue());
    if (StringUtils.isEmpty(tOriginal)) return "";
    tOriginal = StringUtils.replaceChars(tOriginal, tRemoved, tReplacements);
    return StringEscapeUtils.escapeJava(tOriginal);
  }

}
