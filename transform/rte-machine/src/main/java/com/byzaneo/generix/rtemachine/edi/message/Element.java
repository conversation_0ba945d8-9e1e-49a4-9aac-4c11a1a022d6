package com.byzaneo.generix.rtemachine.edi.message;

import static com.byzaneo.generix.rtemachine.edi.dictionary.Charset.UNOD;
import static com.byzaneo.generix.rtemachine.edi.dictionary.CharsetType.NUMERIC;
import static com.byzaneo.generix.rtemachine.edi.error.ErrorCode.E003;
import static com.byzaneo.generix.rtemachine.edi.error.ErrorCode.E006;
import static com.byzaneo.generix.rtemachine.edi.error.ErrorCode.E009;
import static com.byzaneo.generix.rtemachine.edi.error.ErrorCode.E012;
import static com.byzaneo.generix.rtemachine.edi.error.ErrorCode.E020;
import static com.byzaneo.generix.rtemachine.edi.error.ErrorCode.E023;
import static com.byzaneo.generix.rtemachine.util.RteMachineHelper.lengthWithoutEscapeChar;
import static java.lang.Double.parseDouble;
import static java.lang.Integer.parseInt;
import static java.lang.String.format;
import static java.util.regex.Pattern.compile;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.commons.lang3.StringUtils.stripEnd;
import static org.slf4j.LoggerFactory.getLogger;

import java.util.List;
import java.util.regex.*;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

import com.byzaneo.generix.rtemachine.RteRuntime;
import com.byzaneo.generix.rtemachine.edi.dictionary.*;
import com.byzaneo.generix.rtemachine.edi.error.*;
import com.byzaneo.generix.rtemachine.exception.RteCharsetException;
import com.byzaneo.generix.rtemachine.util.RteMachineHelper;

/**
 * <AUTHOR>
 */
public class Element extends DataElement {

  private static final Logger LOGGER = getLogger(Element.class);

  private static final Pattern NUMERIC_PATTERN = compile("^(0*)(.*)");

  private String value;
  private boolean formatted;
  private Charset charset;

  /* -- CONSTRUCTOR -- */

  Element(ElementNode node) {
    super(node);
  }

  /* -- ACCESSORS -- */

  public void setValue(String value) {
    this.value = stripEnd(value, null);
    try {
      if (value == null) return;
      // Test if value is numeric
      Long.parseLong(value.trim());
      this.value = isNotBlank(value) ? value.trim() : value;
    }
    catch (NumberFormatException e) {
      // ignored : just to test value type
    }
  }

  public String getValue() {
    return value;
  }

  public void setFormatted(boolean formatted) {
    this.formatted = formatted;
  }

  public boolean isFormatted() {
    return this.formatted;
  }

  /* -- VALIDATION -- */

  /** @see com.byzaneo.generix.rtemachine.variable.Validable#validate(com.byzaneo.generix.rtemachine.RteRuntime) */
  @Override
  public boolean validate(RteRuntime runtime) {
    if (this.hasError()) {
      return false;
    }

    if (node.isMandatory() && isEmpty()) {
      this.addError(E012);
      return false;
    }

    // resolves charset
    checkCharset(runtime);
    if (this.hasError()) {
      return false;
    }

    // checks type
    checkType();
    if (this.hasError()) {
      return false;
    }

    // checks length
    checkLength((Character) runtime.getProperties()
        .get(SeparatorSegment.ESCAPE_CHARACTER));
    if (this.hasError()) {
      return false;
    }

    return true;
  }

  private void checkCharset(RteRuntime runtime) {
    try {
      charset = runtime == null ? UNOD : runtime.getEdiCharset(UNOD);
      LOGGER.debug("Charset: {}", charset);
    }
    catch (RteCharsetException e) {
      this.addError(E009, e.getCharset());
      return;
    }
    catch (Exception e) {
      this.addError(E009, "UNKNOWN");
      return;
    }

    try {
      charset.validate(value);
    }
    catch (Exception e) {
      this.addError(E020, charset.toString());
    }
  }

  void checkType() {
    ElementNode eNode = (ElementNode) node;
    if (!eNode.getType()
        .evaluate(charset, value)) {
      this.addError(E023, eNode.getType()
          .name(), "");
    }
  }

  private void checkLength(char escapeCharacter) {
    ElementNode eNode = (ElementNode) node;
    String length = eNode.getLength();
    int valueLengthh = lengthWithoutEscapeChar(value, escapeCharacter);
    if (length.startsWith("..")) {
      int maxLength = parseInt(length.substring(2));
      if (valueLengthh > maxLength) {
        this.addError(E006, maxLength);
      }
    }
    else {
      int fixedLength = parseInt(length);
      if (valueLengthh > fixedLength) {
        this.addError(E006, fixedLength);
      }
      else if (valueLengthh < fixedLength) {
        this.addError(E003, fixedLength);
      }
    }
  }

  /* -- IMPLEMENTATION -- */

  /** @see com.byzaneo.generix.rtemachine.edi.message.DataElement#isEmpty() */
  @Override
  public boolean isEmpty() {
    return isBlank(value);
  }

  private boolean isNumeric() {
    return NUMERIC.equals(((ElementNode) node).getType());
  }

  /** @see com.byzaneo.generix.rtemachine.edi.message.Printable#toMessageString(java.lang.String) */
  @Override
  public String toMessageString(String joiner, SeparatorSegment separatorSegment) {
    if (isEmpty())
      return "";
    if (isNumeric() && !isFormatted()) {
      // keeps 0 prefix
      Matcher m = NUMERIC_PATTERN.matcher(value);
      if (!m.matches())
        return value;
      String prefix = m.group(1);
      String numeric = m.group(2);
      if (StringUtils.isEmpty(numeric)) {
        return prefix;
      }
      try {
        double d = parseDouble(numeric);
        if (d < 1) {
          return new StringBuilder(prefix).append(numeric)
              .toString();
        }
        return d == (long) d
            ? format("%s%d", prefix, (long) d)
            : format("%s%s", prefix, numeric);
      }
      catch (NumberFormatException e) {
        LOGGER.error("Malformed numeric value '{}' in '{}' ({})",
            value, getKey(), e.getMessage());
      }
    }
    return RteMachineHelper.escapeEdi(value, separatorSegment);
  }

  /**
   * @see com.byzaneo.generix.rtemachine.edi.message.EdiError#errors(com.byzaneo.generix.rtemachine.edi.error.ErrorLocation, java.util.List)
   */
  @Override
  public void errors(ErrorLocation location, List<ErrorControl> controls) {
    location.newData(this);

    // local Error as Message level errors
    // should never happened...
    this.getErrors()
        .forEach(e -> controls.add(new ErrorControl(location, e)));

    // updates location
    location.updatePosition(this);
  }
}
