package com.byzaneo.generix.rtemachine.builtinfunction;

import static com.byzaneo.commons.service.ConfigurationService.ConfigurationKey.DATABASE_URL;
import static com.byzaneo.commons.service.ConfigurationService.ConfigurationKey.DATABASE_VIEW_PASSWORD;
import static com.byzaneo.commons.service.ConfigurationService.ConfigurationKey.DATABASE_VIEW_USER;

import java.sql.*;
import java.util.*;

import org.slf4j.*;

import com.byzaneo.commons.service.ConfigurationServiceImpl;
import com.byzaneo.generix.rtemachine.RteRuntime;
import com.byzaneo.generix.rtemachine.exception.*;

public class SqlConnectFunction implements BuiltInFunction {

  private static final Logger log = LoggerFactory.getLogger(SqlConnectFunction.class);

  public static final String RTE_SQL_CONNECTION = "rteSqlConnection";

  @Override
  public Object call(RteRuntime runtime, List<Object> args, int statementInputFileLine)
      throws RteException, RteInternalException, ExitFunctionException {
    sqlConnect(runtime);
    return null;
  }

  private void sqlConnect(RteRuntime runtime) throws RteException {
    String dbUserName = ConfigurationServiceImpl.properties.getProperty(DATABASE_VIEW_USER.getKey());
    String dbPassword = ConfigurationServiceImpl.properties.getProperty(DATABASE_VIEW_PASSWORD.getKey());
    if (dbUserName == null || dbPassword == null) {
      throw new RteRuntimeException("Impossible to connect, properties database.view.user or database.view.password are missing.");
    }
    String url = ConfigurationServiceImpl.properties.getProperty(DATABASE_URL.getKey());
    Properties props = new Properties();
    props.setProperty("user", dbUserName);
    props.setProperty("password", dbPassword);
    try {
      Connection connection = DriverManager.getConnection(url, props);
      boolean isConected = !connection.isClosed();
      if (isConected)
        runtime.getProperties()
            .put(RTE_SQL_CONNECTION, connection);
    }
    catch (SQLException e) {
      log.error("Error creating connection: ", e);
      throw new RteRuntimeException("Impossible to connect, please check the database properties. User or password is wrong.");
    }
  }

}
