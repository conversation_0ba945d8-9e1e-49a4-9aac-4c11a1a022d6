!   Date: 06/12/2016 
!   Nom: QAL-AIO-5695.rte 
!   Auteur: CGA
!   But: fonction put() pour des tableaux de texte
!   #NOTE# partie du code commentée parce-que allant en erreur en java

begin

tChar := "bizarrement"
nNbre := 12390.76453
tChars := "antimoustiques"
nNbres := 7612.561

FILL_CHAR := "%"
put (taTest, 2, 4, "[")
put (taTest, 2, 5, "Toto et Momo":30)
put (taTest, 2, 45, "]")

put (taTest, 3, 4, "[")
put (taTest, 3, 5, 234.89:30)
put (taTest, 3, 45, "]")

FILL_CHAR := " "
put (taTest, 1, 1, "Dupont")
put (taTest, 5, 0, tChar)
!print(NL)

!---------------------------------------------
!print("Chaîne de caractères direct avec en 4ème argument avec formattage",NL)
!print(NL)

put (taTest, 11, 6, "[")
put (taTest, 11, 7, "Hippopotame":L30)
put (taTest, 11,37 , "]")

put (taTest, 12, 6, "[")
put (taTest, 12, 7, "Hippopotame":R30)
put (taTest, 12,37 , "]")

put (taTest, 13, 6, "[")
put (taTest, 13, 7, "Hippopotame":L30.6)
put (taTest, 13,37 , "]")

put (taTest, 14, 6, "[")
put (taTest, 14, 7, "Hippopotame":R30.6)
put (taTest, 14,37 , "]")
!print(NL)

!-----------------------------------------------------
!print("Valeur numérique direct avec en 4ème argument avec formattage",NL)
!print(NL)

put (taTest, 15, 6, "[")
put (taTest, 15, 7, 678.67:R30.6)
put (taTest, 15,37 , "]")

put (taTest, 16, 6, "[")
put (taTest, 16, 7, 678.67:L30.6)
put (taTest, 16,37 , "]")

put (taTest, 17, 6, "[")
put (taTest, 17, 7, 678.67:R30)
put (taTest, 17,37 , "]")

put (taTest, 18, 6, "[")
put (taTest, 18, 7, 678.67:L30)
put (taTest, 18,37 , "]")
!print(NL)

!-----------------------------------------------------
!print("Chaine char prefixé t en 4ème argument avec formattage",NL)
!print(NL)

put (taTest, 19, 6, "[")
put (taTest, 19, 7, tChar:R30.6)
put (taTest, 19,37 , "]")

put (taTest, 20, 6, "[")
put (taTest, 20, 7, tChar:L30.6)
put (taTest, 20,37 , "]")

put (taTest, 21, 6, "[")
put (taTest, 21, 7, tChar:R30)
put (taTest, 21,37 , "]")

put (taTest, 22, 6, "[")
put (taTest, 22, 7, tChar:L30)
put (taTest, 22,37 , "]")
!print(NL)

!----------------------------------------------------------

!print("fonction prefixé tf en 4ème argument avec formattage",NL)
!print(NL)

put (taTest, 23, 6, "[")
put (taTest, 23, 7, tfSimpleText(tChar):R30)
put (taTest, 23,37 , "]")

put (taTest, 24, 6, "[")
put (taTest, 24, 7, tfSimpleText(tChar):L30)
put (taTest, 24,37 , "]")

put (taTest, 25, 6, "[")
put (taTest, 25, 7, tfSimpleText(tChar):R30.6)
put (taTest, 25,37 , "]")

put (taTest, 26, 6, "[")
put (taTest, 26, 7, tfSimpleText(tChar):L30.6)
put (taTest, 26,37 , "]")
!print(NL)

!----------------------------------------------------

!print("Val numérique prefixé n en 4ème argument avec formattage",NL)
!print(NL)

put (taTest, 27, 6, "[")
put (taTest, 27, 7, nNbre:R30)
put (taTest, 27,37 , "]")

put (taTest, 28, 6, "[")
put (taTest, 28, 7, nNbre:L30)
put (taTest, 28,37 , "]")

put (taTest, 29, 6, "[")
put (taTest, 29, 7, nNbre:R30.3)
put (taTest, 29,37 , "]")

put (taTest, 30, 6, "[")
put (taTest, 30, 7, nNbre:L30.3)
put (taTest, 30,37 , "]")
!print(NL)

!---------------------------------------------

!print("fonction prefixé nf avec en 4ème argument avec formattage",NL)
!print(NL)

put (taTest, 31, 6, "[")
put (taTest, 31, 7, nfSimpleNum(nNbre):R30)
put (taTest, 31,37 , "]")

put (taTest, 32, 6, "[")
put (taTest, 32, 7, nfSimpleNum(nNbre):L30)
put (taTest, 32,37 , "]")

put (taTest, 33, 6, "[")
put (taTest, 33, 7, nfSimpleNum(nNbre):R30.3)
put (taTest, 33,37 , "]")

put (taTest, 34, 6, "[")
put (taTest, 34, 7, nfSimpleNum(nNbre):L30.3)
put (taTest, 34,37 , "]")
!print(NL)

!------------------------------------------------

!print("Chaîne char en 4ème argument avec formattage de type *.* ou *",NL)
!print(NL)
/*
put (taTest, 35, 6, "[")
put (taTest, 35, 7, tChars:R*,30)
put (taTest, 35,37 , "]")

put (taTest, 36, 6, "[")
put (taTest, 36, 7, tChars:L*,30)
put (taTest, 36,37 , "]")

put (taTest, 37, 6, "[")
put (taTest, 37, 7, tChars:R*.*,30,4)
put (taTest, 37,37 , "]")

put (taTest, 38, 6, "[")
put (taTest, 38, 7, tChars:L*.*,30,4)
put (taTest, 38,37 , "]")
!print(NL)

!--------------------------------------------------------------

!print(NL)

put (taTest, 39, 6, "[")
put (taTest, 39, 7, nNbres:R*,30)
put (taTest, 39,37 , "]")

put (taTest, 40, 6, "[")
put (taTest, 40, 7, nNbres:L*,30)
put (taTest, 40,37 , "]")

put (taTest, 41, 6, "[")
put (taTest, 41, 7, nNbres:R*.*,30,4)
put (taTest, 41,37 , "]")

put (taTest, 42, 6, "[")
put (taTest, 42, 7, nNbres:L*.*,30,4)
put (taTest, 42,37 , "]")
*/
print(taTest)            

endbegin

function tfSimpleText (tArgEffectif)
tEff := tArgEffectif
return tEff
endfunction

function nfSimpleNum (nArgEffectif)
nEff := nArgEffectif
return nEff
endfunction
