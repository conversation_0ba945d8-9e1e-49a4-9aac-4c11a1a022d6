!   Date: 03/10/2016 
!   Nom: QAL-FLAT-04
!Auteur: CDE
!   But: line logical operators Chap 3.2.4


line(5:"TEST" and "Hello")
   print("5:TEST et Hello  en ligne ",LINE,NL)
endline

line(4 and "Hello")
   print("4 et Hello en ligne ",LINE,NL)
endline

line("TEST" and "Hello")
   print("TEST et Hello  en ligne ",LINE,NL)
endline

line("TEST" or "Hello")
   print("TEST ou Hello  en ligne ",LINE,NL)
endline

line(not "Hello")
   print("non Hello en ligne ",LINE,NL)
endline
