#====================================================================
#
#                              UN/EDIFACT
#
#                         DRAFT RECOMMENDATION
#
#            Application error and acknowledgement message
#
#
#
#
#====================================================================
MESSAGE=APERAK
VERSION=D
RELEASE=96A
AGENCY=UN
COMMENT
ENDCOMMENT

#====================================================================
SEGMENTS segs
#====================================================================
UNH M 1 Message header                            
BGM M 1 Beginning of message                      
DTM C 9 Date/time/period                          
FTX C 9 Free text                                 
CNT C 9 Control total                             
group 1 C 9
  RFF M 1 Reference                                 
  DTM C 9 Date/time/period                          
endgroup 1
group 2 C 9
  NAD M 1 Name and address                          
  CTA C 9 Contact information                       
  COM C 9 Communication contact                     
endgroup 2
group 3 C 999
  ERC M 1 Application error information             
  FTX C 1 Free text                                 
  group 4 C 1
    RFF M 1 Reference                                 
    FTX C 9 Free text                                 
  endgroup 4
endgroup 3
UNT M 1 Message trailer                           

#====================================================================
USAGE
#====================================================================
![UNH]
UNH, Message header
A service segment starting and uniquely identifying a message.
The message type code for the Application error and
acknowledgement message is APERAK.

Note: Application error and acknowledgement messages conforming
to this document must contain the following data in segment
UNH, composite S009:
Data element  0065 APERAK
              0052 D
              0054 96A
              0051 UN
![BGM]
BGM, Beginning of message
A segment to indicate the type and function of the message and
to transmit the identifying number.
![DTM]
DTM, Date/time/period
A segment to specify related date/time.
![FTX]
FTX, Free text
A segment to specify free form or processable supplementary
information related to the whole message. In
computer-to-computer exchanges free form text will normally
require the receiver to process this segment manually.
![CNT]
CNT, Control total
A segment to provide message control totals.
![g1]
Segment group 1:  RFF-DTM
A group of segments to specify the document/message to which
the current message relates, and related date and time.
![RFF 1]
RFF, Reference
A segment to indicate the reference number of the
document/message.
![DTM 1]
DTM, Date/time/period
A segment to specify the date and time of the referenced
document/message.
![g2]
Segment group 2:  NAD-CTA-COM
A group of segments to specify the identifications of message
sender and message receiver with their contacts and
communication channels.
![NAD 2]
NAD, Name and address
A segment to specify the identification of the message
issuer and message receiver.
![CTA 2]
CTA, Contact information
A segment to specify a person or department inside the
party's organization, to which communication should be
directed.
![COM 2]
COM, Communication contact
A segment to indicate communication channel type and number
inside the party's organization, to which communication
should be directed.
![g3]
Segment group 3:  ERC-FTX-SG4
A group of segments to identify the application error(s) within
a specified received message and to give specific details
related to the error type or to precise the type of
acknowledgement.
![ERC 3]
ERC, Application error information
A segment identifying the type of application error or
acknowledgement within the referenced message. 
In case of an error, the error code may specify the error in
detail (e.g. a measurement relating to a piece of equipment
is wrong) or as a rough indication (e.g. a measurement is
wrong).
![FTX 3]
FTX, Free text
A segment to provide explanation and/or supplementary
information related to the specified application error or
acknowledgement.
For example, the explanation may provide exact details
relating to a generic error code.
![g4]
Segment group 4:  RFF-FTX
A group of segments to specify the functional entity
reference (e.g. goods item level, equipment level) relating
to the specified error; further details can be added to
identify the error more precisely.
![RFF 4]
RFF, Reference
A segment to provide a reference relating to the
acknowledgement type or the specified error (e.g.
functional entity reference such as equipment level).
![FTX 4]
FTX, Free text
A segment to provide additional details relating to the
reference, e.g. the content of the wrong data (and its
exact place in the message).
![UNT]
UNT, Message trailer
A service segment ending a message, giving the total number of
segments in the message and the control reference number of the
message.
#====================================================================
