#====================================================================
#
#                              UN/EDIFACT
#
#                UNITED NATIONS STANDARD MESSAGE (UNSM)
#
#                    Extended payment order message
#
#
#
#
#====================================================================
MESSAGE=PAYEXT
VERSION=D
RELEASE=01B
AGENCY=UN
COMMENT
ENDCOMMENT

#====================================================================
SEGMENTS segs
#====================================================================
UNH M 1 Message header                            
BGM M 1 Beginning of message                      
BUS C 1 Business function                         
PAI C 1 Payment instructions                      
FCA C 1 Financial charges allocation              
DTM M 4 Date/time/period                          
group 1 C 5
  RFF M 1 Reference                                 
  DTM C 1 Date/time/period                          
endgroup 1
group 2 M 1
  MOA M 1 Monetary amount                           
  CUX C 1 Currencies                                
  DTM C 2 Date/time/period                          
  RFF C 1 Reference                                 
endgroup 2
group 3 C 4
  FII M 1 Financial institution information         
  CTA C 1 Contact information                       
  COM C 5 Communication contact                     
endgroup 3
group 4 C 6
  NAD M 1 Name and address                          
  CTA C 1 Contact information                       
  COM C 5 Communication contact                     
endgroup 4
group 5 C 4
  INP M 1 Parties and instruction                   
  FTX C 1 Free text                                 
  DTM C 2 Date/time/period                          
endgroup 5
group 6 C 10
  GIS M 1 General indicator                         
  MOA C 1 Monetary amount                           
  LOC C 2 Place/location identification             
  NAD C 1 Name and address                          
  RCS C 1 Requirements and conditions               
  FTX C 10 Free text                                 
endgroup 6
group 7 C 1
  PRC M 1 Process identification                    
  FTX C 5 Free text                                 
  group 8 C 9999
    DOC M 1 Document/message details                  
    MOA C 5 Monetary amount                           
    DTM C 5 Date/time/period                          
    RFF C 5 Reference                                 
    NAD C 2 Name and address                          
    group 9 C 5
      CUX M 1 Currencies                                
      DTM C 1 Date/time/period                          
    endgroup 9
    group 10 C 100
      AJT M 1 Adjustment details                        
      MOA C 1 Monetary amount                           
      RFF C 1 Reference                                 
      FTX C 5 Free text                                 
    endgroup 10
    group 11 C 9999
      DLI M 1 Document line identification              
      MOA C 5 Monetary amount                           
      PIA C 5 Additional product id                     
      DTM C 5 Date/time/period                          
      group 12 C 5
        CUX M 1 Currencies                                
        DTM C 1 Date/time/period                          
      endgroup 12
      group 13 C 10
        AJT M 1 Adjustment details                        
        MOA C 1 Monetary amount                           
        RFF C 1 Reference                                 
        FTX C 5 Free text                                 
      endgroup 13
    endgroup 11
  endgroup 8
  group 14 C 1
    GIS M 1 General indicator                         
    MOA C 5 Monetary amount                           
  endgroup 14
endgroup 7
group 15 C 5
  AUT M 1 Authentication result                     
  DTM C 1 Date/time/period                          
endgroup 15
UNT M 1 Message trailer                           

#====================================================================
USAGE
#====================================================================
![UNH]
UNH, Message header
A service segment starting and uniquely identifying a message.
The message type code for the Extended payment order message is
PAYEXT.
Note: Extended payment order messages conforming to this
document must contain the following data in segment UNH,
composite S009:
Data element  0065 PAYEXT
              0052 D
              0054 01B
              0051 UN
![BGM]
BGM, Beginning of message
A segment for unique identification of the Extended Payment
Order, the type of Extended Payment Order and its function. The
requirement for a response, e.g., related debit advice, may be
indicated.

Note: The identification will be passed back to the Ordering
Customer for reconciliation purposes; it would be used in the
case of a cancellation or confirmation of an Extended Payment
Order.
![BUS]
BUS, Business function
A segment identifying certain characteristics of the Extended
Payment Order, such as its business function. In so doing, it
provides information about the message that may be used to
route the message within an institution, for tariffing, or for
the provision of some statistical information.
![PAI]
PAI, Payment instructions
A segment specifying the conditions, guarantee, method and
channel of payment for the Extended Payment Order.
![FCA]
FCA, Financial charges allocation
A segment specifying the method for allocation of charges and
allowances (e.g., charges borne by the Ordering Customer, the
Beneficiary or both), and identifying the Ordering Customers's
account to which such charges or allowances should be directed
where it is different from the principal account.
![DTM]
DTM, Date/time/period
A segment specifying the date and, when required, the time at
which the message has been created as well as other dates and
times relevant to the financial transaction.
![g1]
Segment group 1:  RFF-DTM
A group of segments identifying a previously-sent message.
![RFF 1]
RFF, Reference
A segment identifying a previously-sent message.
![DTM 1]
DTM, Date/time/period
A segment identifying the date/time of the previously-sent
message.
![g2]
Segment group 2:  MOA-CUX-DTM-RFF
A group of segments identifying the monetary amount and, if
necessary, the currencies, exchange rate and date for that
payment.
![MOA 2]
MOA, Monetary amount
A segment giving the amount value of the payment.
![CUX 2]
CUX, Currencies
A segment identifying the reference currency and the target
currency of the transaction when they are different. The
rate of exchange is solely used when previously agreed
between the Ordering Customer and the Ordered Bank.
![DTM 2]
DTM, Date/time/period
A segment identifying the effective date and/or time the
rate of exchange was fixed.
![RFF 2]
RFF, Reference
A segment identifying other transactions to which funds
associated with the Extended Payment Order are related, such
as a separate foreign exchange deal.
![g3]
Segment group 3:  FII-CTA-COM
A group of segments providing information about the financial
institutions and accounts related to the Extended Payment
Order, together with details of any parties to be contacted in
relation to the transaction.
![FII 3]
FII, Financial institution information
A segment identifying the financial institution (e.g., bank)
and relevant account number and currency for each party
involved in the transaction. The Ordering Customer may
indicate the previously agreed choice of financial
institution for payment.
![CTA 3]
CTA, Contact information
A segment identifying a person or a department for the
financial institution specified in the FII segment and to
whom communication should be directed.
![COM 3]
COM, Communication contact
A segment providing a communication number for the party
identified in the FII segment and optionally for the contact
identified in the associated CTA segment.
![g4]
Segment group 4:  NAD-CTA-COM
A group of segments identifying the name and address of the
non-financial institutions involved in the transaction and
their contacts.
![NAD 4]
NAD, Name and address
A segment identifying the name and address of the non-
financial institutions associated with the Extended Payment
Order and their functions.
![CTA 4]
CTA, Contact information
A segment identifying a person or a department for the party
specified in the NAD segment and to whom communication
should be directed.
![COM 4]
COM, Communication contact
A segment providing a communication number for the party
identified in the NAD segment and optionally for the contact
identified in the associated CTA segment.
![g5]
Segment group 5:  INP-FTX-DTM
A group of segments containing instructions from the Ordering
Customer relating to parties identified in the NAD and FII
segments. It specifies action to be taken by the identified
parties, and the date (and optionally time) by which such
action needs to be taken.
![INP 5]
INP, Parties and instruction
A segment identifying the party originating the instruction
and the parties to be contacted at or by the associated
financial institution on matters concerning the execution of
the payment. It specifies where appropriate the instruction
in coded form.
![FTX 5]
FTX, Free text
A segment providing free text instruction relating to the
associated INP segment.
![DTM 5]
DTM, Date/time/period
A segment specifying the earliest and the latest dates and
times by which the instruction specified in the INP and FTX
segments needs to be carried out.
![g6]
Segment group 6:  GIS-MOA-LOC-NAD-RCS-FTX
A group of segments providing information for subsequent use by
regulatory authorities requiring statistical and other types of
data. It also identifies the regulatory authority for which the
information is intended followed by the information itself.
![GIS 6]
GIS, General indicator
A segment identifying what processing should be completed
for regulatory authorities.
![MOA 6]
MOA, Monetary amount
A segment giving the amount and the currency of each
transaction to be reported.
![LOC 6]
LOC, Place/location identification
A segment giving the different origins/destinations (places)
of goods/investment/services.
![NAD 6]
NAD, Name and address
A segment identifying the recipient of the associated
informative text.
![RCS 6]
RCS, Requirements and conditions
A segment giving the nature (e.g. goods, transport services)
and direction of each transaction to be recorded in coded
form.
![FTX 6]
FTX, Free text
A segment giving information, in coded or clear form, to
provide information relevant to regulatory authorities
requirements.
![g7]
Segment group 7:  PRC-FTX-SG8-SG14
A group of segments giving information in free or in coded form
about the purpose of the payment from the ordering customer to
the beneficiary.
![PRC 7]
PRC, Process identification
A segment identifying the kind of process at the
beneficiary's side.
![FTX 7]
FTX, Free text
A segment in clear form to provide information from the
ordering customer to the beneficiary.
![g8]
Segment group 8:  DOC-MOA-DTM-RFF-NAD-SG9-SG10-SG11
A group of segments providing details of all documents,
e.g., invoices, statements, despatch advices, etc..., to
which the Extended Payment Order refers. It includes
information on the monetary amounts for each document and on
any adjustments (with an indication of the reason for
adjustments) and discounts. For information purposes an
indication of the tax element can be provided.
![DOC 8]
DOC, Document/message details
A segment identifying the reference document against
which payment is being made.
![MOA 8]
MOA, Monetary amount
A segment giving the monetary amounts of each reference
document (e.g., original amount, discount amount etc...).
The amount due and the amount remitted are mandatory.
![DTM 8]
DTM, Date/time/period
A segment specifying the date of the referenced document
and indicating any other relevant dates applicable.
![RFF 8]
RFF, Reference
A segment for the inclusion of any additional references
related to the reference document.
![NAD 8]
NAD, Name and address
A segment identifying a party name and address, either by
coded identification or in a clear form.
![g9]
Segment group 9:  CUX-DTM
A group of segments specifying the currencies and the
related dates/periods valid to the referenced document
where different to the reference currency.
![CUX 9]
CUX, Currencies
A segment identifying the currency and associated
exchange rate of the referenced document where
different to the remittance currency.
![DTM 9]
DTM, Date/time/period
A segment specifying the date/time/period related to
the rate of exchange.
![g10]
Segment group 10: AJT-MOA-RFF-FTX
A group of segments indicating adjustment amounts and
their referenced documents.
![AJT 10]
AJT, Adjustment details
A segment indicating any adjustments to the amounts
originally specified in the referenced document, and
to which items such adjustments apply, with the
associated reason for adjustment.
![MOA 10]
MOA, Monetary amount
A segment giving the monetary amounts of the
adjustments of each reference document (e.g. original
amounts, discount amount, etc.). The currency data
elements should not be used.
![RFF 10]
RFF, Reference
A segment for the inclusion of any additional
references related to the reference document.
![FTX 10]
FTX, Free text
A segment providing free text information related to
the payment details.
![g11]
Segment group 11: DLI-MOA-PIA-DTM-SG12-SG13
A group of segments which may be used when required to
provide details of individual line items in the reference
document.
![DLI 11]
DLI, Document line identification
A segment identifying a specific line item within the
referenced document.
![MOA 11]
MOA, Monetary amount
A segment giving the monetary amounts for this line
item.
![PIA 11]
PIA, Additional product id
A segment specifying item identification codes where
required.
![DTM 11]
DTM, Date/time/period
A segment specifying the date/time/period related to
the line item.
![g12]
Segment group 12: CUX-DTM
A group of segments identifying the currency and the
related dates/periods of the line item where different
to the remittance and document currency.
![CUX 12]
CUX, Currencies
A segment identifying the currency and associated
exchange rate of the line item, where different to
the reference and target currency.
![DTM 12]
DTM, Date/time/period
A segment specifying the effective date and/or time
the rate of exchange was fixed.
![g13]
Segment group 13: AJT-MOA-RFF-FTX
A group of segments indicating adjustment amounts and
their referenced documents for this line item.
![AJT 13]
AJT, Adjustment details
A segment indicating any adjustments to the amounts
originally specified for this line item and the
reason for the adjustments.
![MOA 13]
MOA, Monetary amount
A segment giving the monetary amounts of the
adjustment for this line item.
![RFF 13]
RFF, Reference
A segment for the inclusion of any additional
references related to the reference document.
![FTX 13]
FTX, Free text
A segment providing free text information related
to the payment details.
![g14]
Segment group 14: GIS-MOA
A group of segments indicating the end of the details of
payment and specifying hash total amounts for control
purposes.
![GIS 14]
GIS, General indicator
A segment specifying the end of the detail of payment.
![MOA 14]
MOA, Monetary amount
A segment indicating total amounts for control purposes.
![g15]
Segment group 15: AUT-DTM
A group of segments specifying the details of authentication.
![AUT 15]
AUT, Authentication result
A segment specifying the details of any authentication
(validation) procedure applied to the Extended Payment Order
message.
![DTM 15]
DTM, Date/time/period
A segment identifying the date and, where necessary, the
time of validation.
![UNT]
UNT, Message trailer
A service segment ending a message, giving the total number of
segments in the message (including the UNH & UNT) and the
control reference number of the message.
#====================================================================
