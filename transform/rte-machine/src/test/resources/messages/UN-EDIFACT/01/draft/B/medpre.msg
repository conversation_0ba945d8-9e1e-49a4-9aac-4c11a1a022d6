#====================================================================
#
#                              UN/EDIFACT
#
#                UNITED NATIONS STANDARD MESSAGE (UNSM)
#
#                     Medical prescription message
#
#
#
#
#====================================================================
MESSAGE=MEDPRE
VERSION=D
RELEASE=01B
AGENCY=UN
COMMENT
ENDCOMMENT

#====================================================================
SEGMENTS segs
#====================================================================
UNH M 1 Message header                            
BGM M 1 Beginning of message                      
DTM C 9 Date/time/period                          
FTX C 9 Free text                                 
group 1 M 99
  SEQ M 1 Sequence details                          
  PNA C 9 Party identification                      
  IDE C 9 Identity                                  
  SPR C 9 Organisation classification details       
  QUA C 9 Qualification                             
  EMP C 1 Employment details                        
  ADR C 1 Address                                   
  COM C 9 Communication contact                     
  RFF C 1 Reference                                 
  group 2 C 9
    DOC M 1 Document/message details                  
    COM C 1 Communication contact                     
  endgroup 2
endgroup 1
group 3 C 1
  ATT M 1 Attribute                                 
  PNA C 9 Party identification                      
  PDI C 1 Person demographic information            
  IDE C 9 Identity                                  
  DTM C 2 Date/time/period                          
  NAT C 1 Nationality                               
  AGR C 9 Agreement identification                  
  CCI C 2 Characteristic/class id                   
  STS C 1 Status                                    
  FTX C 9 Free text                                 
  LAN C 1 Language                                  
  CAV C 1 Characteristic value                      
  group 4 C 9
    HAN M 1 Handling instructions                     
    FTX C 9 Free text                                 
    DTM C 2 Date/time/period                          
  endgroup 4
  group 5 C 9
    ADR M 1 Address                                   
    COM C 9 Communication contact                     
  endgroup 5
  group 6 C 99
    REL M 1 Relationship                              
    PNA M 9 Party identification                      
    PDI C 1 Person demographic information            
    ADR C 1 Address                                   
    COM C 9 Communication contact                     
    LAN C 1 Language                                  
    CAV C 1 Characteristic value                      
  endgroup 6
  group 7 C 999
    LIN M 1 Line item                                 
    CIN C 9 Clinical information                      
    DTM C 9 Date/time/period                          
    PNA C 9 Party identification                      
    LAN C 1 Language                                  
    FTX C 99 Free text                                 
    RSL C 1 Result                                    
    CLI C 9 Clinical intervention                     
    CCI C 9 Characteristic/class id                   
    group 8 C 1
      IMD M 1 Item description                          
      STS M 1 Status                                    
      DSG C 9 Dosage administration                     
      INP C 9 Parties and instruction                   
      FTX C 9 Free text                                 
      SCC C 9 Scheduling conditions                     
      QTY C 9 Quantity                                  
    endgroup 8
  endgroup 7
endgroup 3
group 9 M 1
  GIS M 1 General indicator                         
  IDE M 9 Identity                                  
  DTM M 9 Date/time/period                          
  PTY C 1 Priority                                  
  AGR C 1 Agreement identification                  
  LAN C 1 Language                                  
  STS C 1 Status                                    
  FTX C 99 Free text                                 
  group 10 C 9
    DOC M 1 Document/message details                  
    IDE C 1 Identity                                  
    DTM C 1 Date/time/period                          
  endgroup 10
  group 11 C 1
    TOD M 1 Terms of delivery or transport            
    TDT C 1 Details of transport                      
    FTX C 9 Free text                                 
    DTM C 9 Date/time/period                          
    ADR C 1 Address                                   
    COM C 9 Communication contact                     
    PNA C 9 Party identification                      
    PTY C 1 Priority                                  
    PAC C 1 Package                                   
  endgroup 11
  group 12 C 9
    FCA M 1 Financial charges allocation              
    PNA C 9 Party identification                      
    IDE C 9 Identity                                  
    RCS C 9 Requirements and conditions               
    group 13 C 9
      ICD M 1 Insurance cover description               
      ALC C 9 Allowance or charge                       
      PCD C 1 Percentage details                        
      MOA C 9 Monetary amount                           
      IDE C 9 Identity                                  
      DTM C 9 Date/time/period                          
    endgroup 13
  endgroup 12
  group 14 C 99
    LIN M 1 Line item                                 
    DSG C 9 Dosage administration                     
    IMD M 1 Item description                          
    QTY C 9 Quantity                                  
    PGI C 9 Product group information                 
    PNA C 9 Party identification                      
    PAC C 1 Package                                   
    IDE C 9 Identity                                  
    DLM C 9 Delivery limitations                      
    EQN C 1 Number of units                           
    PRC C 9 Process identification                    
    DTM C 9 Date/time/period                          
    STS C 1 Status                                    
    CIN C 99 Clinical information                      
    FTX C 99 Free text                                 
    group 15 C 99
      CAV M 1 Characteristic value                      
      IMD M 1 Item description                          
      QTY C 9 Quantity                                  
      FTX C 99 Free text                                 
    endgroup 15
    group 16 C 99
      SEQ M 1 Sequence details                          
      DSG C 9 Dosage administration                     
      QTY C 9 Quantity                                  
      INP C 9 Parties and instruction                   
      DTM C 9 Date/time/period                          
      FTX C 9 Free text                                 
      SCC C 9 Scheduling conditions                     
      CIN C 9 Clinical information                      
      PCI C 9 Package identification                    
      LAN C 1 Language                                  
      EQA C 9 Attached equipment                        
    endgroup 16
    group 17 C 9
      FCA M 1 Financial charges allocation              
      PNA C 9 Party identification                      
      IDE C 9 Identity                                  
      RCS C 9 Requirements and conditions               
      group 18 C 9
        ICD M 1 Insurance cover description               
        ALC C 9 Allowance or charge                       
        PCD C 1 Percentage details                        
        MOA C 9 Monetary amount                           
        IDE C 9 Identity                                  
        DTM C 9 Date/time/period                          
      endgroup 18
    endgroup 17
  endgroup 14
endgroup 9
CNT C 1 Control total                             
UNT M 1 Message trailer                           

#====================================================================
USAGE
#====================================================================
![UNH]
UNH, Message header
A service segment starting and uniquely identifying a message.
The message type code for the Medical prescription message is
MEDPRE.
Note: Medical prescription messages conforming to this document
must contain the following data in segment UNH, composite S009:
Data element  0065 MEDPRE
              0052 D
              0054 01B
              0051 UN
![BGM]
BGM, Beginning of message
To indicate the type and function of a message and to transmit
the identification of the message.
![DTM]
DTM, Date/time/period
To specify date and/or time relating to the message, such as
the date and time the message is generated.
![FTX]
FTX, Free text
A segment with free text information, in coded or clear form,
to provide information related to the message.
![g1]
Segment group 1:  SEQ-PNA-IDE-SPR-QUA-EMP-ADR-COM-RFF-SG2
To represent information about involved healthcare parties.
![SEQ 1]
SEQ, Sequence details
To allocate a unique reference number to an individual party
allowing each party to be referenced from other locations
within the message.
![PNA 1]
PNA, Party identification
To specify information necessary to establish the identity
of a healthcare party.

One occurrence of the segment is used to specify the
identity details at one level. If both organisation,
department and/or physician are to be specified then a
separate occurrence of this segment has to be used for each
of them.
![IDE 1]
IDE, Identity
To specify alternative identification numbers of a
healthcare party.
![SPR 1]
SPR, Organisation classification details
To specify the medical speciality and type of a healthcare
organisation.
![QUA 1]
QUA, Qualification
To specify the qualification of a healthcare professional.
![EMP 1]
EMP, Employment details
A segment to specify the type, medical speciality and the
position or military rank of a healthcare professional.
![ADR 1]
ADR, Address
To specify an address of a healthcare party.
![COM 1]
COM, Communication contact
To specify a communication number of a party.
![RFF 1]
RFF, Reference
A segment to specify a link to a higher level healthcare
organisation which the actual healthcare organisation or
healthcare professional is a part of.

This segment is only used if multiple occurrences of this
segment group is needed to express the information of a
healthcare party.
![g2]
Segment group 2:  DOC-COM
To specify a document or message to be returned to the
actual party as a response to this message.
![DOC 2]
DOC, Document/message details
To specify the type of the requested document or message.
![COM 2]
COM, Communication contact
To identify the requested communication method.
![g3]
Segment group 3:  ATT-PNA-PDI-IDE-DTM-NAT-AGR-CCI-STS-FTX-LAN-
                  CAV-SG4-SG5-SG6-SG7
A group of segments to describe the subject of care.
![ATT 3]
ATT, Attribute
To specify the type of subject of care such as a person,
group of persons, an animal or a group of animals.
![PNA 3]
PNA, Party identification
To specify the name and official ID of the person or the
identification of a group of persons, animal or animal
group.
![PDI 3]
PDI, Person demographic information
To specify the gender of the person or animal.
![IDE 3]
IDE, Identity
To specify an alternative identification numbers for the
subject of care.
![DTM 3]
DTM, Date/time/period
To specify a date and/or time related to the subject of care
such as date and time of birth or age.
![NAT 3]
NAT, Nationality
To specify the nationality of the person.
![AGR 3]
AGR, Agreement identification
To specify the basis of entitlement to health services and 
any confidentiality constraint that applies to the handling
of information about the subject of care.
![CCI 3]
CCI, Characteristic/class id
To specify the breed and species of an animal.
![STS 3]
STS, Status
To specify the authenticity of the person's identity by the
prescriber, such as person known by prescriber, person
unknown, etc.
![FTX 3]
FTX, Free text
A segment with free text information, in coded or clear
form, to provide information relating to the subject of
care, such as a confidentiality constraint concerning a
person or age of a group of animals.
![LAN 3]
LAN, Language
To specify the language of the person.
![CAV 3]
CAV, Characteristic value
To specify the person's ability in the language.
![g4]
Segment group 4:  HAN-FTX-DTM
A group of segments to specify any precaution advice
concerning the subject of care.
![HAN 4]
HAN, Handling instructions
To specify a precaution as a code.
![FTX 4]
FTX, Free text
A segment with free text information, in coded or clear
form, to specify a precaution.
![DTM 4]
DTM, Date/time/period
To specify an associated date for the precaution such as
start or end date and/or time.
![g5]
Segment group 5:  ADR-COM
A group of segments to specify an address and related
communication number(s) of the actual party.
![ADR 5]
ADR, Address
To specify an address of the subject of care.
![COM 5]
COM, Communication contact
To specify a communication number for the subject of care
at the specific address.
![g6]
Segment group 6:  REL-PNA-PDI-ADR-COM-LAN-CAV
A segment group to provide information about a related party
such as next-of-kin or animal carer.
![REL 6]
REL, Relationship
To specify the type of relationship between the subject
of care and the related party.
![PNA 6]
PNA, Party identification
To specify information necessary to establish the
identity of a related party.
![PDI 6]
PDI, Person demographic information
To specify the gender of the person.
![ADR 6]
ADR, Address
To specify an address of a patient related party.
![COM 6]
COM, Communication contact
To specify a communication number of a patient related
party.
![LAN 6]
LAN, Language
To specify a language of the person.
![CAV 6]
CAV, Characteristic value
To specify the person's ability in the language.
![g7]
Segment group 7:  LIN-CIN-DTM-PNA-LAN-FTX-RSL-CLI-CCI-SG8
This segment group contains clinical information about the
patient such as medical history, investigation results,
medication records or allergies.

Each occurrence of this segment group provides information
concerning a single clinical information item.
![LIN 7]
LIN, Line item
To allocate an identification number to the clinical
information item.
![CIN 7]
CIN, Clinical information
To specify the type of clinical information item and
possibly a medical diagnosis.
![DTM 7]
DTM, Date/time/period
To specify start and/or end date and/or time for the
clinical information item and/or date and/or time of the
origin of the clinical information item.
![PNA 7]
PNA, Party identification
To specify information necessary to establish the
identity of the party providing the clinical information
item.

One occurrence of the segment is used to specify the
identity details at one level. If both organisation,
department and/or physician are to be specified then a
separate occurrence of this segment has to be used for
each of them.
![LAN 7]
LAN, Language
To specify the language of the clinical information item.
![FTX 7]
FTX, Free text
A segment with free text information, in coded or clear
form, to specify an item of clinical information.
![RSL 7]
RSL, Result
To specify a clinical investigation result item, such as
the height or weight of the subject of care or the result
of a laboratory investigation.
![CLI 7]
CLI, Clinical intervention
To specify a clinical examination such as a height
measurement.

Multiple occurrences of this segment may be needed if a
multi-axial coding scheme is being used.
![CCI 7]
CCI, Characteristic/class id
To specify a characteristic of a laboratory
investigation.

Multiple occurrences of this segment may be needed if a
multi-axial coding scheme is being used.
![g8]
Segment group 8:  IMD-STS-DSG-INP-FTX-SCC-QTY
A group of segments to specify current or past medication
or known allergy.
![IMD 8]
IMD, Item description
A segment to identify a medicinal product item.
![STS 8]
STS, Status
To specify the medicinal treatment status such as
commenced, stopped or a patient allergy.
![DSG 8]
DSG, Dosage administration
To specify the form of presentation or the route of
administration of the medicinal product as well as
dosage administration or trigger for administration.
![INP 8]
INP, Parties and instruction
To specify an instruction for use.
![FTX 8]
FTX, Free text
A segment with free text information, in coded or
clear form, to provide comments to dosage
administration.
![SCC 8]
SCC, Scheduling conditions
To specify a dosage pattern as structured information.
![QTY 8]
QTY, Quantity
To specify a quantity concerning the medicine such as
a single dose or strength of tablet.
![g9]
Segment group 9:  GIS-IDE-DTM-PTY-AGR-LAN-STS-FTX-SG10-SG11-
                  SG12-SG14
A group of segments to specify a set of prescriptions issued
for a subject of care.
![GIS 9]
GIS, General indicator
To specify the service type of the prescription set such as
a new prescription, a modification of an earlier
prescription or cancellation of a prescription.
![IDE 9]
IDE, Identity
To identify the prescription set and the coding scheme used
for identification of the medicinal product(s).
![DTM 9]
DTM, Date/time/period
A segment to specify a date and/or time relevant for the
prescription set such as issue date of prescription set or
requested date and time of dispensing.
![PTY 9]
PTY, Priority
A segment to specify the requested priority for dispensing.
![AGR 9]
AGR, Agreement identification
To specify the agreed disposal of the prescribed medicinal
product(s) such as to be used by the client or for
distribution.
![LAN 9]
LAN, Language
A segment to specify the language of the prescription set.
![STS 9]
STS, Status
To specify the status of the prescription set such as
requested, prescribed or dispensed.
![FTX 9]
FTX, Free text
A segment with free text information, in coded or clear
form, to provide comments to the prescription set.
![g10]
Segment group 10: DOC-IDE-DTM
A group of segments to specify a reference to a related
message or document.
![DOC 10]
DOC, Document/message details
To specify the type of message or document being
referenced.
![IDE 10]
IDE, Identity
To identify a referenced message or document.
![DTM 10]
DTM, Date/time/period
To specify issue date and/or time for a referenced
message or document.
![g11]
Segment group 11: TOD-TDT-FTX-DTM-ADR-COM-PNA-PTY-PAC
A group of segments to specify where the dispensed medicine
is to be delivered or collected.
![TOD 11]
TOD, Terms of delivery or transport
To specify how the dispensed medicine(s) will be
delivered or collected.
![TDT 11]
TDT, Details of transport
A segment to specify the mode of transport for the
delivery of dispensed medicine(s).
![FTX 11]
FTX, Free text
A segment with free text information, in coded or clear
form, to specify the delivery of the dispensed
medicine(s).
![DTM 11]
DTM, Date/time/period
A segment to specify a date and/or time relevant for the
delivery of the dispensed medicine(s) such as date and
time of delivery.
![ADR 11]
ADR, Address
A segment to specify the address for delivery of the
dispensed medicine(s).
![COM 11]
COM, Communication contact
A segment to identify a telecommunication number for the
receiver at the dispensed medicine delivery point.
![PNA 11]
PNA, Party identification
A segment to identify the receiver of a dispensed
medicine.
![PTY 11]
PTY, Priority
A segment to specify the requested priority for the
delivery of the dispensed medicine(s).
![PAC 11]
PAC, Package
To specify if special packaging is required.
![g12]
Segment group 12: FCA-PNA-IDE-RCS-SG13
A group of segments to specify the party responsible in part
or in whole for the payment and the related payment
conditions for the dispensed medicine(s) in the actual
prescription set.
![FCA 12]
FCA, Financial charges allocation
To specify the responsibility for the settlement of
charges, i.e. whether the costs are to be paid by the
patient, by private insurance, etc.
![PNA 12]
PNA, Party identification
To specify information necessary to establish the
identity of the payer.

One occurrence of the segment is used to specify the
identity details at one level. Thus two occurrences of
this segment may be needed if both organisation and
department are being used.
![IDE 12]
IDE, Identity
To identify a payer.
![RCS 12]
RCS, Requirements and conditions
To specify a national requirement or condition.
![g13]
Segment group 13: ICD-ALC-PCD-MOA-IDE-DTM
A group of segments to specify a payment condition.
![ICD 13]
ICD, Insurance cover description
To specify insurance cover type and care coverage.
![ALC 13]
ALC, Allowance or charge
To specify a reason for reduced or no payment.
![PCD 13]
PCD, Percentage details
To specify percentage to be paid as the contribution
of the payer.
![MOA 13]
MOA, Monetary amount
To specify a monetary amount as the contribution of
the payer.
![IDE 13]
IDE, Identity
To specify a reference for the authorisation of this
service.
![DTM 13]
DTM, Date/time/period
A segment to specify a date and/or time relevant to
this payment condition such as date of authorisation.
![g14]
Segment group 14: LIN-DSG-IMD-QTY-PGI-PNA-PAC-IDE-DLM-EQN-
                  PRC-DTM-STS-CIN-FTX-SG15-SG16-SG17
A group of segments to specify the information about a
single prescription item.
![LIN 14]
LIN, Line item
A segment to specify a prescription item identification
and request for notification information related to
variation in delivery such as a substituted item or
delivery failure.
![DSG 14]
DSG, Dosage administration
A segment to specify the form of presentation of the
medicinal product.
![IMD 14]
IMD, Item description
A segment to identify a medicinal product item and the
type of the item.
![QTY 14]
QTY, Quantity
To specify a quantity concerning the medicine regime such
as number of units of supply or strength of tablet.
![PGI 14]
PGI, Product group information
A segment to specify the group in which the medicine
belongs such as legal category or price category.
![PNA 14]
PNA, Party identification
A segment to specify the supplier of the medicinal
product, the manufacturer of the medicinal product or the
initial prescriber of the treatment.
![PAC 14]
PAC, Package
A segment to specify the type of package and the number
of physical units.
![IDE 14]
IDE, Identity
To identify the package or to give an alternative
identification of the initial authoriser.
![DLM 14]
DLM, Delivery limitations
To specify limitations on dispensing such as allowed
substitution type if any.
![EQN 14]
EQN, Number of units
To specify the number of times dispensing to be repeated.
![PRC 14]
PRC, Process identification
A segment to specify information about the preparation of
an extemporaneously dispensed medicine.
![DTM 14]
DTM, Date/time/period
A segment to specify a date and/or time or period
concerning the prescribing of the dispensed medicine such
as dispensing interval or initial date of treatment.
![STS 14]
STS, Status
To specify the status of the dispensed medicine provided
such as requested, dispensed, etc.
![CIN 14]
CIN, Clinical information
To specify the indication for the prescribed item and/or
comments concerning the prescribed item expressed as a
clinical code.
![FTX 14]
FTX, Free text
A segment with free text information, in coded or clear
form, expressing the indication for the prescribed item,
package description, comments concerning the prescribed
item or other information about this prescription item.
![g15]
Segment group 15: CAV-IMD-QTY-FTX
A group of segments to specify information about a single
ingredient.
![CAV 15]
CAV, Characteristic value
A segment to specify the function of the ingredient
such as active or excipient ingredient.
![IMD 15]
IMD, Item description
A segment to identify the ingredient.
![QTY 15]
QTY, Quantity
A segment to specify the quantity for an ingredient.
![FTX 15]
FTX, Free text
A segment with free text information, in coded or
clear form, to provide comments about the ingredient.
![g16]
Segment group 16: SEQ-DSG-QTY-INP-DTM-FTX-SCC-CIN-PCI-
                  LAN-EQA
A group of segments to specify how the dispensed medicine
should be administered and used.
![SEQ 16]
SEQ, Sequence details
To allocate a sequential regimen phase number.
![DSG 16]
DSG, Dosage administration
To specify how the dispensed medicine is administered.

If this information is provided in a structured way
then this segment is used for route of administration
or trigger for administration.
![QTY 16]
QTY, Quantity
A segment to specify a quantity related to dosage of
the dispensed medicine such as the single dose or
maximum daily dose.
![INP 16]
INP, Parties and instruction
To instruct the dispenser that an exceptional dosage
regimen is intended.
![DTM 16]
DTM, Date/time/period
A segment to specify a date and/or time or period
concerning dosage of the dispensed medicine such as
duration of treatment or time of administration.
![FTX 16]
FTX, Free text
A segment with free text information, in coded or
clear form, to provide information relating to the
medicine dosage and administration.
![SCC 16]
SCC, Scheduling conditions
To specify a dosage pattern and dosage frequency as
structured information.
![CIN 16]
CIN, Clinical information
A segment to specify the indication for the use of the
dispensed medicine.
![PCI 16]
PCI, Package identification
To specify a cautionary or advisory label.
![LAN 16]
LAN, Language
A segment to specify the language of the instructions.
![EQA 16]
EQA, Attached equipment
To specify device or equipment to be used for the
administration of the dispensed medicine.
![g17]
Segment group 17: FCA-PNA-IDE-RCS-SG18
A group of segments to specify the party that is
responsible in part or in whole for the payment of the
actual prescription item and the related payment
conditions.
![FCA 17]
FCA, Financial charges allocation
To specify the responsibility for the settlement of
charges, i.e. whether the costs are to be paid by the
patient, by private insurance, etc.
![PNA 17]
PNA, Party identification
To specify information necessary to establish the
identity of the payer.

One occurrence of the segment is used to specify the
identity details at one level. Thus two occurrences of
this segment may be needed if both organisation and
department are being used.
![IDE 17]
IDE, Identity
To identify a payer.
![RCS 17]
RCS, Requirements and conditions
To specify a national requirement or condition.
![g18]
Segment group 18: ICD-ALC-PCD-MOA-IDE-DTM
A group of segments to specify a payment condition.
![ICD 18]
ICD, Insurance cover description
To specify the insurance cover type and care
coverage.
![ALC 18]
ALC, Allowance or charge
To specify a reason for reduced or no payment.
![PCD 18]
PCD, Percentage details
To specify percentage to be paid as the
contribution of the payer.
![MOA 18]
MOA, Monetary amount
To specify a monetary amount as the contribution of
the payer.
![IDE 18]
IDE, Identity
To specify a reference for the authorisation of
this service.
![DTM 18]
DTM, Date/time/period
A segment to specify a date and/or time relevant to
this payment condition such as date of
authorisation.
![CNT]
CNT, Control total
A segment to provide the number of prescription items in the
message.
![UNT]
UNT, Message trailer
A service segment ending a message, giving the total number of
segments in the message (including the UNH & UNT) and the
control reference number of the message.
#====================================================================
