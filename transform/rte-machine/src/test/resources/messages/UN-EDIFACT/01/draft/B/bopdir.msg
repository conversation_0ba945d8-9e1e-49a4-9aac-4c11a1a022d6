#====================================================================
#
#                              UN/EDIFACT
#
#                UNITED NATIONS STANDARD MESSAGE (UNSM)
#
#            Direct balance of payment declaration message
#
#
#
#
#====================================================================
MESSAGE=BOPDIR
VERSION=D
RELEASE=01B
AGENCY=UN
COMMENT
ENDCOMMENT

#====================================================================
SEGMENTS segs
#====================================================================
UNH M 1 Message header                            
BGM M 1 Beginning of message                      
DTM M 9 Date/time/period                          
group 1 C 9
  RFF M 1 Reference                                 
  DTM C 1 Date/time/period                          
endgroup 1
group 2 M 9
  NAD M 1 Name and address                          
  CTA C 1 Contact information                       
  COM C 9 Communication contact                     
  FTX C 99 Free text                                 
endgroup 2
group 3 C 999
  RFF M 1 Reference                                 
  ATT C 1 Attribute                                 
  FII C 1 Financial institution information         
  NAD C 99 Name and address                          
  MOA C 9 Monetary amount                           
  CUX C 1 Currencies                                
  group 4 M 9999
    RCS M 1 Requirements and conditions               
    FTX C 99 Free text                                 
    DTM C 9 Date/time/period                          
    FII C 1 Financial institution information         
    NAD C 9 Name and address                          
    SPR C 1 Organisation classification details       
    LOC C 9 Place/location identification             
    group 5 C 9
      RFF M 1 Reference                                 
      DTM C 1 Date/time/period                          
    endgroup 5
    group 6 M 9
      MOA M 1 Monetary amount                           
      CUX C 1 Currencies                                
    endgroup 6
    group 7 C 1
      GIR M 1 Related identification numbers            
      QTY C 1 Quantity                                  
      PRI C 1 Price details                             
    endgroup 7
  endgroup 4
endgroup 3
UNS M 1 Section control                           
group 8 C 99
  RFF M 1 Reference                                 
  group 9 M 99
    RCS M 1 Requirements and conditions               
    FTX C 1 Free text                                 
    CUX C 1 Currencies                                
    group 10 M 9999
      MOA M 1 Monetary amount                           
      NAD C 1 Name and address                          
      LOC C 1 Place/location identification             
      group 11 C 1
        GIR M 1 Related identification numbers            
        QTY C 1 Quantity                                  
        PRI C 1 Price details                             
      endgroup 11
    endgroup 10
  endgroup 9
endgroup 8
CNT C 9 Control total                             
UNT M 1 Message trailer                           

#====================================================================
USAGE
#====================================================================
![UNH]
UNH, Message header
A service segment starting and uniquely identifying a message.
The message type code for the Direct balance of payment
declaration message is BOPDIR.
Note: Direct balance of payment declaration messages conforming
to this document must contain the following data in segment
UNH, composite S009:
Data element  0065 BOPDIR
              0052 D
              0054 01B
              0051 UN
![BGM]
BGM, Beginning of message
A segment to indicate the type and function of the message and
to transmit its identifying number.
![DTM]
DTM, Date/time/period
A segment to specify the date and, when required, the time of
the creation of the message and optionally to specify other
process dates which apply to the whole message.
![g1]
Segment group 1:  RFF-DTM
A group of segments to specify references which apply to the
whole message and optionally to specify related dates.
![RFF 1]
RFF, Reference
A segment to specify a reference for the message.
![DTM 1]
DTM, Date/time/period
A segment to specify dates related to the reference.
![g2]
Segment group 2:  NAD-CTA-COM-FTX
A group of segments to identify the parties associated with the
message.
![NAD 2]
NAD, Name and address
A segment to identify the reporting party or to identify the
party on behalf of which the declaration is made when the
reporting party is a third party.
![CTA 2]
CTA, Contact information
A segment to identify a person or a department for the party
to whom communication should be directed.
![COM 2]
COM, Communication contact
A segment to specify a communication number for the party,
such as phone number, E-mail address, fax number or X400
address.
![FTX 2]
FTX, Free text
A segment to specify complementary information on the party.

Part 1: direct reporting of transactions via resident bank
accounts.

This part of the message specifies the requested information
for all the resident bank accounts and for all the accounts
held abroad with banks or non-banks and can also be used for
reporting about netting operations.
![g3]
Segment group 3:  RFF-ATT-FII-NAD-MOA-CUX-SG4
A group of segments to accommodate the details relevant to the
transactions performed via one account (or a group of accounts)
during the relevant period.
![RFF 3]
RFF, Reference
A segment identifying each declaration on an account (or
group of accounts).
![ATT 3]
ATT, Attribute
A segment identifying the type of the reported account.
![FII 3]
FII, Financial institution information
A segment identifying the resident's financial institution
involved or the non-resident financial institution where the
resident's external account is held.
![NAD 3]
NAD, Name and address
A segment to identify the name and address of related
parties such as the resident, the non-resident non-financial
institution where the resident's account is held (group or
third party company) or the co-holders of a shared account.
![MOA 3]
MOA, Monetary amount
A segment to specify the opening balance or the closing
balance of the account for the reported period.
![CUX 3]
CUX, Currencies
A segment to specify the currency of the reported account.
![g4]
Segment group 4:  RCS-FTX-DTM-FII-NAD-SPR-LOC-SG5-SG6-SG7
A group of segments to specify the details of the set of
transactions processed via the financial account.
![RCS 4]
RCS, Requirements and conditions
A segment to specify the reason for the transaction.
![FTX 4]
FTX, Free text
A segment to specify information in clear and free form
about the reason for the transaction.
![DTM 4]
DTM, Date/time/period
A segment to specify the date of an event related to the
transaction, such as processing date, contract date,
payment date or settlement date.
![FII 4]
FII, Financial institution information
A segment to specify the identity of the domestic
financial institution related to a specific flow of
amount related to the transaction.
![NAD 4]
NAD, Name and address
A segment to specify the identification of the
transaction counterpart.
![SPR 4]
SPR, Organisation classification details
A segment to specify the industrial sector of activity of
the transaction counterpart.
![LOC 4]
LOC, Place/location identification
A segment to specify countries related to the
transaction, such as the country of origin or destination
of goods, the direct investment country, the donation
acting country, the payment transaction country (creditor
or debtor) or the country in which the construction work
is done.
![g5]
Segment group 5:  RFF-DTM
A group of segments to specify references and reference
dates related to the transaction or to the loan.
![RFF 5]
RFF, Reference
A segment to specify the reference number of a
document related to the transaction.
![DTM 5]
DTM, Date/time/period
A segment to specify the date and time of the
reference of the document related to the transaction.
![g6]
Segment group 6:  MOA-CUX
A group of segments to specify the transaction amount and
any related currencies.
![MOA 6]
MOA, Monetary amount
A segment to specify the amount of the transaction and
the relevant currency.
![CUX 6]
CUX, Currencies
A segment to specify the reference currency and the
target currency of the transaction when they are
different.
![g7]
Segment group 7:  GIR-QTY-PRI
A group of segments to specify the details related to
transactions on financial securities.
![GIR 7]
GIR, Related identification numbers
A segment to identify the type of security (shares,
bonds, etc).
![QTY 7]
QTY, Quantity
A segment to specify the quantity of the security.
![PRI 7]
PRI, Price details
A segment to specify the face value of the security.
![UNS]
UNS, Section control
A service segment placed at the start of the detail section to
avoid collisions.

Part 2: direct reporting of transactions via accounts held with
non-residents, reporting of BOP-related surveys and of foreign
assets and liabilities.
![g8]
Segment group 8:  RFF-SG9
A group of segments to accommodate the details relevant either
for all types of reporting forms on positions (foreign assets
and liabilities) or for all types of BOP related surveys.
![RFF 8]
RFF, Reference
A segment to specify either the type of reporting form (e.g.
on trade credits) or the type of survey.
![g9]
Segment group 9:  RCS-FTX-CUX-SG10
A group of segments to specify relevant information either
for a type of reporting form on a position (foreign assets
and liabilities) or for a type of BOP related survey.
![RCS 9]
RCS, Requirements and conditions
A segment to specify the type of the account or the type
of reporting form.
![FTX 9]
FTX, Free text
A segment to specify free text information relating to
the type of information or report.
![CUX 9]
CUX, Currencies
A segment to specify the currency of the reported
account.
![g10]
Segment group 10: MOA-NAD-LOC-SG11
A group of segments to specify the amount and other
details relevant to the account position or to any
related Balance of Payment surveys.
![MOA 10]
MOA, Monetary amount
A segment specifying the monetary amount of the
position or flow that has been requested.
![NAD 10]
NAD, Name and address
A segment to specify the identification of a party
related to the transaction.
![LOC 10]
LOC, Place/location identification
A segment to specify the country of the debtor or
creditor.
![g11]
Segment group 11: GIR-QTY-PRI
A group of segments to specify the details related to
transactions on financial securities.
![GIR 11]
GIR, Related identification numbers
A segment to identify the type of security such as
shares or bonds.
![QTY 11]
QTY, Quantity
A segment to specify the quantity of the security.
![PRI 11]
PRI, Price details
A segment to specify the face value of the
security.
![CNT]
CNT, Control total
A segment to specify total values for control purposes.
![UNT]
UNT, Message trailer
A service segment ending a message, giving the total number of
segments in the message (including the UNH & UNT) and the
control reference number of the message.
#====================================================================
