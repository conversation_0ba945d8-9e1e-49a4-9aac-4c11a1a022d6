<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" elementFormDefault="qualified">
    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:complexType name="RangeType">
        <xsd:annotation>
            <xsd:documentation>identifies a range of values by a minimum and maximum
        value.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="MinimumValue" type="MinimumValueType">
                <xsd:annotation>
                    <xsd:documentation>holds the minimum value of the range and its inclusiveness.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="MaximumValue" type="MaximumValueType">
                <xsd:annotation>
                    <xsd:documentation>holds the maximum value of the range and its inclusiveness.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
