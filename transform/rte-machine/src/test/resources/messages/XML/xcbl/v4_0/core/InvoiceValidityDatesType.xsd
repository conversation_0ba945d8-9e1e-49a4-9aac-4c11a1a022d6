<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" elementFormDefault="qualified">
    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:complexType name="InvoiceValidityDatesType">
        <xsd:annotation>
            <xsd:documentation>is used to specify a start and end date of a period
        in which the element they are used is valid.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="StartDate" type="ComplexDateType">
                <xsd:annotation>
                    <xsd:documentation>specifies the first point in time on which validity/effectivity
        occurs.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="EndDate" type="ComplexDateType">
                <xsd:annotation>
                    <xsd:documentation>specifies the last point in time for which a validity/effectivity
        occurs.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
