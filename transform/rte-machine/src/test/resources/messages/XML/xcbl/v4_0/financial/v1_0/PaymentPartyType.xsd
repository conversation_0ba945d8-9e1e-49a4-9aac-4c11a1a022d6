<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd" xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" xmlns:dgs="http://www.w3.org/2000/09/xmldsig#" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd" elementFormDefault="qualified">
<xsd:import namespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" schemaLocation="../../core/core.xsd"/>


    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:complexType name="PaymentPartyType">
        <xsd:annotation>
            <xsd:documentation>contains all parties relevant to the payment document. The
        required parties are the <!--code-->PayerParty<!--/code--> and the <!--code-->PayeeParty<!--/code-->. The <!--code-->PayerParty<!--/code--> includes
        an optional <!--code-->CertificateAuthority<!--/code--> field to specify a digital
        signature.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="PayerParty" type="PayerPartyType">
                <xsd:annotation>
                    <xsd:documentation>describes the partner that is responsible for payment. This is
        assumed to also be the sender of the document. For <!--code-->PayerParty<!--/code-->, the <!--code-->Party<!--/code-->
        definition is extended to include <!--code-->CertificateAuthority<!--/code-->. </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="PayeeParty" type="core:PartyType">
                <xsd:annotation>
                    <xsd:documentation>describes the party to receive payment.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="BuyerParty" type="core:PartyType">
                <xsd:annotation>
                    <xsd:documentation>describes the buyer of the goods or services.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="SellerParty" type="core:PartyType">
                <xsd:annotation>
                    <xsd:documentation>describes the supplier of the goods or services.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="BillToParty" type="core:PartyType">
                <xsd:annotation>
                    <xsd:documentation>describes the party to receive the invoice.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ListOfPartyCoded" type="core:ListOfPartyCodedType">
                <xsd:annotation>
                    <xsd:documentation>contains information relevant to describing parties other than
        the source target parties which are relevant to the time series data.
        </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
