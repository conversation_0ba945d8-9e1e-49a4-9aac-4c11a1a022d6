<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSPY v5 rel. 3 U (http://www.xmlspy.com) by <PERSON><PERSON><PERSON> (Gencod EAN France) -->
<xsd:schema targetNamespace="urn:ean.ucc:2" xmlns:eanucc="urn:ean.ucc:2" xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="unqualified" attributeFormDefault="unqualified" version="2.5">
	<xsd:annotation>
		<xsd:documentation>
            
            ---------------------------
            © Copyright GS1, 2008

            GS1 is providing this XML Schema Definition file and resultant XML file as a service to interested industries.
            This XML Schema Definition file and resultant XML file were developed through a consensus process of interested parties.

            Although efforts have been made to ensure that the XML Schema Definition file and resultant XML file are correct, reliable, and technically
            accurate,  GS1 makes NO WARRANTY, EXPRESS OR IMPLIED, THAT THIS XML Schema Definition file and resultant XML file ARE
            CORRECT, WILL NOT REQUIRE MODIFICATION AS EXPERIENCE AND TECHNOLOGICAL ADVANCES DICTATE, OR WILL BE SUITABLE FOR
            ANY PURPOSE OR WORKABLE IN ANY APPLICATION, OR OTHERWISE.  Use of the XML Schema Definition file and resultant XML
            file are with the understanding that GS1 has no liability for any claim to the contrary, or for any damage or loss of any kind or nature.

            Version Information:
            Version Number: 2.5
            Date of creation: December 2008

            The schema and subsequent updates will be provided on the GS1 websites.
            ---------------------------
                
            </xsd:documentation>
	</xsd:annotation>
	<xsd:complexType name="Description2500Type">
		<xsd:sequence>
			<xsd:element name="language" type="eanucc:ISO639_CodeType"/>
			<xsd:element name="text">
				<xsd:simpleType>
					<xsd:restriction base="xsd:string">
						<xsd:maxLength value="2500"/>
						<xsd:minLength value="1"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="Description5000Type">
		<xsd:sequence>
			<xsd:element name="language" type="eanucc:ISO639_CodeType"/>
			<xsd:element name="text">
				<xsd:simpleType>
					<xsd:restriction base="xsd:string">
						<xsd:maxLength value="5000"/>
						<xsd:minLength value="1"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="DescriptionType">
		<xsd:sequence>
			<xsd:element name="text">
				<xsd:simpleType>
					<xsd:restriction base="xsd:string">
						<xsd:maxLength value="70"/>
						<xsd:minLength value="1"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="ISO639_CodeType">
		<xsd:sequence>
			<xsd:element name="languageISOCode">
				<xsd:simpleType>
					<xsd:restriction base="xsd:string">
						<xsd:maxLength value="5"/>
						<xsd:minLength value="1"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="LongDescriptionType">
		<xsd:sequence>
			<xsd:element name="language" type="eanucc:ISO639_CodeType"/>
			<xsd:element name="longText">
				<xsd:simpleType>
					<xsd:restriction base="xsd:string">
						<xsd:maxLength value="1000"/>
						<xsd:minLength value="1"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="LongTextDescriptionType">
		<xsd:sequence>
			<xsd:element name="longDescription" type="eanucc:LongDescriptionType" maxOccurs="unbounded"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="MultiDescription2500Type">
		<xsd:sequence>
			<xsd:element name="description" type="eanucc:Description2500Type" maxOccurs="unbounded"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="MultiDescription5000Type">
		<xsd:sequence>
			<xsd:element name="description" type="eanucc:Description5000Type" maxOccurs="unbounded"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="MultiDescriptionType">
		<xsd:sequence>
			<xsd:element name="description" type="eanucc:DescriptionType" maxOccurs="unbounded"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="MultiLongDescriptionType">
		<xsd:sequence>
			<xsd:element name="description" type="eanucc:LongDescriptionType" maxOccurs="unbounded"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TextDescriptionType">
		<xsd:sequence>
			<xsd:element name="description" type="eanucc:DescriptionType" maxOccurs="unbounded"/>
		</xsd:sequence>
	</xsd:complexType>
</xsd:schema>
