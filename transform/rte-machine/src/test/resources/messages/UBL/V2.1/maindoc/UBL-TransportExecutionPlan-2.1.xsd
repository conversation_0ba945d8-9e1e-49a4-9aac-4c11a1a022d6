<?xml version="1.0" encoding="UTF-8"?>
<!--
  Library:           OASIS Universal Business Language (UBL) 2.1 OS
                     http://docs.oasis-open.org/ubl/os-UBL-2.1/
  Release Date:      04 November 2013
  Module:            xsd/maindoc/UBL-TransportExecutionPlan-2.1.xsd
  Generated on:      2013-10-31 17:17z
  Copyright (c) OASIS Open 2013. All Rights Reserved.
-->
<xsd:schema xmlns="urn:oasis:names:specification:ubl:schema:xsd:TransportExecutionPlan-2"
            xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
            xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
            xmlns:ext="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2"
            xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            xmlns:ccts="urn:un:unece:uncefact:documentation:2"
            targetNamespace="urn:oasis:names:specification:ubl:schema:xsd:TransportExecutionPlan-2"
            elementFormDefault="qualified"
            attributeFormDefault="unqualified"
            version="2.1">
   <!-- ===== Imports ===== -->
   <xsd:import namespace="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
               schemaLocation="../common/UBL-CommonAggregateComponents-2.1.xsd"/>
   <xsd:import namespace="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
               schemaLocation="../common/UBL-CommonBasicComponents-2.1.xsd"/>
   <xsd:import namespace="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2"
               schemaLocation="../common/UBL-CommonExtensionComponents-2.1.xsd"/>
   <!-- ===== Element Declarations ===== -->
   <xsd:element name="TransportExecutionPlan" type="TransportExecutionPlanType">
      <xsd:annotation>
         <xsd:documentation>This element MUST be conveyed as the root element in any instance document based on this Schema expression</xsd:documentation>
      </xsd:annotation>
   </xsd:element>
   <!-- ===== Type Definitions ===== -->
   <!-- ===== Aggregate Business Information Entity Type Definitions ===== -->
   <xsd:complexType name="TransportExecutionPlanType">
      <xsd:annotation>
         <xsd:documentation>
            <ccts:Component>
               <ccts:ComponentType>ABIE</ccts:ComponentType>
               <ccts:DictionaryEntryName>Transport Execution Plan. Details</ccts:DictionaryEntryName>
               <ccts:Definition>A document used in the negotiation of a transport service between a transport user and a transport service provider.</ccts:Definition>
               <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
            </ccts:Component>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:sequence>
           <xsd:element ref="ext:UBLExtensions" minOccurs="0" maxOccurs="1">
              <xsd:annotation>
                 <xsd:documentation>A container for all extensions present in the document.</xsd:documentation>
              </xsd:annotation>
           </xsd:element>
          <xsd:element ref="cbc:UBLVersionID" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. UBL Version Identifier. Identifier</ccts:DictionaryEntryName>
                     <ccts:Definition>Identifies the earliest version of the UBL 2 schema for this document type that defines all of the elements that might be encountered in the current instance.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTerm>UBL Version Identifier</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
                     <ccts:DataType>Identifier. Type</ccts:DataType>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:CustomizationID" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Customization Identifier. Identifier</ccts:DictionaryEntryName>
                     <ccts:Definition>Identifies a user-defined customization of UBL for a specific use.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTerm>Customization Identifier</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
                     <ccts:DataType>Identifier. Type</ccts:DataType>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:ProfileID" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Profile Identifier. Identifier</ccts:DictionaryEntryName>
                     <ccts:Definition>Identifies a user-defined profile of the customization of UBL being used.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTerm>Profile Identifier</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
                     <ccts:DataType>Identifier. Type</ccts:DataType>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:ProfileExecutionID" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Profile Execution Identifier. Identifier</ccts:DictionaryEntryName>
                     <ccts:Definition>Identifies an instance of executing a profile, to associate all transactions in a collaboration.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTerm>Profile Execution Identifier</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
                     <ccts:DataType>Identifier. Type</ccts:DataType>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:ID" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Identifier</ccts:DictionaryEntryName>
                     <ccts:Definition>An identifier for this document, assigned by the sender.</ccts:Definition>
                     <ccts:Cardinality>1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTerm>Identifier</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
                     <ccts:DataType>Identifier. Type</ccts:DataType>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:VersionID" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Version. Identifier</ccts:DictionaryEntryName>
                     <ccts:Definition>Indicates the current version of the Transport Execution Plan.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTerm>Version</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
                     <ccts:DataType>Identifier. Type</ccts:DataType>
                     <ccts:Examples>1.1 </ccts:Examples>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:CopyIndicator" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Copy_ Indicator. Indicator</ccts:DictionaryEntryName>
                     <ccts:Definition>Indicates whether this document is a copy (true) or not (false).</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Copy</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Indicator</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Indicator</ccts:RepresentationTerm>
                     <ccts:DataType>Indicator. Type</ccts:DataType>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:UUID" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. UUID. Identifier</ccts:DictionaryEntryName>
                     <ccts:Definition>A universally unique identifier for an instance of this document.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTerm>UUID</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
                     <ccts:DataType>Identifier. Type</ccts:DataType>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:IssueDate" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Issue Date. Date</ccts:DictionaryEntryName>
                     <ccts:Definition>The date, assigned by the sender, on which this document was issued.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTerm>Issue Date</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Date</ccts:RepresentationTerm>
                     <ccts:DataType>Date. Type</ccts:DataType>
                     <ccts:AlternativeBusinessTerms>Transport Document Date</ccts:AlternativeBusinessTerms>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:IssueTime" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Issue Time. Time</ccts:DictionaryEntryName>
                     <ccts:Definition>The time, assigned by the sender, at which this document was issued.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTerm>Issue Time</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Time</ccts:RepresentationTerm>
                     <ccts:DataType>Time. Type</ccts:DataType>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:DocumentStatusCode" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Document Status Code. Code</ccts:DictionaryEntryName>
                     <ccts:Definition>A code signifying the status of the Transport Execution Plan (updated, cancelled, confirmed, etc.)</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTerm>Document Status Code</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Code</ccts:RepresentationTerm>
                     <ccts:DataTypeQualifier>Document Status</ccts:DataTypeQualifier>
                     <ccts:DataType>Document Status_ Code. Type</ccts:DataType>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:DocumentStatusReasonCode" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Document Status Reason Code. Code</ccts:DictionaryEntryName>
                     <ccts:Definition>A code signifying a reason associated with the status of a Transport Execution Plan.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTerm>Document Status Reason Code</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Code</ccts:RepresentationTerm>
                     <ccts:DataType>Code. Type</ccts:DataType>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:DocumentStatusReasonDescription"
                      minOccurs="0"
                      maxOccurs="unbounded">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Document Status Reason Description. Text</ccts:DictionaryEntryName>
                     <ccts:Definition>A reason for the status assigned to the Transport Execution Plan, expressed in text.</ccts:Definition>
                     <ccts:Cardinality>0..n</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTerm>Document Status Reason Description</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Text</ccts:RepresentationTerm>
                     <ccts:DataType>Text. Type</ccts:DataType>
                     <ccts:Examples>123 Standard Chartered Tower </ccts:Examples>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:Note" minOccurs="0" maxOccurs="unbounded">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Note. Text</ccts:DictionaryEntryName>
                     <ccts:Definition>Free-form text pertinent to this document, conveying information that is not contained explicitly in other structures.</ccts:Definition>
                     <ccts:Cardinality>0..n</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTerm>Note</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Text</ccts:RepresentationTerm>
                     <ccts:DataType>Text. Type</ccts:DataType>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:TransportUserRemarks" minOccurs="0" maxOccurs="unbounded">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Transport User_ Remarks. Text</ccts:DictionaryEntryName>
                     <ccts:Definition>Remarks from the transport user regarding the transport operations referred to in the Transport Execution Plan.</ccts:Definition>
                     <ccts:Cardinality>0..n</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Transport User</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Remarks</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Text</ccts:RepresentationTerm>
                     <ccts:DataType>Text. Type</ccts:DataType>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:TransportServiceProviderRemarks"
                      minOccurs="0"
                      maxOccurs="unbounded">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Transport Service Provider_ Remarks. Text</ccts:DictionaryEntryName>
                     <ccts:Definition>Remarks from the transport service provider regarding the transport operations referred to in the Transport Execution Plan.</ccts:Definition>
                     <ccts:Cardinality>0..n</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Transport Service Provider</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Remarks</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Text</ccts:RepresentationTerm>
                     <ccts:DataType>Text. Type</ccts:DataType>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:SenderParty" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Sender_ Party. Party</ccts:DictionaryEntryName>
                     <ccts:Definition>The party sending this document (can be either the transport user or the transport service provider).</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Sender</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Party</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Party</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Party</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:ReceiverParty" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Receiver_ Party. Party</ccts:DictionaryEntryName>
                     <ccts:Definition>The party receiving this document (can be either the transport user or the transport service provider).</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Receiver</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Party</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Party</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Party</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:TransportUserParty" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Transport User_ Party. Party</ccts:DictionaryEntryName>
                     <ccts:Definition>The party requesting the transport service from a transport service provider.</ccts:Definition>
                     <ccts:Cardinality>1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Transport User</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Party</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Party</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Party</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:TransportServiceProviderParty" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Transport Service Provider_ Party. Party</ccts:DictionaryEntryName>
                     <ccts:Definition>The party offering the transport service based upon a request from a transport user.</ccts:Definition>
                     <ccts:Cardinality>1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Transport Service Provider</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Party</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Party</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Party</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:BillToParty" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Bill To_ Party. Party</ccts:DictionaryEntryName>
                     <ccts:Definition>Describes the party that will pay for the transport service(s) provided in a Transport Execution Plan</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Bill To</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Party</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Party</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Party</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:Signature" minOccurs="0" maxOccurs="unbounded">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Signature</ccts:DictionaryEntryName>
                     <ccts:Definition>A signature applied to this document.</ccts:Definition>
                     <ccts:Cardinality>0..n</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTerm>Signature</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Signature</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Signature</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:TransportExecutionPlanRequestDocumentReference"
                      minOccurs="0"
                      maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Transport Execution Plan Request_ Document Reference. Document Reference</ccts:DictionaryEntryName>
                     <ccts:Definition>A reference to a Transport Execution Plan Request.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Transport Execution Plan Request</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Document Reference</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Document Reference</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Document Reference</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:TransportExecutionPlanDocumentReference"
                      minOccurs="0"
                      maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Transport Execution Plan_ Document Reference. Document Reference</ccts:DictionaryEntryName>
                     <ccts:Definition>A reference to an original Transport Execution Plan.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Transport Execution Plan</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Document Reference</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Document Reference</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Document Reference</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:TransportServiceDescriptionDocumentReference"
                      minOccurs="0"
                      maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Transport Service Description_ Document Reference. Document Reference</ccts:DictionaryEntryName>
                     <ccts:Definition>A reference to the Transport Service Description, which is used by a transport service provider to announce transport services to transport users (buyers).</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Transport Service Description</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Document Reference</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Document Reference</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Document Reference</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:AdditionalDocumentReference"
                      minOccurs="0"
                      maxOccurs="unbounded">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Additional_ Document Reference. Document Reference</ccts:DictionaryEntryName>
                     <ccts:Definition>A reference to an additional document associated with this document.</ccts:Definition>
                     <ccts:Cardinality>0..n</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Additional</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Document Reference</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Document Reference</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Document Reference</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:TransportContract" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Transport_ Contract. Contract</ccts:DictionaryEntryName>
                     <ccts:Definition>A contract related to the Transport Execution Plan.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Transport</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Contract</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Contract</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Contract</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:TransportServiceProviderResponseRequiredPeriod"
                      minOccurs="0"
                      maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Transport Service Provider Response Required_ Period. Period</ccts:DictionaryEntryName>
                     <ccts:Definition>Describes the deadline for when the Transport Service Provider will have to respond to a Transport Execution Plan .</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Transport Service Provider Response Required</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Period</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Period</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Period</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:TransportUserResponseRequiredPeriod"
                      minOccurs="0"
                      maxOccurs="unbounded">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Transport User Response Required_ Period. Period</ccts:DictionaryEntryName>
                     <ccts:Definition>Describes the deadline for when the Transport User will have to respond to a Transport Execution Plan suggested by a Transport Service Provider.</ccts:Definition>
                     <ccts:Cardinality>0..n</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Transport User Response Required</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Period</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Period</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Period</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:ValidityPeriod" minOccurs="0" maxOccurs="unbounded">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Validity_ Period. Period</ccts:DictionaryEntryName>
                     <ccts:Definition>A period during which the Transport Execution Plan is valid.</ccts:Definition>
                     <ccts:Cardinality>0..n</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Validity</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Period</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Period</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Period</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:MainTransportationService" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Main_ Transportation Service. Transportation Service</ccts:DictionaryEntryName>
                     <ccts:Definition>Description of the main transportation service referenced in the Transport Execution Plan.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Main</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Transportation Service</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Transportation Service</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Transportation Service</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:AdditionalTransportationService"
                      minOccurs="0"
                      maxOccurs="unbounded">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Additional_ Transportation Service. Transportation Service</ccts:DictionaryEntryName>
                     <ccts:Definition>A description of an additional transportation service referenced in the Transport Execution Plan.</ccts:Definition>
                     <ccts:Cardinality>0..n</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Additional</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Transportation Service</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Transportation Service</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Transportation Service</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:ServiceStartTimePeriod" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Service Start Time_ Period. Period</ccts:DictionaryEntryName>
                     <ccts:Definition>The period within which the service must begin.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Service Start Time</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Period</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Period</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Period</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:ServiceEndTimePeriod" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Service End Time_ Period. Period</ccts:DictionaryEntryName>
                     <ccts:Definition>The period during which the service must be completed.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Service End Time</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Period</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Period</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Period</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:FromLocation" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. From_ Location. Location</ccts:DictionaryEntryName>
                     <ccts:Definition>The location of origin of the transport service referenced in the Transport Execution Plan.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>From</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Location</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Location</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Location</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:ToLocation" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. To_ Location. Location</ccts:DictionaryEntryName>
                     <ccts:Definition>The destination location for the transport service referenced in the Transport Execution Plan.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>To</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Location</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Location</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Location</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:AtLocation" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. At_ Location. Location</ccts:DictionaryEntryName>
                     <ccts:Definition>The location of a transport service (e.g., terminal handling service) that does not require transport movement.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>At</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Location</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Location</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Location</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:TransportExecutionTerms" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Transport Execution Terms</ccts:DictionaryEntryName>
                     <ccts:Definition>A description of terms and conditions related to the Transport Execution Plan.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTerm>Transport Execution Terms</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Transport Execution Terms</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Transport Execution Terms</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:Consignment" minOccurs="1" maxOccurs="unbounded">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Transport Execution Plan. Consignment</ccts:DictionaryEntryName>
                     <ccts:Definition>A description of an identifiable collection of goods items to be transported between the consignor and the consignee. This information may be defined within a transport contract. A consignment may comprise more than one shipment (e.g., when consolidated by a freight forwarder).</ccts:Definition>
                     <ccts:Cardinality>1..n</ccts:Cardinality>
                     <ccts:ObjectClass>Transport Execution Plan</ccts:ObjectClass>
                     <ccts:PropertyTerm>Consignment</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Consignment</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Consignment</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
      </xsd:sequence>
   </xsd:complexType>
</xsd:schema>
<!-- ===== Copyright Notice ===== --><!--
  OASIS takes no position regarding the validity or scope of any 
  intellectual property or other rights that might be claimed to pertain 
  to the implementation or use of the technology described in this 
  document or the extent to which any license under such rights 
  might or might not be available; neither does it represent that it has 
  made any effort to identify any such rights. Information on OASIS's 
  procedures with respect to rights in OASIS specifications can be 
  found at the OASIS website. Copies of claims of rights made 
  available for publication and any assurances of licenses to be made 
  available, or the result of an attempt made to obtain a general 
  license or permission for the use of such proprietary rights by 
  implementors or users of this specification, can be obtained from 
  the OASIS Executive Director.

  OASIS invites any interested party to bring to its attention any 
  copyrights, patents or patent applications, or other proprietary 
  rights which may cover technology that may be required to 
  implement this specification. Please address the information to the 
  OASIS Executive Director.
  
  This document and translations of it may be copied and furnished to 
  others, and derivative works that comment on or otherwise explain 
  it or assist in its implementation may be prepared, copied, 
  published and distributed, in whole or in part, without restriction of 
  any kind, provided that the above copyright notice and this 
  paragraph are included on all such copies and derivative works. 
  However, this document itself may not be modified in any way, 
  such as by removing the copyright notice or references to OASIS, 
  except as needed for the purpose of developing OASIS 
  specifications, in which case the procedures for copyrights defined 
  in the OASIS Intellectual Property Rights document must be 
  followed, or as required to translate it into languages other than 
  English. 

  The limited permissions granted above are perpetual and will not be 
  revoked by OASIS or its successors or assigns. 

  This document and the information contained herein is provided on 
  an "AS IS" basis and OASIS DISCLAIMS ALL WARRANTIES, 
  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO ANY 
  WARRANTY THAT THE USE OF THE INFORMATION HEREIN 
  WILL NOT INFRINGE ANY RIGHTS OR ANY IMPLIED 
  WARRANTIES OF MERCHANTABILITY OR FITNESS FOR A 
  PARTICULAR PURPOSE.    
-->