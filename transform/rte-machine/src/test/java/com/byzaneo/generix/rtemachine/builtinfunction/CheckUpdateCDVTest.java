package com.byzaneo.generix.rtemachine.builtinfunction;

import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.Party.BILLTOPARTY;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.Party.BUYER;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.Party.BUYERAGENT;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.Party.FACTOR;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.Party.PAYER;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.Party.REMITTOPARTY;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.Party.SELLER;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.Party.SELLERAGENT;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.byzaneo.generix.rtemachine.RteRuntime;
import com.byzaneo.generix.rtemachine.exception.ExitFunctionException;
import com.byzaneo.generix.rtemachine.exception.RteException;
import com.byzaneo.generix.rtemachine.exception.RteInternalException;
import com.byzaneo.xtrade.api.DocumentStage;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Properties;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class CheckUpdateCDVTest {

  private RteRuntime runtime;

  @BeforeEach
  void before() {
    runtime = new RteRuntime(new Properties());
  }

  @Test
  void testheckUpdateCDVAcceptedCase() {
    Properties properties = new Properties();
    runtime = new RteRuntime(properties);
    try {
      String statusCode = (String) new CheckUpdateCDVFunction().call(runtime,
          Arrays.asList("RECEIVED", "PAYMENT_SENT"), 0);
      assertEquals("ACCEPTED", statusCode);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  @Test
  void testheckUpdateCDVRejectedCase() {
    Properties properties = new Properties();
    runtime = new RteRuntime(properties);
    try {
      String statusCode = (String) new CheckUpdateCDVFunction().call(runtime,
          Arrays.asList("RECEIVED", "UPLOADED"), 0);
      assertEquals("REJECTED", statusCode);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  @Test
  void testheckUpdateCDVIgnoredCase() {
    Properties properties = new Properties();
    runtime = new RteRuntime(properties);
    try {
      String statusCode = (String) new CheckUpdateCDVFunction().call(runtime,
          Arrays.asList("RECEIVED", "COMPLETED"), 0);
      assertEquals("IGNORED", statusCode);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  @Test
  void nullArgs() {
    assertThrows(RteException.class, () ->
        new CheckUpdateCDVFunction().call(runtime, null, 0));
  }

  @Test
  void numberOfArgs() {
    assertThrows(RteException.class, () ->
        new CheckUpdateCDVFunction().call(runtime,
            Arrays.asList("RECEIVED", "PAYMENT_SENT", "SELLER", "REJECTED"), 0));
  }

  @Test
  public void testCheckUpdateSELLER()
      throws RteInternalException, ExitFunctionException, RteException {
    Properties properties = new Properties();
    runtime = new RteRuntime(properties);
    List<StatusTransition> accepted = Arrays.asList(
        new StatusTransition("APPROVED", "REFUSED"),
        new StatusTransition("APPROVED_PARTIALLY", "REFUSED"),
        new StatusTransition("COMPLETED", "REFUSED"),
        new StatusTransition("UPLOADED", "REFUSED"),
        new StatusTransition("SENT", "REFUSED"),
        new StatusTransition("DISPUTED", "REFUSED"),
        new StatusTransition("PAYMENT_RECEIVED", "REFUSED"),
        new StatusTransition("AVAILABLE", "REFUSED"),
        new StatusTransition("NOT_APPROVED_B2G", "REFUSED"),
        new StatusTransition("PAYMENT_SENT", "REFUSED"),
        new StatusTransition("OPENED", "REFUSED"),
        new StatusTransition("RECEIVED", "REFUSED"),
        new StatusTransition("SUSPENDED", "REFUSED"),
        new StatusTransition("APPROVED_B2G", "REFUSED"),

        new StatusTransition("APPROVED", "PAYMENT_RECEIVED"),
        new StatusTransition("APPROVED_PARTIALLY", "PAYMENT_RECEIVED"),
        new StatusTransition("COMPLETED", "PAYMENT_RECEIVED"),
        new StatusTransition("UPLOADED", "PAYMENT_RECEIVED"),
        new StatusTransition("SENT", "PAYMENT_RECEIVED"),
        new StatusTransition("DISPUTED", "PAYMENT_RECEIVED"),
        new StatusTransition("PAYMENT_RECEIVED", "PAYMENT_RECEIVED"),
        new StatusTransition("AVAILABLE", "PAYMENT_RECEIVED"),
        new StatusTransition("NOT_APPROVED_B2G", "PAYMENT_RECEIVED"),
        new StatusTransition("PAYMENT_SENT", "PAYMENT_RECEIVED"),
        new StatusTransition("OPENED", "PAYMENT_RECEIVED"),
        new StatusTransition("RECEIVED", "PAYMENT_RECEIVED"),
        new StatusTransition("SUSPENDED", "PAYMENT_RECEIVED"),
        new StatusTransition("APPROVED_B2G", "PAYMENT_RECEIVED"),
        new StatusTransition("SUSPENDED", "COMPLETED")
    );
    List<StatusTransition> ignored = Arrays.asList(
        new StatusTransition("UNDEFINED", "COMPLETED"),
        new StatusTransition("APPROVED", "COMPLETED"),
        new StatusTransition("APPROVED_PARTIALLY", "COMPLETED"),
        new StatusTransition("COMPLETED", "COMPLETED"),
        new StatusTransition("UPLOADED", "COMPLETED"),
        new StatusTransition("SENT", "COMPLETED"),
        new StatusTransition("DISPUTED", "COMPLETED"),
        new StatusTransition("PAYMENT_RECEIVED", "COMPLETED"),
        new StatusTransition("AVAILABLE", "COMPLETED"),
        new StatusTransition("NOT_APPROVED_B2G", "COMPLETED"),
        new StatusTransition("PAYMENT_SENT", "COMPLETED"),
        new StatusTransition("OPENED", "COMPLETED"),
        new StatusTransition("RECEIVED", "COMPLETED"),
        new StatusTransition("SUSPENDED", "COMPLETED"),
        new StatusTransition("APPROVED_B2G", "COMPLETED")
    );
    for (DocumentStage currentStatus : DocumentStage.values()) {
      for (DocumentStage newStatus : DocumentStage.values()) {
        StatusTransition transition = new StatusTransition(currentStatus.name(), newStatus.name());
        if (accepted.contains(transition)) {
          assertEquals(
              "ACCEPTED", new CheckUpdateCDVFunction().call(runtime,
                  Arrays.asList(transition.getCurrentStatus(), transition.getNewStatus(),
                      SELLER.name()), 0), transition.getNewStatus() + " should be accepted from "
                  + transition.getCurrentStatus() + " for seller");
        } else if (ignored.contains(transition)) {
          Assertions.assertEquals("IGNORED", new CheckUpdateCDVFunction().call(runtime,
              Arrays.asList(transition.getCurrentStatus(), transition.getNewStatus(),
                  SELLER.name()), 0), transition.getNewStatus() + " should be accepted from "
              + transition.getCurrentStatus() + " for seller");
        } else {
          assertEquals("REJECTED", new CheckUpdateCDVFunction().call(runtime,
              Arrays.asList(transition.getCurrentStatus(), transition.getNewStatus(),
                  SELLER.name()), 0), transition.getNewStatus() + " should be accepted from "
              + transition.getCurrentStatus() + " for seller");
        }

      }
    }

  }

  @Test
  public void testCheckUpdateFACTOR()
      throws RteInternalException, ExitFunctionException, RteException {
    Properties properties = new Properties();
    runtime = new RteRuntime(properties);
    List<StatusTransition> accepted = Arrays.asList(
        new StatusTransition("SUSPENDED", "COMPLETED")
    );
    List<StatusTransition> ignored = Arrays.asList(
        new StatusTransition("APPROVED", "COMPLETED"),
        new StatusTransition("APPROVED_PARTIALLY", "COMPLETED"),
        new StatusTransition("COMPLETED", "COMPLETED"),
        new StatusTransition("UPLOADED", "COMPLETED"),
        new StatusTransition("SENT", "COMPLETED"),
        new StatusTransition("DISPUTED", "COMPLETED"),
        new StatusTransition("PAYMENT_RECEIVED", "COMPLETED"),
        new StatusTransition("AVAILABLE", "COMPLETED"),
        new StatusTransition("NOT_APPROVED_B2G", "COMPLETED"),
        new StatusTransition("PAYMENT_SENT", "COMPLETED"),
        new StatusTransition("OPENED", "COMPLETED"),
        new StatusTransition("RECEIVED", "COMPLETED"),
        new StatusTransition("APPROVED_B2G", "COMPLETED")
    );
    for (DocumentStage currentStatus : DocumentStage.values()) {
      for (DocumentStage newStatus : DocumentStage.values()) {
        StatusTransition transition = new StatusTransition(currentStatus.name(), newStatus.name());
        if (accepted.contains(transition)) {
          assertEquals("ACCEPTED", new CheckUpdateCDVFunction().call(runtime,
              Arrays.asList(transition.getCurrentStatus(), transition.getNewStatus(),
                  FACTOR.name()), 0), transition.getNewStatus() + " should be accepted from "
              + transition.getCurrentStatus() + " for factor");
        } else if (ignored.contains(transition)) {
          assertEquals("IGNORED", new CheckUpdateCDVFunction().call(runtime,
              Arrays.asList(transition.getCurrentStatus(), transition.getNewStatus(),
                  FACTOR.name()), 0), transition.getNewStatus() + " should be accepted from "
              + transition.getCurrentStatus() + " for factor");
        } else {
          assertEquals("REJECTED", new CheckUpdateCDVFunction().call(runtime,
              Arrays.asList(transition.getCurrentStatus(), transition.getNewStatus(),
                  FACTOR.name()), 0), transition.getNewStatus() + " should be accepted from "
              + transition.getCurrentStatus() + " for factor");
        }

      }
    }

  }

  @Test
  public void testCheckUpdateREMITTOPARTY()
      throws RteInternalException, ExitFunctionException, RteException {
    Properties properties = new Properties();
    runtime = new RteRuntime(properties);
    List<StatusTransition> accepted = Arrays.asList(
        new StatusTransition("APPROVED", "PAYMENT_RECEIVED"),
        new StatusTransition("APPROVED_PARTIALLY", "PAYMENT_RECEIVED"),
        new StatusTransition("COMPLETED", "PAYMENT_RECEIVED"),
        new StatusTransition("UPLOADED", "PAYMENT_RECEIVED"),
        new StatusTransition("SENT", "PAYMENT_RECEIVED"),
        new StatusTransition("DISPUTED", "PAYMENT_RECEIVED"),
        new StatusTransition("PAYMENT_RECEIVED", "PAYMENT_RECEIVED"),
        new StatusTransition("AVAILABLE", "PAYMENT_RECEIVED"),
        new StatusTransition("NOT_APPROVED_B2G", "PAYMENT_RECEIVED"),
        new StatusTransition("PAYMENT_SENT", "PAYMENT_RECEIVED"),
        new StatusTransition("OPENED", "PAYMENT_RECEIVED"),
        new StatusTransition("RECEIVED", "PAYMENT_RECEIVED"),
        new StatusTransition("SUSPENDED", "PAYMENT_RECEIVED"),
        new StatusTransition("APPROVED_B2G", "PAYMENT_RECEIVED")
    );
    for (DocumentStage currentStatus : DocumentStage.values()) {
      for (DocumentStage newStatus : DocumentStage.values()) {
        StatusTransition transition = new StatusTransition(currentStatus.name(), newStatus.name());
        if (accepted.contains(transition)) {
          assertEquals("ACCEPTED", new CheckUpdateCDVFunction().call(runtime,
              Arrays.asList(transition.getCurrentStatus(), transition.getNewStatus(),
                  REMITTOPARTY.name()), 0), transition.getNewStatus() + " should be accepted from "
              + transition.getCurrentStatus() + " for remit to party");
        } else {
          assertEquals("REJECTED", new CheckUpdateCDVFunction().call(runtime,
              Arrays.asList(transition.getCurrentStatus(), transition.getNewStatus(),
                  REMITTOPARTY.name()), 0), transition.getNewStatus() + " should be accepted from "
              + transition.getCurrentStatus() + " for remit to party");
        }

      }
    }

  }

  @Test
  public void testCheckUpdateSELLERAGENT()
      throws RteInternalException, ExitFunctionException, RteException {
    Properties properties = new Properties();
    runtime = new RteRuntime(properties);
    List<StatusTransition> accepted = Arrays.asList(
        new StatusTransition("UPLOADED", "APPROVED_B2G"),
        new StatusTransition("SENT", "APPROVED_B2G"),
        new StatusTransition("COMPLETED", "APPROVED_B2G"),

        new StatusTransition("APPROVED", "REFUSED"),
        new StatusTransition("APPROVED_PARTIALLY", "REFUSED"),
        new StatusTransition("COMPLETED", "REFUSED"),
        new StatusTransition("UPLOADED", "REFUSED"),
        new StatusTransition("SENT", "REFUSED"),
        new StatusTransition("DISPUTED", "REFUSED"),
        new StatusTransition("PAYMENT_RECEIVED", "REFUSED"),
        new StatusTransition("AVAILABLE", "REFUSED"),
        new StatusTransition("NOT_APPROVED_B2G", "REFUSED"),
        new StatusTransition("PAYMENT_SENT", "REFUSED"),
        new StatusTransition("OPENED", "REFUSED"),
        new StatusTransition("RECEIVED", "REFUSED"),
        new StatusTransition("SUSPENDED", "REFUSED"),
        new StatusTransition("APPROVED_B2G", "REFUSED"),

        new StatusTransition("APPROVED_PARTIALLY", "APPROVED"),
        new StatusTransition("COMPLETED", "APPROVED"),
        new StatusTransition("UPLOADED", "APPROVED"),
        new StatusTransition("SENT", "APPROVED"),
        new StatusTransition("DISPUTED", "APPROVED"),
        new StatusTransition("PAYMENT_RECEIVED", "APPROVED"),
        new StatusTransition("AVAILABLE", "APPROVED"),
        new StatusTransition("NOT_APPROVED_B2G", "APPROVED"),
        new StatusTransition("OPENED", "APPROVED"),
        new StatusTransition("RECEIVED", "APPROVED"),
        new StatusTransition("SUSPENDED", "APPROVED"),
        new StatusTransition("APPROVED_B2G", "APPROVED")
    );
    List<StatusTransition> ignored = Arrays.asList(
        new StatusTransition("APPROVED", "APPROVED_B2G"),
        new StatusTransition("APPROVED_PARTIALLY", "APPROVED_B2G"),
        new StatusTransition("DISPUTED", "APPROVED_B2G"),
        new StatusTransition("AVAILABLE", "APPROVED_B2G"),
        new StatusTransition("NOT_APPROVED_B2G", "APPROVED_B2G"),
        new StatusTransition("PAYMENT_SENT", "APPROVED_B2G"),
        new StatusTransition("OPENED", "APPROVED_B2G"),
        new StatusTransition("RECEIVED", "APPROVED_B2G"),
        new StatusTransition("SUSPENDED", "APPROVED_B2G"),
        new StatusTransition("APPROVED_B2G", "APPROVED_B2G"),
        new StatusTransition("APPROVED", "APPROVED"),
        new StatusTransition("PAYMENT_SENT", "APPROVED")
    );
    for (DocumentStage currentStatus : DocumentStage.values()) {
      for (DocumentStage newStatus : DocumentStage.values()) {
        StatusTransition transition = new StatusTransition(currentStatus.name(), newStatus.name());
        if (accepted.contains(transition)) {
          assertEquals("ACCEPTED", new CheckUpdateCDVFunction().call(runtime,
              Arrays.asList(transition.getCurrentStatus(), transition.getNewStatus(),
                  SELLERAGENT.name()), 0), transition.getNewStatus() + " should be accepted from "
              + transition.getCurrentStatus() + " for seller agent");
        } else if (ignored.contains(transition)) {
          assertEquals("IGNORED", new CheckUpdateCDVFunction().call(runtime,
              Arrays.asList(transition.getCurrentStatus(), transition.getNewStatus(),
                  SELLERAGENT.name()), 0), transition.getNewStatus() + " should be accepted from "
              + transition.getCurrentStatus() + " for seller agent");
        } else {
          assertEquals("REJECTED", new CheckUpdateCDVFunction().call(runtime,
              Arrays.asList(transition.getCurrentStatus(), transition.getNewStatus(),
                  SELLERAGENT.name()), 0), transition.getNewStatus() + " should be accepted from "
              + transition.getCurrentStatus() + " for seller agent");
        }

      }
    }

  }

  @Test
  public void testCheckUpdateBUYER()
      throws RteInternalException, ExitFunctionException, RteException {
    Properties properties = new Properties();
    runtime = new RteRuntime(properties);
    List<StatusTransition> accepted = Arrays.asList(
        new StatusTransition("APPROVED", "REFUSED"),
        new StatusTransition("APPROVED_PARTIALLY", "REFUSED"),
        new StatusTransition("COMPLETED", "REFUSED"),
        new StatusTransition("UPLOADED", "REFUSED"),
        new StatusTransition("SENT", "REFUSED"),
        new StatusTransition("DISPUTED", "REFUSED"),
        new StatusTransition("PAYMENT_RECEIVED", "REFUSED"),
        new StatusTransition("AVAILABLE", "REFUSED"),
        new StatusTransition("NOT_APPROVED_B2G", "REFUSED"),
        new StatusTransition("PAYMENT_SENT", "REFUSED"),
        new StatusTransition("OPENED", "REFUSED"),
        new StatusTransition("RECEIVED", "REFUSED"),
        new StatusTransition("SUSPENDED", "REFUSED"),
        new StatusTransition("APPROVED_B2G", "REFUSED"),

        new StatusTransition("APPROVED", "PAYMENT_SENT"),
        new StatusTransition("APPROVED_PARTIALLY", "PAYMENT_SENT"),
        new StatusTransition("COMPLETED", "PAYMENT_SENT"),
        new StatusTransition("UPLOADED", "PAYMENT_SENT"),
        new StatusTransition("SENT", "PAYMENT_SENT"),
        new StatusTransition("DISPUTED", "PAYMENT_SENT"),
        new StatusTransition("PAYMENT_RECEIVED", "PAYMENT_SENT"),
        new StatusTransition("AVAILABLE", "PAYMENT_SENT"),
        new StatusTransition("NOT_APPROVED_B2G", "PAYMENT_SENT"),
        new StatusTransition("PAYMENT_SENT", "PAYMENT_SENT"),
        new StatusTransition("OPENED", "PAYMENT_SENT"),
        new StatusTransition("RECEIVED", "PAYMENT_SENT"),
        new StatusTransition("SUSPENDED", "PAYMENT_SENT"),
        new StatusTransition("APPROVED_B2G", "PAYMENT_SENT"),

        new StatusTransition("APPROVED", "APPROVED_PARTIALLY"),
        new StatusTransition("COMPLETED", "APPROVED_PARTIALLY"),
        new StatusTransition("UPLOADED", "APPROVED_PARTIALLY"),
        new StatusTransition("SENT", "APPROVED_PARTIALLY"),
        new StatusTransition("DISPUTED", "APPROVED_PARTIALLY"),
        new StatusTransition("PAYMENT_RECEIVED", "APPROVED_PARTIALLY"),
        new StatusTransition("AVAILABLE", "APPROVED_PARTIALLY"),
        new StatusTransition("NOT_APPROVED_B2G", "APPROVED_PARTIALLY"),
        new StatusTransition("OPENED", "APPROVED_PARTIALLY"),
        new StatusTransition("RECEIVED", "APPROVED_PARTIALLY"),
        new StatusTransition("SUSPENDED", "APPROVED_PARTIALLY"),
        new StatusTransition("APPROVED_B2G", "APPROVED_PARTIALLY"),

        new StatusTransition("APPROVED_PARTIALLY", "APPROVED"),
        new StatusTransition("COMPLETED", "APPROVED"),
        new StatusTransition("UPLOADED", "APPROVED"),
        new StatusTransition("SENT", "APPROVED"),
        new StatusTransition("DISPUTED", "APPROVED"),
        new StatusTransition("PAYMENT_RECEIVED", "APPROVED"),
        new StatusTransition("AVAILABLE", "APPROVED"),
        new StatusTransition("NOT_APPROVED_B2G", "APPROVED"),
        new StatusTransition("OPENED", "APPROVED"),
        new StatusTransition("RECEIVED", "APPROVED"),
        new StatusTransition("SUSPENDED", "APPROVED"),
        new StatusTransition("APPROVED_B2G", "APPROVED"),

        new StatusTransition("APPROVED", "DISPUTED"),
        new StatusTransition("APPROVED_PARTIALLY", "DISPUTED"),
        new StatusTransition("COMPLETED", "DISPUTED"),
        new StatusTransition("UPLOADED", "DISPUTED"),
        new StatusTransition("SENT", "DISPUTED"),
        new StatusTransition("PAYMENT_RECEIVED", "DISPUTED"),
        new StatusTransition("AVAILABLE", "DISPUTED"),
        new StatusTransition("NOT_APPROVED_B2G", "DISPUTED"),
        new StatusTransition("OPENED", "DISPUTED"),
        new StatusTransition("RECEIVED", "DISPUTED"),
        new StatusTransition("SUSPENDED", "DISPUTED"),
        new StatusTransition("APPROVED_B2G", "DISPUTED"),

        new StatusTransition("COMPLETED", "SUSPENDED"),
        new StatusTransition("UPLOADED", "SUSPENDED"),
        new StatusTransition("SENT", "SUSPENDED"),
        new StatusTransition("PAYMENT_RECEIVED", "SUSPENDED"),
        new StatusTransition("AVAILABLE", "SUSPENDED"),
        new StatusTransition("NOT_APPROVED_B2G", "SUSPENDED"),
        new StatusTransition("OPENED", "SUSPENDED"),
        new StatusTransition("RECEIVED", "SUSPENDED"),
        new StatusTransition("APPROVED_B2G", "SUSPENDED"),

        new StatusTransition("COMPLETED", "OPENED"),
        new StatusTransition("UPLOADED", "OPENED"),
        new StatusTransition("SENT", "OPENED"),
        new StatusTransition("PAYMENT_RECEIVED", "OPENED"),
        new StatusTransition("AVAILABLE", "OPENED"),
        new StatusTransition("NOT_APPROVED_B2G", "OPENED"),
        new StatusTransition("RECEIVED", "OPENED"),
        new StatusTransition("APPROVED_B2G", "OPENED")

    );
    List<StatusTransition> ignored = Arrays.asList(
        new StatusTransition("APPROVED_PARTIALLY", "APPROVED_PARTIALLY"),
        new StatusTransition("PAYMENT_SENT", "APPROVED_PARTIALLY"),
        new StatusTransition("APPROVED", "APPROVED"),
        new StatusTransition("PAYMENT_SENT", "APPROVED"),
        new StatusTransition("DISPUTED", "DISPUTED"),
        new StatusTransition("PAYMENT_SENT", "DISPUTED"),
        new StatusTransition("SUSPENDED", "SUSPENDED"),
        new StatusTransition("DISPUTED", "SUSPENDED"),
        new StatusTransition("APPROVED", "SUSPENDED"),
        new StatusTransition("APPROVED_PARTIALLY", "SUSPENDED"),
        new StatusTransition("PAYMENT_SENT", "SUSPENDED"),
        new StatusTransition("OPENED", "OPENED"),
        new StatusTransition("SUSPENDED", "OPENED"),
        new StatusTransition("DISPUTED", "OPENED"),
        new StatusTransition("APPROVED", "OPENED"),
        new StatusTransition("APPROVED_PARTIALLY", "OPENED"),
        new StatusTransition("PAYMENT_SENT", "OPENED")
    );
    for (DocumentStage currentStatus : DocumentStage.values()) {
      for (DocumentStage newStatus : DocumentStage.values()) {
        StatusTransition transition = new StatusTransition(currentStatus.name(), newStatus.name());
        if (accepted.contains(transition)) {
          assertEquals("ACCEPTED", new CheckUpdateCDVFunction().call(runtime,
              Arrays.asList(transition.getCurrentStatus(), transition.getNewStatus(),
                  BUYER.name()), 0), transition.getNewStatus() + " should be accepted from "
              + transition.getCurrentStatus() + " for buyer");
          assertEquals("ACCEPTED", new CheckUpdateCDVFunction().call(runtime,
              Arrays.asList(transition.getCurrentStatus(), transition.getNewStatus(),
                  BUYERAGENT.name()), 0), transition.getNewStatus() + " should be accepted from "
              + transition.getCurrentStatus() + " for buyer agent");
          assertEquals("ACCEPTED", new CheckUpdateCDVFunction().call(runtime,
              Arrays.asList(transition.getCurrentStatus(), transition.getNewStatus(),
                  BILLTOPARTY.name()), 0), transition.getNewStatus() + " should be accepted from "
              + transition.getCurrentStatus() + " for bill to party");
        } else if (ignored.contains(transition)) {
          assertEquals("IGNORED", new CheckUpdateCDVFunction().call(runtime,
              Arrays.asList(transition.getCurrentStatus(), transition.getNewStatus(),
                  BUYER.name()), 0), transition.getNewStatus() + " should be accepted from "
              + transition.getCurrentStatus() + " for buyer");
          assertEquals("IGNORED", new CheckUpdateCDVFunction().call(runtime,
              Arrays.asList(transition.getCurrentStatus(), transition.getNewStatus(),
                  BUYERAGENT.name()), 0), transition.getNewStatus() + " should be accepted from "
              + transition.getCurrentStatus() + " for buyer agent");
          assertEquals("IGNORED", new CheckUpdateCDVFunction().call(runtime,
              Arrays.asList(transition.getCurrentStatus(), transition.getNewStatus(),
                  BILLTOPARTY.name()), 0), transition.getNewStatus() + " should be accepted from "
              + transition.getCurrentStatus() + " for bill to party");
        } else {
          assertEquals("REJECTED", new CheckUpdateCDVFunction().call(runtime,
              Arrays.asList(transition.getCurrentStatus(), transition.getNewStatus(),
                  BUYER.name()), 0), transition.getNewStatus() + " should be accepted from "
              + transition.getCurrentStatus() + " for buyer");
          assertEquals("REJECTED", new CheckUpdateCDVFunction().call(runtime,
              Arrays.asList(transition.getCurrentStatus(), transition.getNewStatus(),
                  BUYERAGENT.name()), 0), transition.getNewStatus() + " should be accepted from "
              + transition.getCurrentStatus() + " for buyer agent");
          assertEquals("REJECTED", new CheckUpdateCDVFunction().call(runtime,
              Arrays.asList(transition.getCurrentStatus(), transition.getNewStatus(),
                  BILLTOPARTY.name()), 0), transition.getNewStatus() + " should be accepted from "
              + transition.getCurrentStatus() + " for bill to party");
        }

      }
    }

  }

  @Test
  public void testCheckUpdatePAYER()
      throws RteInternalException, ExitFunctionException, RteException {
    Properties properties = new Properties();
    runtime = new RteRuntime(properties);
    List<StatusTransition> accepted = Arrays.asList(
        new StatusTransition("APPROVED", "PAYMENT_SENT"),
        new StatusTransition("APPROVED_PARTIALLY", "PAYMENT_SENT"),
        new StatusTransition("UPLOADED", "PAYMENT_SENT"),
        new StatusTransition("SENT", "PAYMENT_SENT"),
        new StatusTransition("DISPUTED", "PAYMENT_SENT"),
        new StatusTransition("PAYMENT_RECEIVED", "PAYMENT_SENT"),
        new StatusTransition("AVAILABLE", "PAYMENT_SENT"),
        new StatusTransition("NOT_APPROVED_B2G", "PAYMENT_SENT"),
        new StatusTransition("PAYMENT_SENT", "PAYMENT_SENT"),
        new StatusTransition("OPENED", "PAYMENT_SENT"),
        new StatusTransition("RECEIVED", "PAYMENT_SENT"),
        new StatusTransition("SUSPENDED", "PAYMENT_SENT"),
        new StatusTransition("APPROVED_B2G", "PAYMENT_SENT")
    );
    List<StatusTransition> ignored = Arrays.asList(
        new StatusTransition("COMPLETED", "PAYMENT_SENT")
    );
    for (DocumentStage currentStatus : DocumentStage.values()) {
      for (DocumentStage newStatus : DocumentStage.values()) {
        StatusTransition transition = new StatusTransition(currentStatus.name(), newStatus.name());
        if (accepted.contains(transition)) {
          assertEquals("ACCEPTED", new CheckUpdateCDVFunction().call(runtime,
              Arrays.asList(transition.getCurrentStatus(), transition.getNewStatus(),
                  PAYER.name()), 0), transition.getNewStatus() + " should be accepted from "
              + transition.getCurrentStatus() + " for payer");
        } else if (ignored.contains(transition)) {
          assertEquals("IGNORED", new CheckUpdateCDVFunction().call(runtime,
              Arrays.asList(transition.getCurrentStatus(), transition.getNewStatus(),
                  PAYER.name()), 0), transition.getNewStatus() + " should be accepted from "
              + transition.getCurrentStatus() + " for payer");
        } else {
          assertEquals("REJECTED", new CheckUpdateCDVFunction().call(runtime,
              Arrays.asList(transition.getCurrentStatus(), transition.getNewStatus(),
                  PAYER.name()), 0), transition.getNewStatus() + " should be accepted from "
              + transition.getCurrentStatus() + " for payer");
        }

      }
    }

  }

  @Data
  @AllArgsConstructor
  private class StatusTransition {

    private String currentStatus;
    private String newStatus;

    @Override
    public boolean equals(Object obj) {
      if (this == obj) {
        return true;
      }
      if (obj == null) {
        return false;
      }
      if (getClass() != obj.getClass()) {
        return false;
      }
      StatusTransition other = (StatusTransition) obj;
      return Objects.equals(currentStatus, other.currentStatus) && Objects.equals(newStatus,
          other.newStatus);
    }
  }
}
