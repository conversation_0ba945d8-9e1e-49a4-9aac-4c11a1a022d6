INSERT INTO gnx_i18n_module (id,name) VALUES (32, 'Accounting_posting_balance');

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.balance_label', 'Soldes ', 'fr', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.balance_label', 'Balances', 'en', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.balance_label', 'Equilibrios', 'es', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.balance_label', 'Bilanzen', 'de', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.balance_label', 'Balansen', 'nl', NOW(), 32);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.balance_msg', 'Cette facture n’est pas équilibrée et ne peut donc pas faire l’objet d’une imputation comptable. Veuillez procéder à sa correction.', 'fr', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.balance_msg', 'This invoice is not balanced and therefore cannot be subject to accounting imputation. Please proceed to correct it.', 'en', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.balance_msg', 'Esta factura no está equilibrada y, por lo tanto, no puede estar sujeta a imputación contable. Por favor, proceda a corregirla.', 'es', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.balance_msg', ' Diese Rechnung ist nicht ausgeglichen und kann daher nicht der buchhalterischen.', 'de', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.balance_msg', 'Deze factuur is niet in balans en kan daarom niet worden onderworpen aan boekhoudkundige toerekening. Gelieve deze te corrigeren.', 'nl', NOW(), 32);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.incorrect_tva_amount', 'Montant de TVA à', 'fr', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.incorrect_tva_amount', 'VAT amount at', 'en', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.incorrect_tva_amount', 'Importe del IVA en', 'es', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.incorrect_tva_amount', 'Mehrwertsteuerbetrag bei', 'de', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.incorrect_tva_amount', 'BTW-bedrag op', 'nl', NOW(), 32);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.bolt_icon_tooltip', 'Appliquer ce compte à toutes les lignes non-encore imputées', 'fr', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.bolt_icon_tooltip', 'Apply this account to all lines not yet posted', 'en', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.bolt_icon_tooltip', 'Apply this account to all lines not yet posted', 'es', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.bolt_icon_tooltip', 'Apply this account to all lines not yet posted', 'de', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.bolt_icon_tooltip', 'Apply this account to all lines not yet posted', 'nl', NOW(), 32);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.account_col', 'Compte', 'fr', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.account_col', 'Account', 'en', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.account_col', 'Cuenta', 'es', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.account_col', 'Konto', 'de', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.account_col', 'Rekening', 'nl', NOW(), 32);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.detail_col', 'Détail', 'fr', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.detail_col', 'Detail', 'en', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.detail_col', 'Detalle', 'es', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.detail_col', 'Detail', 'de', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.detail_col', 'Detail', 'nl', NOW(), 32);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.debit_col', 'Débit', 'fr', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.debit_col', 'Debit', 'en', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.debit_col', 'Débito', 'es', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.debit_col', 'Lastschrift', 'de', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.debit_col', 'Debet', 'nl', NOW(), 32);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.credit_col', 'Crédit', 'fr', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.credit_col', 'Credit', 'en', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.credit_col', 'Crédito', 'es', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.credit_col', 'Kredit', 'de', NOW(), 32);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('invoice-control.accounting-allocations.tab.credit_col', 'Credit', 'nl', NOW(), 32);

