INSERT INTO gnx_i18n_module (id, name) VALUES (8, 'Compliance portlet');
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id) VALUES ('invoice_compliance_rule', 'Rule', 'en', NOW(), 8);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id) VALUES ('invoice_compliance_rule', 'Règle', 'fr', NOW(), 8);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id) VALUES ('invoice_compliance_reason', 'Reason', 'en', NOW(), 8);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id) VALUES ('invoice_compliance_reason', 'Motif', 'fr', NOW(), 8);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id) VALUES ('invoice_compliance_status', 'Status', 'en', NOW(), 8);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id) VALUES ('invoice_compliance_status', 'Statut', 'fr', NOW(), 8);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id) VALUES ('invoice_compliance_timestamp', 'Date & time', 'en', NOW(), 8);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id) VALUES ('invoice_compliance_timestamp', 'Date & heure', 'fr', NOW(), 8);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id) VALUES ('invoice_compliance_status_ok', 'Ok', 'en', NOW(), 8);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id) VALUES ('invoice_compliance_status_ok', 'Ok', 'fr', NOW(), 8);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id) VALUES ('invoice_compliance_status_error', 'Error', 'en', NOW(), 8);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id) VALUES ('invoice_compliance_status_error', 'Erreur', 'fr', NOW(), 8);
