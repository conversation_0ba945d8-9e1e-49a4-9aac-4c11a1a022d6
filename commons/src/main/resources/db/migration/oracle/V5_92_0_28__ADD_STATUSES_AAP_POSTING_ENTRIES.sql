delete from DOCUMENT_STATUS where status_code = 'AAP_EXPORTED';
DECLARE
    STATUS_ID NUMBER(19,0);
    COUNTER NUMBER;
BEGIN

    -- AAP_READY
SELECT COUNT(STATUS_CODE) INTO COUNTER FROM DOCUMENT_STATUS WHERE STATUS_CODE = 'AAP_EXPORTED';
IF COUNTER = 0 THEN
        INSERT INTO DOCUMENT_STATUS(STATUS_CODE) VALUES ('AAP_EXPORTED');
FOR ENV IN (SELECT DISTINCT(ENV_CODE) AS CODE FROM DOC_STS_VALUES) LOOP
SELECT DOC_VALUE INTO STATUS_ID FROM (SELECT * FROM DOC_STATUS_VALUES_MAPPING ORDER BY DOC_VALUE DESC) WHERE ROWNUM = 1;
STATUS_ID := STATUS_ID + 1;
INSERT INTO DOC_STS_VALUES(ID, ENV_CODE, STYLE) VALUES (STATUS_ID, ENV.CODE, 'status_deep_blue');
INSERT INTO DOC_STATUS_VALUES_MAPPING(DOC_STATUS, DOC_VALUE) VALUES ('AAP_EXPORTED',STATUS_ID);
INSERT INTO DOC_STATUS_LABELS(LABELS_ID, LABELS, NAME) VALUES (STATUS_ID,'A/c Posting exported','en');
INSERT INTO DOC_STATUS_LABELS(LABELS_ID, LABELS, NAME) VALUES (STATUS_ID,'Imput. exportée','fr');
END LOOP;
END IF;
END;