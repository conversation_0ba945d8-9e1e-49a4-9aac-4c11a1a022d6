package com.byzaneo.generix.api.service.internal;

import com.byzaneo.commons.bean.FileType;
import com.byzaneo.generix.api.bean.BqlUuidDto;
import io.swagger.annotations.*;
import org.apache.cxf.jaxrs.model.wadl.Description;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.UUID;

@Api(tags = "gnxRestAdvancedRecapListService", description = "Operations about Advanced Recap List")

@Path(RestAdvancedRecapListService.SERVICE_PATH)
public interface RestAdvancedRecapListService {
    String SERVICE_NAME = "gnxRestAdvancedRecapListService";
    String SERVICE_PATH = "";

    /**
     * Retrieves the Advanced Recap List.
     */
    @GET
    @Path("/v1/environments/{env-code}/portlet/{portlet-id}/advanced-recap")
    @Produces({"application/json"})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "Retrieve Advanced Recap List", authorizations = {@Authorization(value = "Bearer")})
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
    })
    Response v1getAdvancedRecapList(@Context HttpServletRequest request, @QueryParam("bql") String bql,
                                    @HeaderParam("Request-Id") UUID requestId, @Max(1000) @QueryParam("limit") Integer limit,
                                    @QueryParam("offset") Integer offset, @QueryParam("legalEntity") String legalEntity,
                                    @QueryParam("flowDirection") String flowDirection, @QueryParam("sortBy") String sortBy,
                                    @QueryParam("order") Sort.Direction order, @QueryParam("count") boolean count,
                                    @PathParam("portlet-id") String portletId, @QueryParam("locale") @NotNull String locale,
                                    @QueryParam("isCountEnabled") boolean isCountEnabled);

    /**
     * Exports the Advanced Recap List as an Excel file.
     */
    @POST
    @Path("/v1/environments/{env-code}/export/portlet/{portlet-id}/legalEntity/{legalEntity}")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    @ApiOperation(value = "Export Advanced Recap as Excel File")
    Response exportAdvancedRecapAsExcelFile(@Context HttpServletRequest request,
                                                              @PathParam("env-code") String envCode,
                                                              @Description("The BQL query used to search the agreements")
                                                              @RequestBody BqlUuidDto bql,
                                                              @QueryParam("sortBy") String sortBy, @QueryParam("order") Sort.Direction order,
                                                              @PathParam("portlet-id") Long portletId, @QueryParam("locale") @NotNull String locale,
                                                              @PathParam("legalEntity") String legalEntity,
                                                              @QueryParam("file-type") FileType fileType,
                                                              @QueryParam("export-archive") boolean exportArchive
    );

}


