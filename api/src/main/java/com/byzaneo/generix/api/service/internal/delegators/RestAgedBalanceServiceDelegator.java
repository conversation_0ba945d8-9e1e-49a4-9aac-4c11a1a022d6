package com.byzaneo.generix.api.service.internal.delegators;

import com.byzaneo.commons.bean.FileType;
import com.byzaneo.generix.api.bean.SortOrderDto;
import io.swagger.annotations.*;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.data.domain.Sort;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Max;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;


public interface RestAgedBalanceServiceDelegator {

  Response getAgedBalance(HttpServletRequest request,
      String bql,
      String envCode,
      String portletId,
      Integer limit,
      Integer pageNumber,
      String sortBy,
      Sort.Direction order,
      boolean count,
      String legalEntity,
      boolean overdueOnly,
      boolean enableRefresh);


  Response getAgedBalanceInvoiceList(HttpServletRequest request,
      String bql,
      String envCode,
      String portletId,
      String legalEntities,
      boolean overdueOnly);

  public Response getExportedAgedBalanceFile(HttpServletRequest request, String envCode, String bql, String sortBy, Sort.Direction order,
      String sortByForInvoice, Sort.Direction orderForInvoice, Long portletId, String locale, FileType fileType, boolean exportArchive,
      boolean exportDetails, String legalEntity, boolean overdueOnly);
}
