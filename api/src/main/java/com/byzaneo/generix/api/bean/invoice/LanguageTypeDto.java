package com.byzaneo.generix.api.bean.invoice;

import com.byzaneo.xtrade.xcbl.v4_0.core.core.LanguageCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.LocaleCodeType;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class LanguageTypeDto {

  private LanguageCodeType languageCoded;

  private String languageCodedOther;

  private LocaleCodeType localeCoded;

  private String localeCodedOther;

  private Boolean languageDependent;
}
