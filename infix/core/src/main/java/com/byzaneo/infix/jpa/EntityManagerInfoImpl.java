package com.byzaneo.infix.jpa;

import java.util.Map;

import javax.sql.DataSource;

/**
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date May 2, 2012
 * @since
 */
public class EntityManagerInfoImpl implements EntityManagerInfo {

  private DataSource dataSource;
  // private JpaVendorAdapter jpaVendorAdapter;
  private Map<String, Object> jpaPropertyMap;

  @Override
  public DataSource getDataSource() {
    return dataSource;
  }

  public void setDataSource(DataSource dataSource) {
    this.dataSource = dataSource;
  }

  // public JpaVendorAdapter getJpaVendorAdapter() { return jpaVendorAdapter; }
  // public void setJpaVendorAdapter(JpaVendorAdapter jpaVendorAdapter) { this.jpaVendorAdapter = jpaVendorAdapter; }

  @Override
  public Map<String, Object> getJpaPropertyMap() {
    return jpaPropertyMap;
  }

  public void setJpaPropertyMap(Map<String, Object> jpaProperties) {
    this.jpaPropertyMap = jpaProperties;
  }
}
