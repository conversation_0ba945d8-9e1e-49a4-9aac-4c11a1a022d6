<?xml version="1.0" encoding="ISO-8859-1"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task"
	xmlns:mongo="http://www.springframework.org/schema/data/mongo"
	xsi:schemaLocation="
			http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
			http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
			http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
			http://www.springframework.org/schema/data/mongo http://www.springframework.org/schema/data/mongo/spring-mongo-3.3.xsd
			http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd">

	<!-- E X T E R N A L   P R O P E R T I E S -->
	<bean id="comPropertyConfigurer" class="com.byzaneo.commons.util.PropertyPlaceholderConfigurer">
		<property name="locations">
			<list>
				<value>classpath:/commons.properties</value>
				<value>classpath:/task-test.properties</value>
			</list>
		</property>
	</bean>

	<!-- S E C U R I T Y -->
	<import resource="classpath:/META-INF/spring/security-pwd.beans.xml" />

  	<!-- C O M M O N S -->
  	<import resource="classpath:/META-INF/spring/commons-ds.beans.xml" />
  	<import resource="classpath:/META-INF/spring/commons-services.beans.xml" />

	<!-- T A S K T Y P E S -->
	<bean class="com.byzaneo.task.spi.AnnotatedTaskTypeProvider" />
	
	<!-- S P R I N G A N N O T A T I O N S -->
	<context:annotation-config />
	<context:component-scan base-package="com.byzaneo.commons" />
	<context:component-scan base-package="com.byzaneo.security" />
	<context:component-scan base-package="com.byzaneo.task" />

	<!-- SERVER -->
	<mongo:mongo-client id="xtdMongoClient"  connection-string="${index.mongo.uri}" >
		<mongo:client-settings
			cluster-server-selection-timeout="${index.mongo.socketTimeout:45000}"
			application-name="${index.mongo.description:Mongo Index Operations Client}"
			connection-pool-max-size="${index.mongo.connectionsPerHost:10}"
			connection-pool-min-size="${index.mongo.minConnectionsPerHost:0}"
			connection-pool-max-connection-idle-time="${index.mongo.maxConnectionIdleTime:0}"
			connection-pool-max-connection-life-time="${index.mongo.maxConnectionLifeTime:0}"
			socket-connect-timeout="${index.mongo.connectTimeout:10000}"
			connection-pool-max-wait-time="${index.mongo.maxWaitTime:180000}"
			socket-read-timeout="${index.mongo.socketTimeout:45000}"
			read-preference="${index.mongo.readPreference:PRIMARY_PREFERRED}"
			server-heartbeat-frequency="${index.mongo.heartbeatFrequency:10000}"
			server-min-heartbeat-frequency="${index.mongo.minHeartbeatFrequency:500}"
		/>
	</mongo:mongo-client>

	<!-- FACTORY -->
	<bean id="mongoDbName"
		class="com.byzaneo.commons.util.DataSourceMongo"
		factory-method="extractMongoDbName">
		<constructor-arg value="${index.mongo.uri}" />
	</bean>
	<mongo:db-factory id="xtdMongoDbFactory" dbname="#{mongoDbName}" mongo-client-ref="xtdMongoClient" />

	<!-- CONVERTERS -->
	<mongo:mapping-converter id="xtdMongoMappingConverter" db-factory-ref="xtdMongoDbFactory">
		<mongo:custom-converters>
			<mongo:converter><bean class="com.byzaneo.commons.dao.mongo.ClassWriteConverter" /></mongo:converter>
			<mongo:converter><bean class="com.byzaneo.commons.dao.mongo.ClassReadConverter" /></mongo:converter>
		</mongo:custom-converters>
	</mongo:mapping-converter>

	<bean id="documentLockingDummy" class="com.byzaneo.commons.test.service.DocumentLockingServiceDummy" />
</beans>
