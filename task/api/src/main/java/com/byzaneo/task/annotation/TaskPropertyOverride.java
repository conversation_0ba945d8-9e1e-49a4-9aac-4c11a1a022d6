package com.byzaneo.task.annotation;

import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

/**
 * Allows to override a task property definition in the current or parent types.
 * <p/>
 * This annotation is only available in {@link TaskViewModel}.
 * 
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Sep 7, 2015
 * @since 3.0 TSK-26
 */
@Documented
@Target({})
@Retention(RUNTIME)
public @interface TaskPropertyOverride {

  /**
   * Name of the property to override.
   */
  String name();

  /**
   * The task property override {@link TaskProperty})
   */
  TaskProperty property();
}
