package com.byzaneo.generix.specifics.ui.edition;

import static com.byzaneo.commons.util.SpringContextHelper.getBean;

import com.byzaneo.generix.customer.service.*;
import com.byzaneo.generix.service.SequenceService;
import com.byzaneo.generix.specifics.service.CartService;
import com.byzaneo.generix.xcbl.ui.edition.DocumentEditionContext;

public class LorealDocumentEditionContext extends DocumentEditionContext {

  private static final String PATH = "path";
  private static ArticleService articleService;
  private static CartService cartService;
  private static CalendarService calendarService;
  private static ClientService clientService;
  private static FurnitureService furnitureService;
  private static ProductHierarchyService productHierarchyService;
  private static SequenceService sequenceService;

  // -- VARIABLES --

  public String getPath() {
    return getVariable(PATH, null);
  }

  public void setPath(String path) {
    setVariable(PATH, path);
  }

  // -- SERVICE --

  public ArticleService getArticleService() {
    if (articleService == null)
      articleService = getBean(ArticleService.class, ArticleService.SERVICE_NAME);
    return articleService;
  }

  public CalendarService getCalendarService() {
    if (calendarService == null)
      calendarService = getBean(CalendarService.class, CalendarService.SERVICE_NAME);
    return calendarService;
  }

  public ClientService getClientService() {
    if (clientService == null)
      clientService = getBean(ClientService.class, ClientService.SERVICE_NAME);
    return clientService;
  }

  public FurnitureService getFurnitureService() {
    if (furnitureService == null)
      furnitureService = getBean(FurnitureService.class, FurnitureService.SERVICE_NAME);
    return furnitureService;
  }

  public ProductHierarchyService getProductHierarchyService() {
    if (productHierarchyService == null)
      productHierarchyService = getBean(ProductHierarchyService.class, ProductHierarchyService.SERVICE_NAME);
    return productHierarchyService;
  }

  public SequenceService getSequenceService() {
    if (sequenceService == null)
      sequenceService = getBean(SequenceService.class, SequenceService.SERVICE_NAME);
    return sequenceService;
  }

  public CartService getCartService() {
    if (cartService == null)
      cartService = getBean(CartService.class, CartService.SERVICE_NAME);
    return cartService;
  }
}
