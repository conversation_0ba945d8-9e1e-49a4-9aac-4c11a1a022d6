package com.byzaneo.generix.specifics.util;

import java.math.BigDecimal;
import java.util.*;
import java.util.Calendar;

import com.byzaneo.generix.customer.bean.*;
import com.byzaneo.generix.customer.service.ArticleService;
import com.byzaneo.generix.specifics.bean.*;
import com.byzaneo.security.bean.User;

public final class TestHelper {

  public static Affair createAffair(String id, String label, int orderNumber, String logoName) {
    Affair affair = new Affair();
    affair.setId(id);
    affair.setLabel(label);
    affair.setOrderNumber(orderNumber);
    affair.setLogoName(logoName);
    return affair;
  }

  public static Organization createOrganization(String id, Affair affair, String label) {
    Organization organization = new Organization();
    organization.setId(new OrganizationId(id, affair));
    organization.setLabel(label);
    return organization;
  }

  public static Client createClient(Long numClient, Client parent, String adrNumvoi, String name,
      boolean binB2BSsaisieCode, boolean binB2BSuiviCode, boolean binBlocage, String codeAffaire) {
    Client client = new Client();
    client.setId(numClient);
    client.setAdrNumvoi(adrNumvoi);
    client.setName(name);
    client.setBinB2BSsaisieCode(binB2BSsaisieCode);
    client.setBinB2BSuiviCode(binB2BSuiviCode);
    client.setBinBlocage(binBlocage);
    client.setParent(parent);
    client.setCodeAffair(codeAffaire);
    return client;
  }

  public static Furniture createFurniture(String id, String brand, Date startDate, Date endDate, boolean b2b, PresentationType type,
      int orderNumber) {
    Furniture furniture = new Furniture();
    furniture.setId(id);
    furniture.setBrandCode(brand);
    furniture.setBrandLabel(brand);
    furniture.setStartValidityDate(startDate);
    furniture.setEndValidityDate(endDate);
    furniture.setB2b(b2b);
    furniture.setType(type);
    furniture.setOrderNumber(orderNumber);
    return furniture;
  }

  public static FurnitureDetail createFurnitureDetail(Article article, Integer defaultQuantity) {
    FurnitureDetailId fdId = new FurnitureDetailId();
    fdId.setArticle(article);
    fdId.setCodeGen(article.getGencode());
    FurnitureDetail fd = new FurnitureDetail();
    fd.setDefaultQuantity(defaultQuantity);
    fd.setId(fdId);
    return fd;
  }

  public static Article createArticle(ArticleService service, Affair affair, Client client, String id, ProductHierarchy ph,
      Calendar referenceDate, Article... composedArticles) {

    Article article = new Article();
    article.setId(id);
    article.setGencode(id);
    article.setLabel(ph.getLabel());
    article.setCloseDate(referenceDate.getTime());
    article.setPrices(new ArrayList<>());
    Calendar dateEnd = java.util.Calendar.getInstance();
    dateEnd.set(Calendar.YEAR, referenceDate.get(Calendar.YEAR) + 10);
    article.getPrices()
        .add(createPrice(article, referenceDate.getTime(), dateEnd.getTime(), ph));

    referenceDate.set(Calendar.DAY_OF_YEAR, referenceDate.get(Calendar.DAY_OF_YEAR) + 1);

    ProductHierarchy prodHierarchy = createProdHierarchy(ph.getId(), null, ph.getLevel(), ph.getOrderNumber());
    List<Composition> compositions = createComposition(article, composedArticles);
    List<Assortiment> assortments = createAssortments(article, client);
    return service.createArticle(article, prodHierarchy, compositions, assortments);
  }

  public static List<Assortiment> createAssortments(Article article, Client client) {

    Calendar dateStart = java.util.Calendar.getInstance();

    List<Assortiment> result = new ArrayList<>();
    dateStart.set(Calendar.DAY_OF_YEAR, dateStart.get(Calendar.DAY_OF_YEAR) + 1);
    Assortiment assortiment = new Assortiment();
    assortiment.setId(new AssortimentId(article, client, dateStart.getTime()));
    result.add(assortiment);
    return result;
  }

  public static Assortiment createAssortiment(Article article, Client client, Date startDate, Date endDate) {
    AssortimentId aid = new AssortimentId();
    aid.setArticle(article);
    aid.setClient(client);
    aid.setStartDate(startDate);
    Assortiment assortiment = new Assortiment();
    assortiment.setId(aid);
    assortiment.setEndDate(endDate);
    return assortiment;
  }

  public static List<Composition> createComposition(Article article, Article... composedArticles) {
    List<Composition> compositions = new ArrayList<>();
    for (Article composedArticle : composedArticles) {
      Composition composition = new Composition();
      composition.setId(new CompositionId(article, composedArticle));
      compositions.add(composition);
    }
    return compositions;
  }

  public static ProductHierarchy createProdHierarchy(String codeHP, String labelHP, int level, int order) {
    ProductHierarchy productHierarchy = new ProductHierarchy();
    productHierarchy.setId(codeHP);
    productHierarchy.setLevel(level);
    productHierarchy.setOrderNumber(order);
    productHierarchy.setLabel(labelHP);
    return productHierarchy;
  }

  public static Price createPrice(Article article, Date startDate, Date endDate, ProductHierarchy ph) {
    Price price = new Price();
    price.setPrice(new BigDecimal(10));
    price.setDateStart(startDate);
    price.setDateEnd(endDate);
    price.setArticle(article);
    price.setId(startDate.toString());
    price.setProductHierarchy(ph);
    return price;
  }

  public static Brand createBrand(String name, Date startDate, boolean publish) {
    Brand brand = new Brand();
    brand.setName(name);
    brand.setCode(name);
    brand.setStartValidity(startDate);
    brand.setPublish(publish);
    return brand;
  }
  
  public static Brand createBrand(String name, Date startDate, Date endDate, boolean publish) {
    Brand brand = new Brand();
    brand.setName(name);
    brand.setCode(name);
    brand.setStartValidity(startDate);
    brand.setEndValidity(endDate);
    brand.setPublish(publish);
    return brand;
  }

  public static FiliereClient createFiliereClient(Client client, FiliereClientType type, PresentationType blisterType) {
    FiliereClientId fcId = new FiliereClientId();
    fcId.setClient(client);
    fcId.setClienGLN(client.getId());
    fcId.setType(type);
    FiliereClient fc = new FiliereClient();
    fc.setClientId(String.valueOf(client.getId()));
    fc.setBlisterType(blisterType);
    fc.setId(fcId);
    fc.setDeliverySunday(true);
    fc.setDeliveryMonday(true);
    fc.setDeliveryTuesday(true);
    fc.setDeliveryWednesday(true);
    fc.setDeliveryThursday(true);
    fc.setDeliveryFriday(true);
    fc.setDeliverySaturday(true);
    return fc;
  }

  public static CodeInterne createCodeInterne(Article article, Client client, String code) {
    CodeInterneId ciId = new CodeInterneId();
    ciId.setArticle(article);
    ciId.setClient(client);
    CodeInterne codeInterne = new CodeInterne();
    codeInterne.setId(ciId);
    codeInterne.setCodPdtint(code);
    codeInterne.setCodNivhiecli("05");
    return codeInterne;
  }

  public static CartItem createCartItem(Article article, BigDecimal qty, BigDecimal price) {
    CartItem cartItem = new CartItem();
    cartItem.setCodArticle(article.getId());
    cartItem.setQuantity(qty);
    cartItem.setPrice(price);
    return cartItem;
  }

  public static Cart createCart(User user, List<CartItem> cartItems, Date creation, Date delivery, Furniture furniture) {
    Cart cart = new Cart(user, cartItems);
    cart.setDateCreation(creation);
    cart.setDeliveryDate(delivery);
    cart.setFurnitureTypeCode(furniture.getId());
    cart.setFurnitureBrandCode(furniture.getId());
    return cart;
  }
}
