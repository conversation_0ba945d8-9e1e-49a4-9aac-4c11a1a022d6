<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml"
              xmlns:cc="http://xmlns.jcp.org/jsf/composite"
              xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
              xmlns:p="http://primefaces.org/ui"
              xmlns:xcbl="http://xmlns.jcp.org/jsf/composite/components/gnx/safraninvoice">
    <cc:interface name="safInvView">
        <cc:attribute name="value" class="com.byzaneo.generix.saf.portal.task.SafranInvoiceEditionTask"/>
        <cc:attribute name="owner" class="com.byzaneo.generix.bean.Instance"/>
    </cc:interface>
    <cc:implementation>
        <p:outputPanel>
            <xcbl:safranDocumentEdition value="#{cc.attrs.value}" owner="#{cc.attrs.owner}"/>
        </p:outputPanel>
    </cc:implementation>
</ui:component>