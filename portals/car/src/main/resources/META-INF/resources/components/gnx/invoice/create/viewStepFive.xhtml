<?xml version="1.0" encoding="UTF-8"?>
<ui:component id="creInvStFiv" xmlns="http://www.w3.org/1999/xhtml" xmlns:f="http://xmlns.jcp.org/jsf/core"
			  xmlns:h="http://xmlns.jcp.org/jsf/html"
			  xmlns:cc="http://xmlns.jcp.org/jsf/composite" xmlns:p="http://primefaces.org/ui"
			  xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
	<!-- INTERFACE -->
	<cc:interface name="creInvStFiv">
		<cc:attribute name="taskBean" required="true" />
		<cc:attribute name="invoice" required="true" />
	</cc:interface>
	<cc:implementation>
		<!-- Totals Zone -->
		<p:panelGrid>
			<p:row>
				<p:column style="width:30%;">
					<!-- labels title -->
					<h:outputText value="#{craddinvtsklbls.five_step_tot}" style="font-size: 3.5rem; font-weight: bold;" />
				</p:column>
				<p:column>
					<!-- PRE-TAX AMOUNT -->
					<p:outputPanel styleClass="form-group">
						<p:outputLabel styleClass="col-sm-3 control-label text-left" for="amHtIpt" value="#{craddinvtsklbls.five_step_amount_ht}"  style="font-weight: bold;"/>
						<p:outputPanel styleClass="col-sm-4">
							<p:inputNumber id="amHtIpt"
										   value="#{cc.attrs.invoice.invoiceSummary.invoiceTotals.invoiceSubTotal.monetaryAmount}"
										   styleClass="inputRight cosIpt"
										   readonly="true"
										   symbol="#{cc.attrs.taskBean.getCurrencySign(cc.attrs.invoice.invoiceHeader.invoiceCurrency.currencyCoded)}"
										   symbolPosition="s"
										   decimalSeparator=","
										   thousandSeparator="&#160;"
										   placeholder="0,00"/>
						</p:outputPanel>
					</p:outputPanel>
					<!-- VAT AMOUNT -->
					<p:outputPanel styleClass="form-group">
						<p:outputLabel styleClass="col-sm-3 control-label" for="amVatIpt" value="#{craddinvtsklbls.five_step_amount_vat}"  style="font-weight: bold;"/>
						<p:outputPanel styleClass="col-sm-4">
							<p:inputNumber id="amVatIpt"
										   value="#{cc.attrs.invoice.invoiceSummary.invoiceTotals.totalTaxAmount.monetaryAmount.value}"
										   styleClass="inputRight cosIpt"
										   readonly="true"
										   symbol="#{cc.attrs.taskBean.getCurrencySign(cc.attrs.invoice.invoiceHeader.invoiceCurrency.currencyCoded)}"
										   symbolPosition="s"
										   decimalSeparator=","
										   thousandSeparator="&#160;"
										   placeholder="0,00"/>
						</p:outputPanel>
					</p:outputPanel>
					<!-- TOTAL PRICE -->
					<p:outputPanel styleClass="form-group">
						<p:outputLabel styleClass="col-sm-3 control-label" value="#{craddinvtsklbls.five_step_tot_ttc}"  style="font-weight: bold;"/>
						<p:outputPanel styleClass="col-sm-4">
							<p:inputNumber id="invoiceTotal"
										   value="#{cc.attrs.invoice.invoiceSummary.invoiceTotals.invoiceTotal.monetaryAmount.value}"
										   styleClass="inputRight invoiceTotal"
										   readonly="true"
										   symbol="#{cc.attrs.taskBean.getCurrencySign(cc.attrs.invoice.invoiceHeader.invoiceCurrency.currencyCoded)}"
										   symbolPosition="s"
										   decimalSeparator=","
										   thousandSeparator="&#160;"
										   placeholder="0,00"/>
						</p:outputPanel>
					</p:outputPanel>
				</p:column>
			</p:row>
		</p:panelGrid>
		<p:separator style="width:80%;height:0px;margin: 15px auto !important;" />
		<!-- VAT Beakdown Zone -->
		<p:panelGrid>
			<p:row>
				<p:column style="width:30%;">
					<!-- labels title -->
					<h:outputText value="#{craddinvtsklbls.five_step_vat_breakdown}" style="font-size: 3.5rem; font-weight: bold;" />
				</p:column>
				<p:column>
					<p:outputPanel styleClass="col-md-8" rendered="#{not cc.attrs.value.exoTvaChecked}">
						<p:dataTable id="listOfTaxSummary"
									 value="#{cc.attrs.taskBean.vatBreakdownSummaries}"
									 var="tax"
									 emptyMessage="#{labels.no_records_found}"
									 resizableColumns="true">
							<p:column headerText="#{gnxxcblinvlbls.tax_category}">
								<h:outputText value="#{tax.taxCategoryCodedOther.value}" style="float:right;">
									<f:converter converterId="gnxBigDecimalConverter"/>
									<f:attribute name="scale" value="#{cc.attrs.taskBean.amountPrecision}"/>
									<f:attribute name="symbol" value="%"/>
								</h:outputText>
							</p:column>
							<p:column headerText="#{gnxxcblinvlbls.vat_amount}">
								<h:outputText value="#{tax.taxAmountInTaxAccountingCurrency.value}" style="float:right;">
									<f:converter converterId="gnxBigDecimalConverter"/>
									<f:attribute name="scale" value="#{cc.attrs.taskBean.amountPrecision}"/>
									<f:attribute name="symbol" value="#{cc.attrs.taskBean.getCurrencySign(cc.attrs.invoice.invoiceHeader.invoiceCurrency.currencyCoded)}"/>
								</h:outputText>
							</p:column>
						</p:dataTable>
					</p:outputPanel>
				</p:column>
			</p:row>
		</p:panelGrid>
		<p:separator style="width:80%;height:0px;margin: 15px auto !important;" />
		<!-- Tax Beakdown Zone -->
		<p:panelGrid>
			<p:row>
				<p:column style="width:30%;">
					<!-- labels title -->
					<h:outputText value="#{craddinvtsklbls.five_step_tax_breakdown}" style="font-size: 3.5rem; font-weight: bold;" />
				</p:column>
				<p:column>
					<p:outputPanel styleClass="col-md-8" rendered="#{not cc.attrs.value.exoTvaChecked}">
						<p:dataTable id="listOfTaxSummaryByProduct" value="#{cc.attrs.taskBean.taxBreakdownSummaries}"
							var="tax" emptyMessage="#{labels.no_records_found}" resizableColumns="true">
							<p:column headerText="#{craddinvtsklbls.invoice_tax_ean}">
								<h:outputText value="#{tax.taxCategoryCodedOther.value}" style="float:right;"/>
							</p:column>
							<p:column headerText="#{gnxxcblinvlbls.monetary_amount}">
								<h:outputText value="#{tax.taxAmountInTaxAccountingCurrency.value}" style="float:right;">
									<f:converter converterId="gnxBigDecimalConverter"/>
									<f:attribute name="scale" value="#{cc.attrs.taskBean.amountPrecision}"/>
									<f:attribute name="symbol" value="#{cc.attrs.taskBean.getCurrencySign(cc.attrs.invoice.invoiceHeader.invoiceCurrency.currencyCoded)}"/>
								</h:outputText>
							</p:column>
						</p:dataTable>
					</p:outputPanel>
				</p:column>
			</p:row>
		</p:panelGrid>
	</cc:implementation>
</ui:component>