export_list=Export list
error_missing_template=There is no Birt template configured, please contact your administrator.
filter=Filter
actions=Actions on the asn

actions_asnItem=Actions on the asn detail
staggerAction=Stagger

asnHeader_asnNumber=Advance shipment notice number
asnHeader_shippingReferences_shipmentIdentifier_refNum=Delivery note number
asnHeader_asnDates_shipDate=Ship date
asnHeader_asnParty_shipToParty_partyID_ident=Ship to party code
asnHeader_asnParty_shipToParty_nameAddress_city=Ship to party
asnHeader_asnIssueDate=Issue date
status=Status

references=References

asnNumber=Advance shipment notice number
shipDate=Ship date
shippingNumber=Advance shipment notice number
asnIssueDate=Issue date
deliveryPoint=Ship to party
deliveryNumber=Delivery form

asnDetail_postNumber=Post no.
asnDetail_buyerOrderNumber=Order number
asnDetail_purchaseOrderLineItemNumber=Order line number
asnDetail_sellerOrderNumber=Confirmation number
asnHeader_ident=Delivery point
asnHeader_city=Ship to party
asnDetail_itemDescription=Designation
asnDetail_measurementValue=Size
asnDetail_buyerProductNumber=Elis SAP product code
asnDetail_manufacturerProductNumber=Elis laundry product code
asnDetail_sellerProductNumber=Supplier product code
asnDetail_value=Shipped quantity
asnDetail_UOMCoded=Unit of shipped quantity
asnDetail_search=Search...
asnDetail_title=Generation of an advance shipment notice
value_required=Ship to party: Value required

createASN_placeOfDelivery=Ship to party:
createASN_choosePlaceOfDelivery=Choose a location...
createASN_deliveryDate=Ship date:
createASN_deliveryPointOrDeliveryDateErrorMessage=No line found for this delivery location on the indicated date. Thank you for filling an existing date/location couple

createASN_orderNumber=Order number
createASN_confirmationNumber=Confirmation number (buyer)
createASN_supplierProductNumber=Supplier product number
createASN_quantityConfirmed=Quantity confirmed
draft=Save as draft
createASN_deliveryNoteNumberOrCheckboxesErrorMessage=To generate a shipment notice, you must select at least one line and enter a delivery note number

delete_draft_dialog_header=Delete
delete_draft_dialog_content=Are you sure you want to delete this draft shipment notice?
asn_delete_draft_msg=The lines contained in this draft shipment notice can not be seen (delivery date prior to today's date, modified lines, already shipped lines). This draft no longer exists.

asn_save_draft_msg=Shipment notice no. {0} has been saved as a draft
asn_save_draft_msg_changed=Shipment notice no. {0} has been saved as a draft. The previous shipment notice number has already been used.
asn_save_draft_error_msg=An error occurred while saving an advance shipment notice as draft.
confirm_saveASN_draft_dialog_header=Save as draft
confirm_saveASN_draft_dialog_content=Are you sure you want to create a draft shipment notice?
use_draft=Use draft
asn_validate_fail=Unable to create the shipment notice. One or more lines have been modified or have already been shipped
asn_draft_send_msg=Draft no. {0} has already been shipped
asn_draft_updated_msg=Draft shipment notice no. {0} has been updated
asn_draft_shipped_msg=Draft shipment notice no. {0} has been shipped

asn_send_msg=Shipment notice no. {0} was successfully generated
asn_send_msg_changed=Shipment notice no. {0} was successfully generated. The previous shipment notice number has already been used.
asn_send_error_msg=An error occurred while creating an advance shipment notice.
cancel_sendASN_dialog_header=Cancel
confirm_sendASN_dialog_header=Confirm
cancel_sendASN_dialog_content=Are you sure you want to abort?
confirm_sendASN_dialog_content=Do you want to send this dispatch advice?

# Create ASN from Order number
next=Next
step1_error_fill=Please fill in an order number as well as delivery dates
step1_selected_deliveryDates_wrong=Thanks for selecting a delivery date matching the schedule line following the last shipped schedule line. You can select several schedule lines for the same product line
createASN_orderNumber_label=Order number
createASN_chooseOrderNumber=Choose an order number...
createASN_delivery_date=Delivery date:
createASN_chooseDeliveryDates=Choose delivery dates...
dispatch_date_and_place=Dispatch (date and place)
dispatch_order_nr=Dispatch (order)
all_delivery_dates_selected=All dates are selected
delivery_date=Delivery date
delivery_point=Delivery point
delivery_place=Place of delivery
select_asn_error=Please select at least one item
previous_deadline_not_selected=Previous deadline not selected
step1_title=Step 1: Select an order number
step2_title=Step 2: Select the lines to be shipped
step3_title=Step 3: Enter the delivery note number
required_delivery_note=Please fill the delivery form for each dispatch advice
order_number_sent_success=Order no. {0} has been shipped
order_number_sent_partially_success=Order no. {0} has been partially shipped
deadline_must_be_selected={0}: schedule line {1} dated {2} has to be shipped before or at the same time as schedule line {3} dated {4}
order_locked=Impossible to perform this action. Another user is currently doing modifications on those documents
selected_delivery_dates_error=Only the schedule lines following the last shipped schedule line can be shipped
found_date_before_today=Some delivery dates were confirmed on a date before today's date. Those lines will be shippable when you will have change the delivery date in the order confirmation