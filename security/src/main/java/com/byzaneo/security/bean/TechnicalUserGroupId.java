package com.byzaneo.security.bean;

import lombok.*;

import javax.persistence.Embeddable;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import java.io.Serializable;

@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Getter
@Setter
@Embeddable
public class TechnicalUserGroupId implements Serializable {

  private static final long serialVersionUID = 6229359607898900456L;

  @ManyToOne
  @JoinColumn(name = "TECHNICAL_USER_ID")
  private TechnicalUser technicalUser;

  @ManyToOne
  @JoinColumn(name = "GROUP_ID")
  private Group group;

}
