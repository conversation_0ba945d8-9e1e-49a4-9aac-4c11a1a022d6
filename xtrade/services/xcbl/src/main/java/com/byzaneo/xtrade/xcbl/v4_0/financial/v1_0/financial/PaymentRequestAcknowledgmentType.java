//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * acknowledges the receipt of the PaymentRequest.
 * <p>
 * Java class for PaymentRequestAcknowledgmentType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="PaymentRequestAcknowledgmentType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="PaymentRequestAcknHeader" type="{rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd}PaymentRequestAcknHeaderType"/>
 *         &lt;element name="ListOfPaymentRequestAcknDetail" type="{rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd}ListOfPaymentRequestAcknDetailType"/>
 *         &lt;element name="PaymentRequestAcknSummary" type="{rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd}PaymentRequestSummaryType" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PaymentRequestAcknowledgmentType", propOrder = {
    "paymentRequestAcknHeader",
    "listOfPaymentRequestAcknDetail",
    "paymentRequestAcknSummary"
})
public class PaymentRequestAcknowledgmentType implements java.io.Serializable {
  private static final long serialVersionUID = 1L;

  @XmlElement(name = "PaymentRequestAcknHeader", required = true)
  protected PaymentRequestAcknHeaderType paymentRequestAcknHeader;
  @XmlElement(name = "ListOfPaymentRequestAcknDetail", required = true)
  protected ListOfPaymentRequestAcknDetailType listOfPaymentRequestAcknDetail;
  @XmlElement(name = "PaymentRequestAcknSummary")
  protected PaymentRequestSummaryType paymentRequestAcknSummary;

  /**
   * Gets the value of the paymentRequestAcknHeader property.
   * 
   * @return possible object is {@link PaymentRequestAcknHeaderType }
   */
  public PaymentRequestAcknHeaderType getPaymentRequestAcknHeader() {
    return paymentRequestAcknHeader;
  }

  /**
   * Sets the value of the paymentRequestAcknHeader property.
   * 
   * @param value allowed object is {@link PaymentRequestAcknHeaderType }
   */
  public void setPaymentRequestAcknHeader(PaymentRequestAcknHeaderType value) {
    this.paymentRequestAcknHeader = value;
  }

  /**
   * Gets the value of the listOfPaymentRequestAcknDetail property.
   * 
   * @return possible object is {@link ListOfPaymentRequestAcknDetailType }
   */
  public ListOfPaymentRequestAcknDetailType getListOfPaymentRequestAcknDetail() {
    return listOfPaymentRequestAcknDetail;
  }

  /**
   * Sets the value of the listOfPaymentRequestAcknDetail property.
   * 
   * @param value allowed object is {@link ListOfPaymentRequestAcknDetailType }
   */
  public void setListOfPaymentRequestAcknDetail(ListOfPaymentRequestAcknDetailType value) {
    this.listOfPaymentRequestAcknDetail = value;
  }

  /**
   * Gets the value of the paymentRequestAcknSummary property.
   * 
   * @return possible object is {@link PaymentRequestSummaryType }
   */
  public PaymentRequestSummaryType getPaymentRequestAcknSummary() {
    return paymentRequestAcknSummary;
  }

  /**
   * Sets the value of the paymentRequestAcknSummary property.
   * 
   * @param value allowed object is {@link PaymentRequestSummaryType }
   */
  public void setPaymentRequestAcknSummary(PaymentRequestSummaryType value) {
    this.paymentRequestAcknSummary = value;
  }

}
