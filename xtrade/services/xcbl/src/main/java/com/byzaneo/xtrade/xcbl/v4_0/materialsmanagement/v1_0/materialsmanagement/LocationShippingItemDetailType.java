//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfStructuredNoteType;

/**
 * is a collection of items that are grouped by location for shipping purposes.
 * <p>
 * Java class for LocationShippingItemDetailType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="LocationShippingItemDetailType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="BaseShippingDetail" type="{rrn:org.xcbl:schemas/xcbl/v4_0/materialsmanagement/v1_0/materialsmanagement.xsd}BaseShippingDetailType"/>
 *         &lt;element name="ListOfShipScheduleDetail" type="{rrn:org.xcbl:schemas/xcbl/v4_0/materialsmanagement/v1_0/materialsmanagement.xsd}ListOfShipScheduleDetailType"/>
 *         &lt;element name="LineItemNote" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ListOfStructuredNote" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ListOfStructuredNoteType" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "LocationShippingItemDetailType", propOrder = {
    "baseShippingDetail",
    "listOfShipScheduleDetail",
    "lineItemNote",
    "listOfStructuredNote"
})
public class LocationShippingItemDetailType implements java.io.Serializable {
  private static final long serialVersionUID = 1L;

  @XmlElement(name = "BaseShippingDetail", required = true)
  protected BaseShippingDetailType baseShippingDetail;
  @XmlElement(name = "ListOfShipScheduleDetail", required = true)
  protected ListOfShipScheduleDetailType listOfShipScheduleDetail;
  @XmlElement(name = "LineItemNote")
  protected String lineItemNote;
  @XmlElement(name = "ListOfStructuredNote")
  protected ListOfStructuredNoteType listOfStructuredNote;

  /**
   * Gets the value of the baseShippingDetail property.
   * 
   * @return possible object is {@link BaseShippingDetailType }
   */
  public BaseShippingDetailType getBaseShippingDetail() {
    return baseShippingDetail;
  }

  /**
   * Sets the value of the baseShippingDetail property.
   * 
   * @param value allowed object is {@link BaseShippingDetailType }
   */
  public void setBaseShippingDetail(BaseShippingDetailType value) {
    this.baseShippingDetail = value;
  }

  /**
   * Gets the value of the listOfShipScheduleDetail property.
   * 
   * @return possible object is {@link ListOfShipScheduleDetailType }
   */
  public ListOfShipScheduleDetailType getListOfShipScheduleDetail() {
    return listOfShipScheduleDetail;
  }

  /**
   * Sets the value of the listOfShipScheduleDetail property.
   * 
   * @param value allowed object is {@link ListOfShipScheduleDetailType }
   */
  public void setListOfShipScheduleDetail(ListOfShipScheduleDetailType value) {
    this.listOfShipScheduleDetail = value;
  }

  /**
   * Gets the value of the lineItemNote property.
   * 
   * @return possible object is {@link String }
   */
  public String getLineItemNote() {
    return lineItemNote;
  }

  /**
   * Sets the value of the lineItemNote property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setLineItemNote(String value) {
    this.lineItemNote = value;
  }

  /**
   * Gets the value of the listOfStructuredNote property.
   * 
   * @return possible object is {@link ListOfStructuredNoteType }
   */
  public ListOfStructuredNoteType getListOfStructuredNote() {
    return listOfStructuredNote;
  }

  /**
   * Sets the value of the listOfStructuredNote property.
   * 
   * @param value allowed object is {@link ListOfStructuredNoteType }
   */
  public void setListOfStructuredNote(ListOfStructuredNoteType value) {
    this.listOfStructuredNote = value;
  }

}
