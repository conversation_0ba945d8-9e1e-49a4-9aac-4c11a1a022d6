package com.byzaneo.xtrade.xcbl.util.converters;

import org.apache.commons.beanutils.converters.AbstractConverter;

import com.byzaneo.xtrade.xcbl.v4_0.core.core.InvoicePartNumbersType;

public class InvoicePartNumbersTypeConverter extends AbstractConverter {

  @Override
  protected <T> T convertToType(Class<T> type, Object value) throws Throwable {
    return OCRTypeConverter.convertToInvoicePartNumbersType(type, value);
  }

  @Override
  protected Class<?> getDefaultType() {
    return InvoicePartNumbersType.class;
  }

}
