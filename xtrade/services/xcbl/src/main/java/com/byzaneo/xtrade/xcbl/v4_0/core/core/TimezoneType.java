//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.core.core;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * identifies the time zone.
 * <p>
 * Java class for TimezoneType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="TimezoneType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="TimezoneCoded" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}TimezoneCodeType"/>
 *         &lt;element name="TimezoneCodedOther" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TimezoneType", propOrder = {
    "timezoneCoded",
    "timezoneCodedOther"
})
public class TimezoneType implements java.io.Serializable {
  private static final long serialVersionUID = 1L;

  @XmlElement(name = "TimezoneCoded", required = true)
  protected String timezoneCoded;
  @XmlElement(name = "TimezoneCodedOther")
  protected String timezoneCodedOther;

  /**
   * Gets the value of the timezoneCoded property.
   * 
   * @return possible object is {@link String }
   */
  public String getTimezoneCoded() {
    return timezoneCoded;
  }

  /**
   * Sets the value of the timezoneCoded property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setTimezoneCoded(String value) {
    this.timezoneCoded = value;
  }

  /**
   * Gets the value of the timezoneCodedOther property.
   * 
   * @return possible object is {@link String }
   */
  public String getTimezoneCodedOther() {
    return timezoneCodedOther;
  }

  /**
   * Sets the value of the timezoneCodedOther property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setTimezoneCodedOther(String value) {
    this.timezoneCodedOther = value;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + ((timezoneCoded == null) ? 0 : timezoneCoded.hashCode());
    result = prime * result + ((timezoneCodedOther == null) ? 0 : timezoneCodedOther
        .hashCode());
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj)
      return true;
    if (obj == null)
      return false;
    if (getClass() != obj.getClass())
      return false;
    TimezoneType other = (TimezoneType) obj;
    if (timezoneCoded == null) {
      if (other.timezoneCoded != null)
        return false;
    }
    else if (!timezoneCoded.equals(other.timezoneCoded))
      return false;
    if (timezoneCodedOther == null) {
      if (other.timezoneCodedOther != null)
        return false;
    }
    else if (!timezoneCodedOther.equals(other.timezoneCodedOther))
      return false;
    return true;
  }

}
