//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.catalog.v1_0.catalog;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * provides the data type of the category's attribute. Has ScalarType enumerated attribute that indicates whether this data value is a
 * String, an Integer, a Numeric value (such as a floating point number), a Currency (an ISO Currency Code), a Date, or an Enumeration. If
 * an Enumeration, AttributeType has a set of subelements called EnumeratedValue which list the values of the enumeration. Also optionally
 * has MaxSize attribute which indicates the maximum length of the string in characters.
 * <p>
 * Java class for AttributeTypeType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="AttributeTypeType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="EnumeratedValue" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *       &lt;attribute name="ScalarType" type="{rrn:org.xcbl:schemas/xcbl/v4_0/catalog/v1_0/catalog.xsd}ScalarTypeCodeType" default="String" />
 *       &lt;attribute name="MaxSize" type="{http://www.w3.org/2001/XMLSchema}string" />
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AttributeTypeType", propOrder = {
    "enumeratedValue"
})
public class AttributeTypeType implements java.io.Serializable {
  private static final long serialVersionUID = 1L;

  @XmlElement(name = "EnumeratedValue")
  protected List<String> enumeratedValue;
  @XmlAttribute(name = "ScalarType")
  protected ScalarTypeCodeType scalarType;
  @XmlAttribute(name = "MaxSize")
  protected String maxSize;

  /**
   * Gets the value of the enumeratedValue property.
   * <p>
   * This accessor method returns a reference to the live list, not a snapshot. Therefore any modification you make to the returned list
   * will be present inside the JAXB object. This is why there is not a <CODE>set</CODE> method for the enumeratedValue property.
   * <p>
   * For example, to add a new item, do as follows:
   * 
   * <pre>
   * getEnumeratedValue().add(newItem);
   * </pre>
   * <p>
   * Objects of the following type(s) are allowed in the list {@link String }
   */
  public List<String> getEnumeratedValue() {
    if (enumeratedValue == null) {
      enumeratedValue = new ArrayList<String>();
    }
    return this.enumeratedValue;
  }

  /**
   * Gets the value of the scalarType property.
   * 
   * @return possible object is {@link ScalarTypeCodeType }
   */
  public ScalarTypeCodeType getScalarType() {
    if (scalarType == null) {
      return ScalarTypeCodeType.STRING;
    }
    else {
      return scalarType;
    }
  }

  /**
   * Sets the value of the scalarType property.
   * 
   * @param value allowed object is {@link ScalarTypeCodeType }
   */
  public void setScalarType(ScalarTypeCodeType value) {
    this.scalarType = value;
  }

  /**
   * Gets the value of the maxSize property.
   * 
   * @return possible object is {@link String }
   */
  public String getMaxSize() {
    return maxSize;
  }

  /**
   * Sets the value of the maxSize property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setMaxSize(String value) {
    this.maxSize = value;
  }

}
