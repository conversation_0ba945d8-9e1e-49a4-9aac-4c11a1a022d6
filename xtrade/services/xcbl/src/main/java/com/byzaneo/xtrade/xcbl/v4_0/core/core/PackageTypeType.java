//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.core.core;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.CollapsedStringAdapter;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

/**
 * contains information about the specifics of the collection of packages being described by the PackageDetail element. If the collection of
 * packages all contain the same things, and there is no need to identify specifically which packages are contained in which, then only one
 * Package element should be used. If the contents of the package differ or if there needs to be a distinction between which package a sub
 * package is contained in, then multiple Package elements should be used.
 * <p>
 * Java class for PackageTypeType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="PackageTypeType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="PackageTypeCoded" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}PackageTypeCodeType"/>
 *         &lt;element name="PackageTypeCodedOther" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PackageTypeDescription" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PackageTypeType", propOrder = {
    "packageTypeCoded",
    "packageTypeCodedOther",
    "packageTypeDescription"
})
public class PackageTypeType implements java.io.Serializable {
  private static final long serialVersionUID = 1L;

  @XmlElement(name = "PackageTypeCoded", required = true)
  @XmlJavaTypeAdapter(CollapsedStringAdapter.class)
  protected String packageTypeCoded;
  @XmlElement(name = "PackageTypeCodedOther")
  protected String packageTypeCodedOther;
  @XmlElement(name = "PackageTypeDescription")
  protected String packageTypeDescription;

  /**
   * Gets the value of the packageTypeCoded property.
   * 
   * @return possible object is {@link String }
   */
  public String getPackageTypeCoded() {
    return packageTypeCoded;
  }

  /**
   * Sets the value of the packageTypeCoded property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setPackageTypeCoded(String value) {
    this.packageTypeCoded = value;
  }

  /**
   * Gets the value of the packageTypeCodedOther property.
   * 
   * @return possible object is {@link String }
   */
  public String getPackageTypeCodedOther() {
    return packageTypeCodedOther;
  }

  /**
   * Sets the value of the packageTypeCodedOther property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setPackageTypeCodedOther(String value) {
    this.packageTypeCodedOther = value;
  }

  /**
   * Gets the value of the packageTypeDescription property.
   * 
   * @return possible object is {@link String }
   */
  public String getPackageTypeDescription() {
    return packageTypeDescription;
  }

  /**
   * Sets the value of the packageTypeDescription property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setPackageTypeDescription(String value) {
    this.packageTypeDescription = value;
  }

}
