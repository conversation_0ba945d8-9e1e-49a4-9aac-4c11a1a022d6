//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.core.core;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * identifies documents with information related to the hazardous packaging of this order.
 * <p>
 * Java class for AssociatedDocumentsType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="AssociatedDocumentsType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ListOfDocumentLoose" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ListOfPackageDocType" minOccurs="0"/>
 *         &lt;element name="ListOfDocumentAttached" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ListOfPackageDocType" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AssociatedDocumentsType", propOrder = {
    "listOfDocumentLoose",
    "listOfDocumentAttached"
})
public class AssociatedDocumentsType implements java.io.Serializable {
  private static final long serialVersionUID = 1L;

  @XmlElement(name = "ListOfDocumentLoose")
  protected ListOfPackageDocType listOfDocumentLoose;
  @XmlElement(name = "ListOfDocumentAttached")
  protected ListOfPackageDocType listOfDocumentAttached;

  /**
   * Gets the value of the listOfDocumentLoose property.
   * 
   * @return possible object is {@link ListOfPackageDocType }
   */
  public ListOfPackageDocType getListOfDocumentLoose() {
    return listOfDocumentLoose;
  }

  /**
   * Sets the value of the listOfDocumentLoose property.
   * 
   * @param value allowed object is {@link ListOfPackageDocType }
   */
  public void setListOfDocumentLoose(ListOfPackageDocType value) {
    this.listOfDocumentLoose = value;
  }

  /**
   * Gets the value of the listOfDocumentAttached property.
   * 
   * @return possible object is {@link ListOfPackageDocType }
   */
  public ListOfPackageDocType getListOfDocumentAttached() {
    return listOfDocumentAttached;
  }

  /**
   * Sets the value of the listOfDocumentAttached property.
   * 
   * @param value allowed object is {@link ListOfPackageDocType }
   */
  public void setListOfDocumentAttached(ListOfPackageDocType value) {
    this.listOfDocumentAttached = value;
  }

}
