//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.core.core;

import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.XmlValue;

/**
 * specifies the minimum value and its inclusiveness. This is a string content model of a number datatype.
 * <p>
 * Java class for MinimumValueType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="MinimumValueType">
 *   &lt;simpleContent>
 *     &lt;extension base="&lt;http://www.w3.org/2001/XMLSchema>decimal">
 *       &lt;attribute name="SignificanceCoded" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}SignificanceCodeType" />
 *       &lt;attribute name="SignificanceCodedOther" type="{http://www.w3.org/2001/XMLSchema}string" />
 *     &lt;/extension>
 *   &lt;/simpleContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MinimumValueType", propOrder = {
    "value"
})
public class MinimumValueType implements java.io.Serializable {
  private static final long serialVersionUID = 1L;

  @XmlValue
  protected BigDecimal value;
  @XmlAttribute(name = "SignificanceCoded")
  protected SignificanceCodeType significanceCoded;
  @XmlAttribute(name = "SignificanceCodedOther")
  protected String significanceCodedOther;

  /**
   * Gets the value of the value property.
   * 
   * @return possible object is {@link BigDecimal }
   */
  public BigDecimal getValue() {
    return value;
  }

  /**
   * Sets the value of the value property.
   * 
   * @param value allowed object is {@link BigDecimal }
   */
  public void setValue(BigDecimal value) {
    this.value = value;
  }

  /**
   * Gets the value of the significanceCoded property.
   * 
   * @return possible object is {@link SignificanceCodeType }
   */
  public SignificanceCodeType getSignificanceCoded() {
    return significanceCoded;
  }

  /**
   * Sets the value of the significanceCoded property.
   * 
   * @param value allowed object is {@link SignificanceCodeType }
   */
  public void setSignificanceCoded(SignificanceCodeType value) {
    this.significanceCoded = value;
  }

  /**
   * Gets the value of the significanceCodedOther property.
   * 
   * @return possible object is {@link String }
   */
  public String getSignificanceCodedOther() {
    return significanceCodedOther;
  }

  /**
   * Sets the value of the significanceCodedOther property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setSignificanceCodedOther(String value) {
    this.significanceCodedOther = value;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + ((significanceCoded == null) ? 0 : significanceCoded
        .hashCode());
    result = prime * result + ((significanceCodedOther == null) ? 0
        : significanceCodedOther.hashCode());
    result = prime * result + ((value == null) ? 0 : value.hashCode());
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj)
      return true;
    if (obj == null)
      return false;
    if (getClass() != obj.getClass())
      return false;
    MinimumValueType other = (MinimumValueType) obj;
    if (significanceCoded != other.significanceCoded)
      return false;
    if (significanceCodedOther == null) {
      if (other.significanceCodedOther != null)
        return false;
    }
    else if (!significanceCodedOther.equals(other.significanceCodedOther))
      return false;
    if (value == null) {
      if (other.value != null)
        return false;
    }
    else if (!value.equals(other.value))
      return false;
    return true;
  }

}
