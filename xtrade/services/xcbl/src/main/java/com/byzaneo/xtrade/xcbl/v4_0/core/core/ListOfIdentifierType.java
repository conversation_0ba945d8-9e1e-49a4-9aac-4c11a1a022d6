//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.core.core;

import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * contains one or more Identifier elements.
 * <p>
 * Java class for ListOfIdentifierType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ListOfIdentifierType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Identifier" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}IdentifierType" maxOccurs="unbounded"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ListOfIdentifierType", propOrder = {
    "identifier"
})
public class ListOfIdentifierType implements java.io.Serializable {
  private static final long serialVersionUID = 1L;

  @XmlElement(name = "Identifier", required = true)
  protected List<IdentifierType> identifier;

  /**
   * Gets the value of the identifier property.
   * <p>
   * This accessor method returns a reference to the live list, not a snapshot. Therefore any modification you make to the returned list
   * will be present inside the JAXB object. This is why there is not a <CODE>set</CODE> method for the identifier property.
   * <p>
   * For example, to add a new item, do as follows:
   * 
   * <pre>
   * getIdentifier().add(newItem);
   * </pre>
   * <p>
   * Objects of the following type(s) are allowed in the list {@link IdentifierType }
   */
  public List<IdentifierType> getIdentifier() {
    if (identifier == null) {
      identifier = new ArrayList<IdentifierType>();
    }
    return this.identifier;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + ((identifier == null) ? 0 : identifier.hashCode());
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj)
      return true;
    if (obj == null)
      return false;
    if (getClass() != obj.getClass())
      return false;
    ListOfIdentifierType other = (ListOfIdentifierType) obj;
    if (identifier == null) {
      if (other.identifier != null)
        return false;
    }
    else if (!identifier.equals(other.identifier))
      return false;
    return true;
  }

}
