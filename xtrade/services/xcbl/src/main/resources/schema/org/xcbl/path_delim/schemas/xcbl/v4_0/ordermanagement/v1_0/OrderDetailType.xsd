<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/ordermanagement/v1_0/ordermanagement.xsd" xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" xmlns:dgs="http://www.w3.org/2000/09/xmldsig#" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/ordermanagement/v1_0/ordermanagement.xsd" elementFormDefault="qualified">
<xsd:import namespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" schemaLocation="../../core/core.xsd"/>


    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:complexType name="OrderDetailType">
        <xsd:annotation>
            <xsd:documentation>contains the line item and package details of the
        <!--code-->Order<!--/code-->.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="ListOfItemDetail" type="ListOfItemDetailType">
                <xsd:annotation>
                    <xsd:documentation>contains a list of line items for the <!--code-->Order<!--/code-->. </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ListOfPackageDetail" type="core:ListOfPackageDetailType">
                <xsd:annotation>
                    <xsd:documentation>contains a list of package details for the <!--code-->Order<!--/code-->.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
