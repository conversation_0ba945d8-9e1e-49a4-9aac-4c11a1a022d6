<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" elementFormDefault="qualified">
    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleType name="EquipmentStatusCodeType">
        <xsd:annotation>
            <xsd:documentation>This code identifies the status of the equipment. This code list is derived from EDIFACT 8249</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:NMTOKEN">
            <xsd:enumeration value="Other">
                <xsd:annotation>
                    <xsd:documentation>Custom Code</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Continental">
                <xsd:annotation>
                    <xsd:documentation>The equipment is or will be moving across a continent on an intermodal or multimodal basis.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Export">
                <xsd:annotation>
                    <xsd:documentation>Transport equipment to be exported on a marine vessel.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Import">
                <xsd:annotation>
                    <xsd:documentation>Transport equipment to be imported on a marine vessel.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RemainOnBoard">
                <xsd:annotation>
                    <xsd:documentation>Transport equipment arriving on a marine vessel is to remain on board.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Shifter">
                <xsd:annotation>
                    <xsd:documentation>Transport equipment is to be shifted from one stowage location on a marine vessel to another on the same vessel.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Transhipment">
                <xsd:annotation>
                    <xsd:documentation>Transport equipment is to be transferred from one marine vessel to another.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Shortlanded">
                <xsd:annotation>
                    <xsd:documentation>Transport equipment notified to arrive which did not arrive on the means of transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Overlanded">
                <xsd:annotation>
                    <xsd:documentation>Transport equipment not notified to arrive but which did arrive on the means of transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Domestic">
                <xsd:annotation>
                    <xsd:documentation>Transport equipment is used in domestic service.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Positioning">
                <xsd:annotation>
                    <xsd:documentation>Equipment is being transported for positioning purposes.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Delivery">
                <xsd:annotation>
                    <xsd:documentation>Equipment is being delivered.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Redelivery">
                <xsd:annotation>
                    <xsd:documentation>Equipment is being redelivered.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Repair">
                <xsd:annotation>
                    <xsd:documentation>The equipment is for repair.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Reloader">
                <xsd:annotation>
                    <xsd:documentation>Transport equipment to be discharged and subsequently reloaded on the same means of transport but in a different stowage location.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>
</xsd:schema>
