//
// Ce fichier a été généré par l'implémentation de référence JavaTM Architecture for XML Binding (JAXB), v2.2.6 
// Voir <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Toute modification apportée à ce fichier sera perdue lors de la recompilation du schéma source. 
// Généré le : 2013.08.12 à 05:00:38 PM CEST 
//
@XmlSchema(elementFormDefault = XmlNsForm.QUALIFIED, namespace = "http://www.springframework.org/schema/beans", xmlns = {
    @XmlNs(prefix = "", namespaceURI = "http://www.springframework.org/schema/beans"),
    @XmlNs(prefix = "xsi", namespaceURI = "http://www.w3.org/2001/XMLSchema-instance") })
package com.byzaneo.xtrade.ipm.xml.spring;

import javax.xml.bind.annotation.*;

/*
 * , location="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd " +
 * "http://camel.apache.org/schema/spring http://camel.apache.org/schema/spring/camel-spring.xsd"
 */