package com.byzaneo.xtrade.service;

import java.util.List;

import com.byzaneo.security.bean.User;
import com.byzaneo.xtrade.bean.Category;
import com.byzaneo.xtrade.bean.Product;

/**
 * <AUTHOR> Fung <<EMAIL>>
 * @date Mar 4, 2013
 * @company Byzaneo
 * @since XTD-361
 */
public interface ProductService {
  public static final String SERVICE_NAME = "xtdProductService";

  List<Product> getProducts(User user);

  Product saveProduct(Product product);

  List<Product> getProducts(Category category);

  Product getProductByCode(String code);
  // ProductGroup getProductByGroupAndCode(Group group, String code);
}
