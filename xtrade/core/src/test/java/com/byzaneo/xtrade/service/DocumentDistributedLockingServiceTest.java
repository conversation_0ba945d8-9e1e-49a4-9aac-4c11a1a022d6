package com.byzaneo.xtrade.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.slf4j.LoggerFactory.getLogger;

import java.io.File;
import java.util.*;
import java.util.concurrent.*;

import com.byzaneo.commons.exception.ProcessInterruptedException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.springframework.beans.factory.BeanExpressionException;
import org.springframework.test.annotation.Repeat;

import com.byzaneo.xtrade.bean.Document;
import com.hazelcast.core.HazelcastInstance;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> <<EMAIL>>
 */
public class DocumentDistributedLockingServiceTest extends com.hazelcast.jet.core.JetTestSupport {

  private static final Logger log = getLogger(DocumentDistributedLockingServiceTest.class);

  public DocumentDistributedLockingServiceImpl documentService1 = new DocumentDistributedLockingServiceImpl();

  public DocumentDistributedLockingServiceImpl documentService2 = new DocumentDistributedLockingServiceImpl();

  public HazelcastInstance newHazelcastInstance_node1 = createHazelcastInstance();

  public HazelcastInstance newHazelcastInstance_node2 = createHazelcastInstance();

  public ExecutorService executor;

  public File directory = new File("src/test/resources/files");

  @AfterEach
  void resetMap() {
    // Clean up all locks before stopping executor
    try {
      documentService1.unlockAllDocuments();
      documentService2.unlockAllDocuments();
    } catch (Exception e) {
      log.warn("Error during lock cleanup: " + e.getMessage());
    }
    stop(executor);
  }

  @BeforeEach
  void createExecutor() {
    this.executor = Executors.newFixedThreadPool(10);

    documentService1.setHazelcastInstance(newHazelcastInstance_node1);
    documentService2.setHazelcastInstance(newHazelcastInstance_node2);
  }

  @Repeat(value = 5) // Reduced repetitions to reduce flakiness
  @Test
  void unlockConcurrent() throws InterruptedException {
    // Ensure clean state before each iteration
    documentService1.unlockAllDocuments();
    documentService2.unlockAllDocuments();

    // Wait a bit to ensure cleanup is complete
    Thread.sleep(200);

    List<Document> documents = createDocuments(Arrays.asList(1l, 2l, 3l));

    // Use CountDownLatch for better synchronization
    CountDownLatch lockingDone = new CountDownLatch(1);
    CountDownLatch unlockingDone = new CountDownLatch(1);

    Callable<Boolean> thread1 = () -> {
      documentService1.addDocumentLocks(documents);
      lockingDone.countDown(); // Signal that locking is done
      sleep(5);
      documentService1.unlock(documents);
      unlockingDone.countDown(); // Signal that unlocking is done
      return documents.stream()
          .allMatch(doc -> !documentService1.isDocumentLocked(doc));
    };
    Callable<Boolean> thread2 = () -> {
      lockingDone.await(); // Wait for locking to be done
      sleep(1); // Small additional delay to ensure locks are visible
      return documents.stream()
          .allMatch(doc -> documentService1.isDocumentLocked(doc));
    };
    Callable<Boolean> thread3 = () -> {
      unlockingDone.await(); // Wait for unlocking to be done
      sleep(1); // Small additional delay to ensure unlocks are visible
      return documents.stream()
          .allMatch(doc -> !documentService1.isDocumentLocked(doc));
    };

    try {
      List<Future<Boolean>> futures = executor.invokeAll(Arrays.asList(thread1, thread2, thread3));
      for (int i = 0; i < futures.size(); i++) {
        Future<Boolean> future = futures.get(i);
        try {
          boolean result = future.get(10, TimeUnit.SECONDS); // Add timeout
          if (!result) {
            log.error("Thread {} returned false", i + 1);
            // Log the current lock state for debugging
            for (Document doc : documents) {
              log.error("Document {} locked: {}", doc.getId(), documentService1.isDocumentLocked(doc));
            }
          }
          assertTrue(result, "Thread " + (i + 1) + " should return true");
        }
        catch (Exception e) {
          log.error("ERROR in thread {}: {}", i + 1, e);
          fail("Thread " + (i + 1) + " failed with exception: " + e.getMessage());
        }
      }
    } finally {
      // Ensure cleanup after each iteration
      documentService1.unlockAllDocuments();
      documentService2.unlockAllDocuments();
    }
  }

  /**
   * T1->lock(1) (no unlock)</br>
   * T2->sleep(1)->lock(1) (throws and error because 1 is locked by another process)</br>
   */
  @Test
  public void testResourceIsUsedByOtherProcess_Document() throws InterruptedException, ExecutionException {
    final List<Document>[] documents = new List[]{null};
    Assertions.assertDoesNotThrow(() -> documents[0] = createDocuments(Arrays.asList(1l)));
    Callable<Boolean> thread1 = () -> {
      documentService1.addDocumentLocks(documents[0]);
      return true;
    };
    Callable<Boolean> thread2 = () -> {
      sleep(1);
      documentService2.addDocumentLocks(documents[0]);
      return true;
    };
    List<Future<Boolean>> futures = executor.invokeAll(Arrays.asList(thread1, thread2));
    assertTrue(futures.get(0).get());
    Assertions.assertThrows(ExecutionException.class, () -> futures.get(1).get());

  }

  /**
   * T1->lock(1) (no unlock)</br>
   * T2->sleep(1)->lock(1) (throws and error because 1 is locked by another process)</br>
   */
  @Test
  public void testResourceIsUsedByOtherProcess_Directory() throws InterruptedException, ExecutionException {
    Callable<Boolean> thread1 = () -> {
      documentService1.addDirectoryLock(directory, false);
      return true;
    };
    Callable<Boolean> thread2 = () -> {
      sleep(1);
      documentService2.addDirectoryLock(directory, false);
      return true;
    };
    List<Future<Boolean>> futures = executor.invokeAll(Arrays.asList(thread1, thread2));

      assertTrue(futures.get(0).get());
      Assertions.assertThrows(ExecutionException.class, () -> futures.get(1).get());

  }

  @Test
  void addDocumentLocks() {
    this.documentService1.addDocumentLocks(createDocuments(Arrays.asList(1l, 2l, 3l)));

    assertTrue(newHazelcastInstance_node1.getCPSubsystem()
        .getLock("1")
        .isLockedByCurrentThread());
    assertTrue(newHazelcastInstance_node1.getCPSubsystem()
        .getLock("2")
        .isLockedByCurrentThread());
    assertTrue(newHazelcastInstance_node1.getCPSubsystem()
        .getLock("3")
        .isLockedByCurrentThread());

    assertTrue(newHazelcastInstance_node2.getCPSubsystem()
        .getLock("1")
        .isLocked());
    assertTrue(newHazelcastInstance_node2.getCPSubsystem()
        .getLock("2")
        .isLocked());
    assertTrue(newHazelcastInstance_node2.getCPSubsystem()
        .getLock("3")
        .isLocked());

  }

  @Test
  void addDirectoryLocks() {

    this.documentService1.addDirectoryLock(directory, false);
    assertTrue(newHazelcastInstance_node1.getCPSubsystem()
        .getLock(directory.getAbsolutePath())
        .isLockedByCurrentThread());
    assertTrue(newHazelcastInstance_node2.getCPSubsystem()
        .getLock(directory.getAbsolutePath())
        .isLocked());
  }

  @Test
  void addDirectoryLockWithSubdirectories() {

    this.documentService1.addDirectoryLock(directory, true);
    assertTrue(newHazelcastInstance_node1.getCPSubsystem()
        .getLock(directory.getAbsolutePath())
        .isLockedByCurrentThread());
    assertTrue(newHazelcastInstance_node1.getCPSubsystem()
        .getLock(new File(directory, "child-folder").getAbsolutePath())
        .isLockedByCurrentThread());

    assertTrue(newHazelcastInstance_node2.getCPSubsystem()
        .getLock(directory.getAbsolutePath())
        .isLocked());

    assertTrue(newHazelcastInstance_node2.getCPSubsystem()
        .getLock(new File(directory, "child-folder").getAbsolutePath())
        .isLocked());
  }

  @Test
  void removeLocks() {

    List<Document> documents = createDocuments(Arrays.asList(1l, 2l, 3l));
    this.documentService1.addDocumentLocks(documents);
    this.documentService1.unlock(documents);

    assertFalse(newHazelcastInstance_node1.getCPSubsystem()
        .getLock("1")
        .isLockedByCurrentThread());
    assertFalse(newHazelcastInstance_node1.getCPSubsystem()
        .getLock("2")
        .isLockedByCurrentThread());
    assertFalse(newHazelcastInstance_node1.getCPSubsystem()
        .getLock("3")
        .isLockedByCurrentThread());

    assertFalse(newHazelcastInstance_node2.getCPSubsystem()
        .getLock("1")
        .isLocked());
    assertFalse(newHazelcastInstance_node2.getCPSubsystem()
        .getLock("2")
        .isLocked());
    assertFalse(newHazelcastInstance_node2.getCPSubsystem()
        .getLock("3")
        .isLocked());

  }

  @Test
  void removeLocks2() {
    List<Document> documents = createDocuments(Arrays.asList(1l, 2l, 3l));
    this.documentService1.addDocumentLocks(documents);

    this.documentService1.unlockAllDocuments();

    assertFalse(newHazelcastInstance_node1.getCPSubsystem()
        .getLock("1")
        .isLockedByCurrentThread());
    assertFalse(newHazelcastInstance_node1.getCPSubsystem()
        .getLock("2")
        .isLockedByCurrentThread());
    assertFalse(newHazelcastInstance_node1.getCPSubsystem()
        .getLock("3")
        .isLockedByCurrentThread());
  }

  // test with multiple threads, ensure only one thread 'gets' to lock all of them and the others unlock their already locked files
  @Test
  void tryLockFiles() throws InterruptedException, ExecutionException {
    List<File> files = createFileList(100);

    List<Callable<Boolean>> threads = new ArrayList<>();
    for (int i = 0; i < 10; i++) {
      Callable<Boolean> thread = () -> documentService1.tryLock(files);
      threads.add(thread);
    }
    for (int i = 0; i < 10; i++) {
      Callable<Boolean> thread = () -> documentService2.tryLock(files);
      threads.add(thread);
    }

    List<Future<Boolean>> futures = executor.invokeAll(threads);

    Set<Boolean> results = new HashSet<>();
    for (Future<Boolean> future : futures) {

      results.add(future.get());
    }
    assertEquals(2, results.size());
    for (File file : files) {
      assertTrue(documentService1.isFileLocked(file));
    }
  }

  private List<File> createFileList(int size) {
    List<File> fileList = new ArrayList<>();
    for (int i = 0; i < size; i++) {
      fileList.add(new File("test_file_" + i));
    }
    return fileList;
  }

  private List<Document> createDocuments(List<Long> ids) {
    List<Document> documents = new ArrayList<Document>();
    for (Long id : ids) {
      Document document = new Document();
      document.setId(id);
      documents.add(document);
    }
    return documents;
  }

  private static void stop(ExecutorService executor) {
    executor.shutdown();
  }

  private static void sleep(int seconds) {
    try {
      TimeUnit.SECONDS.sleep(seconds);
    }
    catch (InterruptedException e) {
      throw new IllegalStateException(e);
    }
  }
}
