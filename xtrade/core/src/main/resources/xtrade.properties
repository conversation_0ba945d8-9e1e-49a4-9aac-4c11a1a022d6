# -----------------------------------------------------------------------------
#  X T R A D E   P R O P E R T I E S
# Targeted at system administrators, to avoid touching the context XML files.
# -----------------------------------------------------------------------------

# -- T R A N S F O R M --
transform.birt.engine.dir = /C:/dev/util/birt-runtime/ReportEngine
#transform.birt.url = http://localhost:8080/birt/run

# -- W A T C H E R --
process.watch.autostart=false
process.watch.interval=300000
process.watch.time.based=false
process.watch.polling.time.based=false
# comma separated polled directories: [absoluteDirPath]|[processName]
#process.watch.directories=#{input.dir}|input,#{input.dir}/tms|desadv-out 

# -- I N D E X A T I O N --
# - MONGODB -
# The comma delimited list of host:port entries to use for replica set/pairs.
# The name of the database to connect to.
# The MongoClient description.
#index.mongo.description=Mongo Index Operations Client
# The comma delimited list of username:password@database entries 
# to use for authentication. 
# Appending ? uri.authMechanism allows to specify the authentication 
# challenge mechanism.
#index.mongo.uri=
# The number of connections allowed per host. Will block if run out. 
# Default is 10. System property MONGO.POOLSIZE can override
#index.mongo.connectionsPerHost=10
# The minimum number of connections per host. 0 is default and infinite.
#index.mongo.minConnectionsPerHost=0
# The maximum idle time for a pooled connection. 0 is default and infinite.
#index.mongo.maxConnectionIdleTime=0
# The maximum life time for a pooled connection. 0 is default and infinite.
#index.mongo.maxConnectionLifeTime=0
# The connect timeout in milliseconds. 0 is infinite. 
# Default is 10000 ms (10 seconds)
#index.mongo.connectTimeout=10000
# The multiplier for connectionsPerHost for # of threads that can block. 
# Default is 5. If connectionsPerHost is 10, and threadsAllowedToBlockForConnectionMultiplier is 5, 
# then 50 threads can block more than that and an exception will be thrown.
#index.mongo.threadsAllowedToBlockForConnectionMultiplier=5
# The max wait time of a blocking thread for a connection. 
# Default is 180000 ms (3 minutes)
#index.mongo.maxWaitTime=180000
# The keep alive flag, controls whether or not to have socket keep alive timeout. 
# Defaults to false.
#index.mongo.socketKeepAlive=false
# The socket timeout. 0 is default and infinite.
#index.mongo.socketTimeout=0
#index.mongo.readPreference=PRIMARY_PREFERRED
# The connect timeout for connections used for the cluster heartbeat.
# Default 20000 ms (20s)
#index.mongo.heartbeatConnectTimeout=20000
# This is the frequency that the driver will attempt to determine the 
# current state of each server in the cluster.
# Default 10000 ms (10s)
#index.mongo.heartbeatFrequency=10000
# The socket timeout for connections used for the cluster heartbeat.
# Default 20000 ms (20s)
#index.mongo.heartbeatSocketTimeout=20000
# In the event that the driver has to frequently re-check a server's 
# availability, it will wait at least this long since the previous 
# check to avoid wasted effort.
# Default 500 ms (0.5s)
#index.mongo.minHeartbeatFrequency=500