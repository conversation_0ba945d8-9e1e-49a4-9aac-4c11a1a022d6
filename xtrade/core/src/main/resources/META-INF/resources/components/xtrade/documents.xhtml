<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml" xmlns:c="http://java.sun.com/jsp/jstl/core" xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:h="http://xmlns.jcp.org/jsf/html" xmlns:p="http://primefaces.org/ui" xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:xtd="http://xmlns.jcp.org/jsf/composite/components/xtrade"
	xmlns:cc="http://xmlns.jcp.org/jsf/composite">
	<!-- INTERFACE -->
	<cc:interface name="documents">
		<cc:attribute name="documents" required="true" />
		<cc:attribute name="title" default="Documents" />
		<cc:attribute name="renderColumnType" default="false" />
		<cc:attribute name="renderColumnEdit" default="false" />
		<cc:attribute name="documentUrl" default="document.jsf" />
	</cc:interface>
	<!-- IMPLEMENATION -->
	<cc:implementation>
		<p:dataTable id="documents" var="doc" value="#{cc.attrs.documents}" dynamic="false" paginator="true" rows="25" rowsPerPageTemplate="10,25,50"
			paginatorPosition="bottom" paginatorTemplate="{FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}">
			<f:facet name="header">#{cc.attrs.title}</f:facet>
			<p:column id="reference" sortBy="#{doc.reference}" filterBy="#{doc.reference}" headerText="#{xtdlbls.reference}">
				<h:panelGrid styleClass="no-border" columns="2" style="width: auto;" cellpadding="0" cellspacing="0">
					<h:graphicImage value="#{request.contextPath}/javax.faces.resource/document-#{doc.status}16.gif.jsf?ln=images/xtrade" alt="#{doc.status}" />
					<p:commandLink process="@this" update="-#{cc.clientId}-documentPanel" actionListener="#{xtdDocumentHandler.init()}" value="#{doc.reference}"
						oncomplete="PF('wDocumentDialog').show()" styleClass="mls">
						<f:param name="did" value="#{doc.id}" />
					</p:commandLink>
				</h:panelGrid>
			</p:column>
			<p:column id="type" rendered="#{cc.attrs.renderColumnType}" filterBy="#{doc.type}" sortBy="#{doc.type}" headerText="#{xtdlbls.type}">
				<h:outputText value="#{doc.type}" />
			</p:column>
			<p:column id="from" sortBy="#{doc.from}" filterBy="#{doc.from}" headerText="#{xtdlbls.from}">
				<h:outputText value="#{doc.from}" />
			</p:column>
			<p:column id="to" sortBy="#{doc.to}" filterBy="#{doc.to}" headerText="#{xtdlbls.to}">
				<h:outputText value="#{doc.to}" />
			</p:column>
			<p:column id="creationDate" sortBy="#{doc.creationDate}" styleClass="center" headerText="#{comlbls.date}">
				<h:outputText value="#{doc.creationDate}">
					<f:convertDateTime locale="#{gnxSessionHandler.locale}" type="both" dateStyle="medium" timeStyle="short" timeZone="#{secSessionHandler.timeZone}" />
				</h:outputText>
			</p:column>
			<p:column id="edit" rendered="#{cc.attrs.renderColumnEdit}" styleClass="center">
				<h:outputLink value="#{cc.attrs.documentUrl}">
					<f:param name="did" value="#{doc.id}" />
					<f:param name="edit" value="true" />
					<h:outputText value="#{comlbls.edit}" />
				</h:outputLink>
			</p:column>
		</p:dataTable>
		<p:dialog id="documentDialog" widgetVar="wDocumentDialog" position="center" modal="true" width="900" height="500">
			<p:outputPanel id="documentPanel">
				<xtd:document id="document" renderWorkflow="false" />
			</p:outputPanel>
		</p:dialog>
	</cc:implementation>
</ui:component>