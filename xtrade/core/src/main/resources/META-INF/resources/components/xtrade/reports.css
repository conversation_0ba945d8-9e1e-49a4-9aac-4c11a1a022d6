@CHARSET "ISO-8859-1";
.reportOK * {  }
.reportWARN * { color: orange !important;font-weight: bold; }
.reportERROR * { color: red !important;font-weight: bolder; }
.report0 * { color: #999 !important; }

/* Carousel */
#reportCarousel .ui-widget { border: none; }
#reportCarousel TABLE { height: 100%;margin-bottom: -20px;border-color: none; }
#reportCarousel THEAD { display: none; }
#reportCarousel A { text-decoration: none; }

#reportCarousel .ui-paginator { background: none;border: none;} 
#reportCarousel .ui-paginator-prev { float: left;background: none;border: none; } 
#reportCarousel .ui-paginator-next { float: right;background: none;border: none; } 

#reportCarousel .reportCarouselItem { border-color: white !important;text-align: center; }
#reportCarousel .reportCarouselItem .rciTitle { font-size: 18px;font-weight: bold;line-height: 20px;vertical-align: middle;margin: 10px auto; }
#reportCarousel .reportCarouselItem .rciTitle IMG { height: 22px;vertical-align: middle;margin-right: 5px; }
#reportCarousel .reportCarouselItem .rciReportLink { font-size: 13px;margin: 10px auto;white-space: nowrap;font-weight: bold; }
#reportCarousel .reportCarouselItem .rciReportDate { font-size: 13px;margin: 10px auto;white-space: nowrap; }
#reportCarousel .reportCarouselItem .rciReportDetails { color: grey; }
#reportCarousel .reportCarouselItemOK .rciTitle { color: green; }
#reportCarousel .reportCarouselItemOK .rciTitle * { color: green; }
#reportCarousel .reportCarouselItemWARN .rciTitle { color: orange; }
#reportCarousel .reportCarouselItemWARN .rciTitle * { color: orange; }
#reportCarousel .reportCarouselItemERROR .rciTitle { color: red; }
#reportCarousel .reportCarouselItemERROR .rciTitle * { color: red; }