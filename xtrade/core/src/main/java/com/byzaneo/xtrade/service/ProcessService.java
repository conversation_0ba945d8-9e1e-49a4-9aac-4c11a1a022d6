package com.byzaneo.xtrade.service;

import java.io.*;
import java.util.*;
import java.util.concurrent.*;

import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.repository.*;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.transaction.TransactionDefinition;

import com.byzaneo.commons.job.*;
import com.byzaneo.commons.service.*;
import com.byzaneo.xtrade.api.DefinitionDescriptor;
import com.byzaneo.xtrade.api.DeploymentDescriptor;
import com.byzaneo.xtrade.api.ProcessEngine;
import com.byzaneo.xtrade.api.Report;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.DocumentFile;
import com.byzaneo.xtrade.bean.IOFile;
import com.byzaneo.xtrade.dao.DocumentDAO;

public interface ProcessService extends ProcessEngine, Initializable, Disposable {
  static final String SERVICE_NAME = "xtdProcessService";

  static final String PROCESS_DIR_NAME = "process";

  static final String PROCESS_EVENT_TYPE = "process";

  static final String PROCESS_ARCHIVE_EXTENSION = ".bar";

  static final String PROCESS_DEFINITION_EXTENSION = ".bpmn";

  static final String PROCESS_IMAGE_EXTENSION = ".png";

  static final String PROCESS_JOB_GROUP = "PROCESS";

  /** Process' version {@link Comparator} */
  static final Comparator<ProcessDefinition> PROCESS_VERSION_COMPARATOR = new Comparator<ProcessDefinition>() {
    @Override
    public int compare(ProcessDefinition p1, ProcessDefinition p2) {
      return ((Integer) p1.getVersion()).compareTo(p2.getVersion());
    }
  };

  // DEPLOYMENT

  /**
   * @param processDefinition process path in the classpath to deploy.
   * @return the brand-new {@link Deployment} id.
   */
  Deployment deployFromClasspath(String processDefinition);

  /**
   * @param stream containing the data to deploy
   * @param filename Only files with extension {@link #PROCESS_DEFINITION_EXTENSION} or *.?ar (*.bar, *.jar...) are accepted.
   * @return the brand-new {@link Deployment} id.
   */
  Deployment deployStream(InputStream stream, String filename);

  /**
   * @param processFile file to deploy. Only files with extension {@link #PROCESS_DEFINITION_EXTENSION} or *.?ar (*.bar, *.jar...) are
   *          accepted.
   * @return the brand-new {@link Deployment} id.
   */
  Deployment deployFile(File processFile);

  /**
   * Redeploy a process:
   * <ul>
   * <li>Stops scheduling</li>
   * <li>Gets the resources associated to the initial process (create a Zip stream if necessary)</li>
   * <li>{@link #undeploy(ProcessDefinition, boolean, boolean)} keeping scheduled jobs and reports</li>
   * <li>{@link #deployStream(InputStream, String)} the changed process.</li>
   * </ul>
   * 
   * @param document
   * @param resources (optional) if null, retrieves the existing resources
   * @return the brand-new {@link Deployment} id.
   * @since 5.2 XTD-335
   */
  org.w3c.dom.Document saveProcessDocument(org.w3c.dom.Document document, List<IOFile> resources);

  void undeploy(String key);

  void undeployById(String deploymentId, boolean unscheduled, boolean removeReports, boolean removeWatchers);

  void undeploy(String key, boolean unscheduled, boolean removeReports, boolean removeWatchers);

  void undeploy(ProcessDefinition process);

  void undeploy(ProcessDefinition process, boolean removeJob, boolean removeReports);

  boolean isProcessDeployed(String key);

  Date getDeploymentTime(ProcessDefinition pd);

  <J extends Job> J getJob(String processKey);

  /**
   * @param pd
   * @return
   * @throws ServiceException
   * @since 5.2 XTD-338
   */
  File export(ProcessDefinition pd) throws ServiceException;

  // PROCESSES
  ProcessDefinition getProcessDefinition(String key);

  List<ProcessDefinition> getProcessDefinitions();

  List<ProcessInstance> getProcessInstances();

  List<ProcessInstance> getProcessInstances(ProcessDefinition process);

  /**
   * @param key
   * @param definitionId TODO
   * @param excludedSuffixes
   * @return the list of process's resources as {@link IOFile}s
   * @since 5.2 XTD-335
   */
  List<IOFile> getProcessResources(String key, String definitionId, String... excludedSuffixes);

  // EXECUTION

  /**
   * Adds configuration to the current thread
   * 
   * @since 8.0
   */
  void setProcessEngineConfigurationContext();

  /**
   * @param definition to start
   * @return execution report
   * @since 7.0
   */
  Report start(DefinitionDescriptor definition);

  /**
   * @param definition the identifier of the engine process to execute
   * @param execution triggered job execution context
   * @return an execution {@link Report}
   * @see #start(DefinitionDescriptor, Map)
   */
  Report start(DefinitionDescriptor definition, JobExecution execution);

  /** @see #start(String, Map) */
  ProcessInstance start(String key);

  /** @see #start(String, Map) */
  Future<ProcessInstance> startAsync(String key, Map<String, Object> variables);

  /**
   * Runs the process defined by the given key to the end. <br>
   * <br>
   * The process is launched in a Transactional context with the default propagation {@link TransactionDefinition#PROPAGATION_REQUIRED}. So,
   * all the mehtods/tasks called during the process share the same TX (if the TX behaviour is not changed thru the Transactional
   * annotation).
   *
   * @param key name or key of the process to run
   * @param variables
   * @return the {@link ProcessInstance} executed or null if any exception occurred.
   */
  ProcessInstance start(String key, Map<String, Object> variables);

  // HISTORY

  /**
   * @param key process definition key
   * @return the list of {@link HistoryProcessInstance} associated to the given process key.
   */
  List<HistoricProcessInstance> getProcessHistory(String key);

  /** @see DocumentDAO#findReportDocuments(String) */
  List<Document> getReportDocuments(String key, int count);

  // REPORTING

  boolean sendReport(Report report, Map<String, Object> transformParameters, String... notifiedGroupNames);

  /**
   * Create reports instance from the XML {@link DocumentFile} of the given {@link Document} list.
   * 
   * @param doc
   * @return
   */
  List<Report> toReports(List<Document> docs);

  /**
   * Create report instance from the xml file document
   * 
   * @param doc
   * @return
   */
  Report toReport(Document doc);

  List<Document> getDocuments(List<com.byzaneo.xtrade.bean.ReportDocument> reportDocuments);

  void toReportHtml(Report report, Writer out, boolean embedded);

  // DIAGRAM IMAGE

  /**
   * @param process
   * @return {@link ConfigurationService#getFullContextUrl()}+"/resources/tmp/"+{@link #getProcessDefinitionDiagramName(ProcessDefinition)}
   */
  File getProcessDefinitionDiagramFile(ProcessDefinition process);

  /**
   * @param process
   * @return {@link ProcessDefinition#getImageResourceName()} without the parent directories (if any).
   */
  String getProcessDefinitionDiagramName(ProcessDefinition process);

  /**
   * @param process
   * @return the {@link InputStream} of the {@link ProcessDefinition#getImageResourceName()}
   */
  InputStream getProcessDefinitionDiagramStream(ProcessDefinition process);

  // EDITING PROCESS

  /**
   * @param process
   * @return XML document representing the process definition as JPDL (ie. looks for *.bpmn resource name in the given process deployment)
   */
  org.w3c.dom.Document getProcessDefinitionDocument(ProcessDefinition process);

  // JOBS

  <J extends Job> List<J> getJobs();

  /**
   * @param definition to execute as job
   * @param triggers planned to execute the job
   * @return brand new job using {@link #start(DefinitionDescriptor)} method as execution
   * @see #createJob(DefinitionDescriptor, Trigger...)
   */
  <J extends Job> J createJob(ProcessDefinition processDefinition, Trigger... triggers);

  /**
   * @param definition to execute as job
   * @param triggers planned to execute the job
   * @return brand new job using {@link #start(DefinitionDescriptor)} method as execution
   */
  <J extends Job> J createJob(DefinitionDescriptor definition, Trigger... triggers);

  void removeJob(Job job);

  <J extends Job> J saveJob(J job);

  Date getNextExecutionDate(Job job);

  boolean isDeployProcessRunning(String key);

  public void addExecutionListenr(ProcessExecutionListener listenr);

  public void removeExecutionListenr(ProcessExecutionListener listenr);

  /**
   * @return a {@link Set} with the {@link DefinitionDescriptor#getId() Ids} of processes currently running
   */
  Set<String> getRunningProcessesIds();

  /**
   * Suspend a running process
   * <ul>
   * <li>If the process is currently running -> {@link Thread#interrupt() Interrupt it}</li>
   * </ul>
   * 
   * @param descriptor - {@link DeploymentDescriptor descriptor}
   */
  boolean cancelProcessByDeploymentDescriptor(DefinitionDescriptor descriptor, boolean onlyRunningProcess);

  /**
   * @return size of {@link ThreadPoolExecutor#getQueue()}
   */
  int getBlockingQueueSize();

  /**
   * Clears {@link ThreadPoolExecutor#getQueue()}
   */
  void clearBlockingQueue();

  void unlockLockedDocsByEndedProcesses();


}