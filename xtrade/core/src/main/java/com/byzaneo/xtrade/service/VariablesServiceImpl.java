package com.byzaneo.xtrade.service;

import static com.byzaneo.query.builder.Clauses.equal;
import static com.byzaneo.query.builder.Clauses.isNull;
import static com.byzaneo.query.builder.QueryBuilder.createBuilder;
import static com.byzaneo.xtrade.api.Variable.FIELD_CATEGORY;
import static com.byzaneo.xtrade.api.Variable.FIELD_NAME;
import static com.byzaneo.xtrade.api.Variable.FIELD_PRODUCER;
import static com.byzaneo.xtrade.api.Variable.FIELD_SERVICE;
import static java.lang.System.currentTimeMillis;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.util.Assert.notNull;
import static org.thymeleaf.standard.expression.StandardExpressionObjectFactory.AGGREGATES_EXPRESSION_OBJECT_NAME;
import static org.thymeleaf.standard.expression.StandardExpressionObjectFactory.ARRAYS_EXPRESSION_OBJECT_NAME;
import static org.thymeleaf.standard.expression.StandardExpressionObjectFactory.BOOLS_EXPRESSION_OBJECT_NAME;
import static org.thymeleaf.standard.expression.StandardExpressionObjectFactory.CALENDARS_EXPRESSION_OBJECT_NAME;
import static org.thymeleaf.standard.expression.StandardExpressionObjectFactory.DATES_EXPRESSION_OBJECT_NAME;
import static org.thymeleaf.standard.expression.StandardExpressionObjectFactory.LISTS_EXPRESSION_OBJECT_NAME;
import static org.thymeleaf.standard.expression.StandardExpressionObjectFactory.LOCALE_EXPRESSION_OBJECT_NAME;
import static org.thymeleaf.standard.expression.StandardExpressionObjectFactory.MAPS_EXPRESSION_OBJECT_NAME;
import static org.thymeleaf.standard.expression.StandardExpressionObjectFactory.NUMBERS_EXPRESSION_OBJECT_NAME;
import static org.thymeleaf.standard.expression.StandardExpressionObjectFactory.OBJECTS_EXPRESSION_OBJECT_NAME;
import static org.thymeleaf.standard.expression.StandardExpressionObjectFactory.SETS_EXPRESSION_OBJECT_NAME;
import static org.thymeleaf.standard.expression.StandardExpressionObjectFactory.STRINGS_EXPRESSION_OBJECT_NAME;

import java.util.*;

import javax.annotation.*;

import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;
import org.springframework.context.expression.MapAccessor;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.domain.*;
import org.springframework.expression.*;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;
import org.thymeleaf.expression.*;
import org.thymeleaf.expression.Arrays;
import org.thymeleaf.expression.Objects;

import com.byzaneo.commons.bean.LabelSet;
import com.byzaneo.query.Query;
import com.byzaneo.xtrade.*;
import com.byzaneo.xtrade.api.*;

/**
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date Aug 14, 2014
 * @since 7.0 XTD-417
 */
@Service(VariablesService.SERVICE_NAME)
public class VariablesServiceImpl implements VariablesService {
  private static final Logger log = getLogger(VariablesServiceImpl.class);

  // STORAGE

  /** Index storage */
  @Autowired(required = false)
  @Qualifier(DocumentService.INDEX_OPERATIONS_NAME)
  private IndexOperations indexOperations;

  // EXPRESSION

  /** Expression parser context */
  // should we qualified it?
  @Autowired(required = false)
  private ParserContext parserContext;

  /** Expression parser */
  // should we qualified it?
  @Autowired(required = false)
  private ExpressionParser parser;

  /*
   * -- LIFE CYCLE --
   */

  /** @see com.byzaneo.commons.service.Initializable#init() */
  @Override
  @PostConstruct
  public void init() throws Exception {
    final long start = currentTimeMillis();
    log.info("STARTING VARIABLE SERVICE...");

    try {
      log.info("\t- Variables storage: {}", this.getIndexOperations()
          .getName());
    }
    catch (Exception e) {
      log.warn("\t- {}", e.getMessage());
    }

    if (this.parserContext == null)
      this.parserContext = new TemplateParserContext();
    log.info("\t- Parser context: {} (prefix={}, suffix={})",
        this.parserContext.getClass()
            .getName(),
        this.parserContext.getExpressionPrefix(),
        this.parserContext.getExpressionSuffix());

    if (this.parser == null)
      this.parser = new SpelExpressionParser();
    log.info("\t- Expression parser: {}",
        this.parser.getClass()
            .getName());

    log.info("VARIABLE SERVICE STARTED in {}ms.", currentTimeMillis() - start);
  }

  /** @see com.byzaneo.commons.service.Disposable#destroy() */
  @Override
  @PreDestroy
  public void destroy() {
    final long start = currentTimeMillis();
    log.info("STOPPING VARIABLE SERVICE...");
    // closing index operations is delegated to the document service
    log.info("VARIABLE SERVICE STOPPED in {}ms.", currentTimeMillis() - start);
  }

  /*
   * -- MANAGEMENT --
   */

  /**
   * @see com.byzaneo.xtrade.service.VariablesService#create(java.lang.String, com.byzaneo.commons.bean.LabelSet, java.lang.String, String,
   *      java.lang.String[], boolean, com.byzaneo.xtrade.api.VariableValue)
   */
  @Override
  public Variable create(String name, LabelSet label,
      String service, String category, String producer,
      boolean locked, Object value) throws DuplicateException {
    return this.create(new com.byzaneo.xtrade.bean.Variable(
        name, label, // definition
        service, category, producer, // owners
        locked, // access
        value)); // value
  }

  /** @see com.byzaneo.xtrade.service.VariablesService#create(com.byzaneo.xtrade.api.Variable) */
  @Override
  public Variable create(Variable variable) throws DuplicateException {
    return this.saveVariable(variable, false);
  }

  /** @see com.byzaneo.xtrade.service.VariablesService#save(com.byzaneo.xtrade.api.Variable) */
  @Override
  public Variable save(Variable variable) throws DuplicateException {
    return this.saveVariable(variable, true);
  }

  /** @see com.byzaneo.xtrade.service.VariablesService#remove(com.byzaneo.xtrade.api.Variable) */
  @Override
  public void remove(Variable variable) {
    this.getIndexOperations()
        .remove(variable);
  }

  /** @see com.byzaneo.xtrade.service.VariablesService#getVariable(java.lang.String) */
  @Override
  public Variable getVariable(String id) {
    return this.getIndexOperations()
        .findById(com.byzaneo.xtrade.bean.Variable.class, id);
  }

  /** @see com.byzaneo.xtrade.service.VariablesService#getVariable(java.lang.String, java.lang.String, String, java.lang.String) */
  @Override
  public Variable getVariable(String name, String service, String category, String producer) {
    notNull(name, "Searched variable's name is requiered");

    final Page<com.byzaneo.xtrade.bean.Variable> result = this.getIndexOperations()
        .findByQuery(
            com.byzaneo.xtrade.bean.Variable.class,
            createBuilder().and(
                equal(FIELD_NAME, name),
                service == null ? isNull(FIELD_SERVICE) : equal(FIELD_SERVICE, service),
                category == null ? isNull(FIELD_CATEGORY) : equal(FIELD_CATEGORY, category),
                producer == null ? isNull(FIELD_PRODUCER) : equal(FIELD_PRODUCER, producer))
                .query(),
            PageRequest.of(0, 1));

    return result.getTotalElements() == 0 ? null : result.getContent()
        .get(0);
  }

  /*
   * -- SEARCH --
   */
  @Override
  public <R extends Variable> List<R> findByQuery(Class<R> type, Query query, Pageable pageable) {
    final Page<R> result = this.getIndexOperations()
        .findByQuery(type, query, pageable);
    return result.getContent();
  }

  /*
   * -- RESOLUTION --
   */

  /** @see com.byzaneo.xtrade.service.VariablesService#evaluate(String, Class, Locale, Map) */
  @Override
  public <R> R evaluate(final String expression, final Class<R> returnType, final Locale locale, final Map<String, Object> variables)
      throws VariableException {
    notNull(returnType, "Return type is required to parse the expression");
    final Locale l = locale == null ? Locale.getDefault() : locale;

    // - utilities -
    final Map<String, Object> vars = new HashMap<String, Object>(20);
    // international
    vars.put(LOCALE_EXPRESSION_OBJECT_NAME, l);
    vars.put(CALENDARS_EXPRESSION_OBJECT_NAME, new Calendars(l));
    vars.put(DATES_EXPRESSION_OBJECT_NAME, new Dates(l));
    vars.put(NUMBERS_EXPRESSION_OBJECT_NAME, new Numbers(l));
    vars.put(STRINGS_EXPRESSION_OBJECT_NAME, new Strings(l));
    // objects
    vars.put(BOOLS_EXPRESSION_OBJECT_NAME, new Bools());
    vars.put(OBJECTS_EXPRESSION_OBJECT_NAME, new Objects());
    // collections
    vars.put(ARRAYS_EXPRESSION_OBJECT_NAME, new Arrays());
    vars.put(LISTS_EXPRESSION_OBJECT_NAME, new Lists());
    vars.put(SETS_EXPRESSION_OBJECT_NAME, new Sets());
    vars.put(MAPS_EXPRESSION_OBJECT_NAME, new Maps());
    vars.put(AGGREGATES_EXPRESSION_OBJECT_NAME, new Aggregates());

    // - context -
    final StandardEvaluationContext context = new StandardEvaluationContext();
    if (variables != null) {
      vars.putAll(variables);
    }
    // set variables as context root object
    // to allow direct access to the key (ie #{key...}
    // instead of #{#key...} as the context variable
    // should be accessed
    context.setRootObject(vars);
    context.addPropertyAccessor(new MapAccessor());

    // - resolution -
    try {
      return parser.parseExpression(expression, this.parserContext)
          .getValue(context, returnType);
    }
    catch (Exception e) {
      log.error("Error during expression '{}' resolution with variables={}: {}", expression, vars, e.getMessage());
      throw new VariableException(e, "Error during expression evaluation: %s (variables=%s)", expression, vars);
    }
  }

  /*
   * -- PRIVATE --
   */

  private IndexOperations getIndexOperations() {
    if (this.indexOperations == null)
      throw new UnsupportedOperationException("Index operations are not confugured in variable service");
    return this.indexOperations;
  }

  private Variable saveVariable(Variable variable, boolean replace) throws DuplicateException {
    // sanity check
    if (variable == null)
      return null;
    notNull(variable.getName(), "Variable's name is requiered");
    notNull(variable.getValue(), "Variable's value is requiered");

    // removes existing if replace option is true
    // assumes if the variable has an identifier it will
    // automatically updated by the index operations
    if (replace && variable.getId() == null)
      this.getIndexOperations()
          .remove(
              this.getVariable(variable.getName(), variable.getService(), variable.getCategory(), variable.getProducer()));

    // save
    try {
      return this.getIndexOperations()
          .save(variable);
    }
    catch (DuplicateKeyException dke) {
      throw new DuplicateException(dke, "Duplicate variable: %s", variable);
    }
  }
}
