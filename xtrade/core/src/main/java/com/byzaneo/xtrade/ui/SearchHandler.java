package com.byzaneo.xtrade.ui;

import static com.byzaneo.commons.ui.util.JSFHelper.getManagedBean;
import static com.byzaneo.commons.ui.util.JSFHelper.getSpringBean;
import static com.byzaneo.commons.ui.util.JSFHelper.toSelectItems;
import static com.byzaneo.commons.ui.util.MessageHelper.error;
import static com.byzaneo.commons.util.DatePeriodHelper.DatePeriodStep.MONTH;
import static com.byzaneo.commons.util.DatePeriodHelper.DatePeriodStep.YEAR;
import static com.byzaneo.security.util.ResourceHelper.generateId;
import static com.byzaneo.xtrade.dao.DocumentDAO.DocumentField.creationDate;
import static com.byzaneo.xtrade.ui.SearchHandler.MANAGED_BEAN_NAME;
import static java.util.Arrays.asList;

import java.io.Serializable;
import java.util.*;

import javax.annotation.PostConstruct;
import javax.faces.bean.*;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;

import org.slf4j.*;
import org.springframework.data.domain.*;
import org.springframework.data.domain.Sort.Direction;

import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.dao.DataAccessException;
import com.byzaneo.commons.util.StringHelper;
import com.byzaneo.security.api.Right;
import com.byzaneo.security.ui.handler.SessionHandler;
import com.byzaneo.xtrade.api.DocumentStatus;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.service.DocumentService;

/**
 * <AUTHOR> ROSSI
 * @company Byzaneo
 */
@ManagedBean(name = MANAGED_BEAN_NAME)
@SessionScoped
public class SearchHandler implements Serializable {
  private static final long serialVersionUID = 4984937979018639403L;
  private static final Logger log = LoggerFactory.getLogger(SearchHandler.class);

  public static final String MANAGED_BEAN_NAME = "xtdSearchHandler";

  transient private DocumentService documentService;

  private String reference = null;
  private DocumentStatus status = null;
  private String from = null;
  private List<SelectItem> fromItems = null;
  private String to = null;
  private List<SelectItem> toItems = null;
  private String docType = null;
  private List<SelectItem> docTypeItems = null;
  private DatePeriod period = null;
  private FileType fileType;
  private List<FileType> fileTypes;
  /** Checks if there is at least one file associated to the returned documents */
  private boolean noFileCheck = false;

  private Page<Document> documentPage;
  private List<String> userGroups;
  private boolean admin;

  public SearchHandler() {
    super();
  }

  @PostConstruct
  public void init() {
    this.documentService = getSpringBean(DocumentService.class, DocumentService.SERVICE_NAME);
    // Security
    SessionHandler session = getManagedBean(SessionHandler.class, SessionHandler.MANAGED_BEAN_NAME);
    this.admin = session.isAdministrator();
    this.userGroups = session.getUserGroupNames();
    // Period
    this.period = new DatePeriod(MONTH, 0);
    // FileTypes
    if (!this.admin) {
      this.fileTypes = new ArrayList<FileType>();
      for (FileType type : FileType.values())
        if (session.isGranted(generateId(FileType.class, type.toString()), Right.READ))
          this.fileTypes.add(type);
    }
    else {
      this.fileTypes = asList(FileType.values());
    }
    // criterion values
    this.fromItems = toSelectItems(this.documentService.getDocumentFroms());
    this.toItems = toSelectItems(this.documentService.getDocumentTos());
    this.docTypeItems = toSelectItems(this.documentService.getDocumentTypes());
  }

  public void reset() {
    this.docType = null;
    this.documentPage = null;
    this.fileType = null;
    this.from = null;
    this.reference = null;
    this.period = new DatePeriod(MONTH, 0);
    this.status = null;
    this.to = null;
  }

  /*
   * EVENTS
   */

  public void onSearch(ActionEvent event) {
    this.documentPage = this.searchDocuments();
  }

  public String onQuickSearch() {
    // reset search fields...
    this.docType = null;
    this.fileType = null;
    this.from = null;
    this.status = null;
    this.to = null;
    // set search period to 1 year...
    this.period = new DatePeriod(YEAR, 0);

    this.documentPage = this.searchDocuments();

    return "search";
  }

  /*
   * EL EVENT
   */

  public List<Document> getLatestDocuments(String from, String to, String doctype) {
    try {
      return this.documentService.getDocuments(Document.class,
          PageRequest.of(0, 100, Direction.DESC, creationDate.toString()),
          this.admin ? null : this.userGroups, null,
          StringHelper.trimToNull(doctype), StringHelper.trimToNull(from), StringHelper.trimToNull(to),
          !this.admin, null, null, null)
          .getContent();
    }
    catch (Exception e) {
      log.error("Latest Document Search failed", e);
      return Collections.emptyList();
    }
  }

  /*
   * PRIVATE
   */

  private Page<Document> searchDocuments() {
    try {
      final List<FileType> ftypes = (this.fileType == null ? (noFileCheck ? null : this.fileTypes) : Arrays.asList(fileType));
      return this.documentService.getDocuments(Document.class,
          PageRequest.of(0, 500, Direction.DESC, creationDate.toString()),
          this.admin ? null : this.userGroups, ftypes, docType, from, to, !this.admin, reference,
          this.period.getStartDate(), period.getEndDate(), new DocumentStatusEntity(this.status));
    }
    catch (DataAccessException e) {
      error(e.getLocalizedMessage());
      log.error("Search failed", e);
      this.documentPage = null;
      return new PageImpl<Document>(Collections.<Document> emptyList());
    }
  }

  /*
   * ACCESSORS
   */

  public boolean isRendered() {
    return this.getGrantedTypes()
        .size() > 0;
  }

  public Date getStart() {
    return this.period.getStartDate();
  }

  public void setStart(Date fromDate) {
    this.period.setStartDate(fromDate);
  }

  public Date getEnd() {
    return this.period.getEndDate();
  }

  public void setEnd(Date toDate) {
    this.period.setEndDate(toDate);
  }

  public String getReference() {
    return reference;
  }

  public void setReference(String reference) {
    this.reference = StringHelper.trimToNull(reference);
  }

  public String getStatus() {
    return status == null ? "" : this.status.toString();
  }

  public void setStatus(String status) {
    this.status = StringHelper.isBlank(status) ? null : DocumentStatus.valueOf(status);
  }

  public String getFrom() {
    return from;
  }

  public void setFrom(String from) {
    this.from = StringHelper.trimToNull(from);
  }

  public List<SelectItem> getFromItems() {
    return fromItems;
  }

  public String getTo() {
    return to;
  }

  public void setTo(String to) {
    this.to = StringHelper.trimToNull(to);
  }

  public List<SelectItem> getToItems() {
    return toItems;
  }

  public String getDocType() {
    return docType;
  }

  public void setDocType(String docType) {
    this.docType = StringHelper.trimToNull(docType);
  }

  public List<SelectItem> getDocTypeItems() {
    return docTypeItems;
  }

  public String getFileType() {
    return fileType == null ? "" : this.fileType.toString();
  }

  public void setFileType(String fileType) {
    this.fileType = StringHelper.isBlank(fileType) ? null : FileType.valueOf(fileType);
  }

  public List<FileType> getFileTypes() {
    return fileTypes;
  }

  public Page<Document> getDocuments() {
    return documentPage;
  }

  public List<FileType> getGrantedTypes() {
    return this.fileTypes;
  }

  public boolean isNoFileCheck() {
    return noFileCheck;
  }

  public void setNoFileCheck(boolean noFileCheck) {
    this.noFileCheck = noFileCheck;
  }
}
