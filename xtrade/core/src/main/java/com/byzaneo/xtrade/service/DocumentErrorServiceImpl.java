package com.byzaneo.xtrade.service;

import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.dao.DocumentErrorDAO;
import com.byzaneo.xtrade.util.DocumentErrorHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.slf4j.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.nio.charset.Charset;
import java.util.*;

import static com.byzaneo.xtrade.util.DocumentErrorHelper.escapeQuotes;

@Service(DocumentErrorService.SERVICE_NAME)
public class DocumentErrorServiceImpl implements DocumentErrorService {

  private static final Logger log = LoggerFactory.getLogger(DocumentErrorServiceImpl.class);

  @Autowired
  @Qualifier(DocumentErrorDAO.DAO_NAME)
  private DocumentErrorDAO documentErrorDao;

  @Autowired(required = false)
  @Qualifier(DocumentErrorTranslationService.SERVICE_NAME)
  private transient DocumentErrorTranslationService documentErrorTranslationService;

  @Override
  @Transactional
  public void save(DocumentError documentError) {
    documentErrorDao.store(documentErrorDao.merge(documentError));
  }

  @Override
  @Transactional(readOnly = true)
  public List<DocumentError> findErrors(Document document) {
    return documentErrorDao.findAllErrors(document);
  }

  @Override
  public File createFileFromDBErrors(Document document) {
    File file = new File(document
        .getReference() + ".err");
    try {
      List<DocumentError> documentErrors = findErrors(document);
      if (CollectionUtils.isEmpty(documentErrors))
        return null;
      FileUtils.writeStringToFile(file, getDocumentErrorsAsJson(documentErrors), Charset.defaultCharset());
    }
    catch (IOException e) {
      log.error("Cannot create error file: {}", e);
    }
    return file;
  }

  @Override
  @Transactional(readOnly = true)
  public String getDocumentErrorsAsJson(List<DocumentError> documentErrors) {
    if (documentErrors.isEmpty())
      return "{}";
    Map<DocumentError, Map<Locale, String>> allDocumentErrors = new HashMap<>();
    for (DocumentError documentError : documentErrors) {
      String defaultLabel = documentError.getDefaultLabel();
      if (defaultLabel != null && defaultLabel.contains("\""))
        documentError.setDefaultLabel(escapeQuotes(defaultLabel));
      Map<Locale, String> translations = new HashMap<>();
      for (Map.Entry<Locale, String> translation : documentErrorTranslationService.getLabelsForCode(documentError.getErrorCode())
          .entrySet())
        translations.put(translation.getKey(),
            DocumentErrorHelper.formatErrorMessage(translation.getValue(), documentError.getErrorParams()));
      allDocumentErrors.put(documentError, translations);
    }
    return DocumentErrorHelper.convertDocumentErrorsToJson(allDocumentErrors);
  }

  @Override
  @Transactional
  public void storeAll(List<DocumentError> documentErrors) {
    documentErrorDao.storeAll(documentErrors);
  }

  @Override
  public int deleteAllDocumentErrors(Collection<Long> docIds) {
    return documentErrorDao.removeErrors(docIds);
  }

}
