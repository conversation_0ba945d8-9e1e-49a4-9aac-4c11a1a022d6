package com.byzaneo.xtrade.process.task;

import com.byzaneo.commons.test.SystemPropertyContextLoader;
import com.byzaneo.xtrade.test.TestProcessService;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import static com.byzaneo.xtrade.test.TestProcessService.getProcessExtension;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date 23 oct. 2009
 */
@ExtendWith(SpringExtension.class)
@ContextConfiguration(locations = { "classpath:/xtrade-test.beans.xml" }, loader = SystemPropertyContextLoader.class)
class DocumentSetTaskIndexITCase {

  @Autowired
  TestProcessService testProcessService;

  @BeforeAll
  public static void beforeAll(@Autowired TestProcessService testProcessService) throws Exception {
    testProcessService.beforeAll(getProcessPath());
  }

  @AfterAll
  public static void afterAll(@Autowired TestProcessService testProcessService) throws Exception {
    testProcessService.afterAll(getProcessKey());
  }

  private static String getProcessPath() {
    return DocumentSetTaskIndexITCase.class.getName()
        .replace('.', '/') + getProcessExtension();
  }

  private static String getProcessKey() {
    return DocumentSetTaskIndexITCase.class.getSimpleName();
  }

  @Test
  public void testStart() throws Exception {
    assertTrue(testProcessService.start(getProcessKey())
        .isEnded());
  }
}
