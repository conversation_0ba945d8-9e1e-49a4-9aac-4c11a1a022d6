package com.byzaneo.xtrade.broker.config;

import com.byzaneo.xtrade.broker.bean.*;
import com.byzaneo.xtrade.broker.bean.invoice.create.InvoiceCreateMessage;
import com.byzaneo.xtrade.broker.bean.invoice.status.InvoiceStatusMessage;
import com.byzaneo.xtrade.broker.handler.MessageTypeHandler;
import com.byzaneo.xtrade.broker.listener.*;
import com.byzaneo.xtrade.broker.publisher.RabbitMQPublisher;
import com.byzaneo.xtrade.broker.service.MessageBrokerService;
import org.slf4j.*;
import org.springframework.amqp.core.*;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.*;
import org.springframework.amqp.rabbit.core.*;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.*;

import java.util.*;

@Configuration
@PropertySource(value = "classpath:gnx.properties", ignoreResourceNotFound = true)
public class RabbitMQConfig {

  private static final Logger logger = LoggerFactory.getLogger(RabbitMQConfig.class);
  public static final String ENVIRONMENT_ID = "environment-id";
  public static final String MESSAGE_TYPE = "message-type";
  public static final String PROCESS_EXECUTE = "process.execute";
  public static final String INVOICE_CREATE = "invoice.create";
  public static final String INVOICE_STATUS = "invoice.status";
  public static final String MESSAGE_HANDLER_SUFFIX = "_handler";
  public static final String MESSAGE_CONVERTER_SUFFIX = "_converter";
  public static final String DOCUMENTS = "documents";
  public static final String BROKER = "broker";
  public static final String CDV = "cdv";
  public static final String INVOICE = "invoice";
  public static final String DEFAULT = "default";

  @Value("${broker.enabled:false}")
  private boolean brokerEnabled;

  @Value("${broker.instance.host:localhost}")
  private String instanceHost;

  @Value("${broker.instance.port:5672}")
  private int instancePort;

  @Value("${broker.instance.username:guest}")
  private String instanceUsername;

  @Value("${broker.instance.password:guest}")
  private String instancePassword;

  @Value("${broker.instance.virtualHost:/}")
  private String instanceVirtualHost;

  @Value("${broker.instance.exchange:aio_exchange}")
  private String instanceExchange;

  @Value("${broker.instance.queue.process:process_queue}")
  private String processQueue;

  @Value("${broker.instance.queue.invoice:aio_invoice_queue}")
  private String invoiceQueue;

  @Value("${broker.instance.aio.prefetch_count:10}")
  private int preFetchCount;

  @Value("${broker.instance.aio.batch.batchSize:10}")
  private int batchSize;

  @Value("${broker.instance.aio.batch.batchReceiveTimeout:30000}")
  private long batchReceiveTimeout;

  @Bean
  @Conditional(BrokerEnabledCondition.class)
  public ConnectionFactory connectionFactory() {
    CachingConnectionFactory connectionFactory = new CachingConnectionFactory(instanceHost, instancePort);
    connectionFactory.setUsername(instanceUsername);
    connectionFactory.setPassword(instancePassword);
    connectionFactory.setVirtualHost(instanceVirtualHost);
    return connectionFactory;
  }

  @Bean
  @Conditional(BrokerEnabledCondition.class)
  public RabbitAdmin rabbitAdmin(ConnectionFactory connectionFactory) {
    return new RabbitAdmin(connectionFactory);
  }

  @Bean("aioExchange")
  @Conditional(BrokerEnabledCondition.class)
  public HeadersExchange aioExchange() {
    return new HeadersExchange(instanceExchange, true, false);
  }

  @Bean("processQueue")
  @Conditional(BrokerEnabledCondition.class)
  public Queue processQueue() {
    return new Queue(processQueue, true, false, false);
  }

  @Bean("invoiceQueue")
  @Conditional(BrokerEnabledCondition.class)
  public Queue invoiceQueue() {
    return new Queue(invoiceQueue, true, false, false);
  }

  @Bean("processBinding")
  @Conditional(BrokerEnabledCondition.class)
  public Binding processBinding(HeadersExchange aioExchange, Queue processQueue) {
    return BindingBuilder.bind(processQueue)
        .to(aioExchange)
        .where(MESSAGE_TYPE).matches(PROCESS_EXECUTE);
  }

  @Bean("invoiceBinding")
  @Conditional(BrokerEnabledCondition.class)
  public Binding invoiceBinding(HeadersExchange aioExchange, Queue invoiceQueue) {
    return BindingBuilder.bind(invoiceQueue)
        .to(aioExchange)
        .where(MESSAGE_TYPE).matches(INVOICE_CREATE);
  }

  @Bean
  @Conditional(BrokerEnabledCondition.class)
  public SimpleMessageListenerContainer processMessageListenerContainer(ConnectionFactory connectionFactory, ProcessQueueListener processQueueListener) {
    SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setConcurrentConsumers(1); // Only 1 consumer
    container.setMaxConcurrentConsumers(1); // Max 1 consumer
    container.setPrefetchCount(1);
    container.setQueues(processQueue());
    container.setMessageListener(new MessageListenerAdapter(processQueueListener)); // Assuming same listener
    return container;
  }

  @Bean
  @Conditional(BrokerEnabledCondition.class)
  public SimpleMessageListenerContainer invoiceMessageListenerContainer(ConnectionFactory connectionFactory, InvoiceCreateQueueListener invoiceCreateQueueListener) {
    SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setConcurrentConsumers(1); // Only 1 consumer
    container.setMaxConcurrentConsumers(1); // Max 1 consumer
    container.setConsumerBatchEnabled(true);
    container.setPrefetchCount(preFetchCount);
    container.setBatchSize(batchSize);
    container.setReceiveTimeout(batchReceiveTimeout);
    container.setQueues(invoiceQueue());
    container.setMessageListener(invoiceCreateQueueListener); // Assuming same listener
    return container;
  }

  @Bean
  @Conditional(BrokerEnabledCondition.class)
  public InvoiceCreateQueueListener invoiceCreateListener(MessageBrokerService messageBrokerService, RabbitMQPublisher rabbitMQPublisher,
      Jackson2JsonMessageConverter jackson2JsonMessageConverter,
      Map<String, MessageTypeHandler> messageTypeHandlers) {
    return new InvoiceCreateQueueListener(messageBrokerService, rabbitMQPublisher, jackson2JsonMessageConverter, messageTypeHandlers);
  }

  @Bean
  @Conditional(BrokerEnabledCondition.class)
  public ProcessQueueListener processQueueListener(MessageBrokerService messageBrokerService,
      Jackson2JsonMessageConverter jackson2JsonMessageConverter) {
    return new ProcessQueueListener(messageBrokerService, jackson2JsonMessageConverter);
  }

  @Bean
  public GnxJackson2JavaTypeMapper gnxJackson2JavaTypeMapper() {
    GnxJackson2JavaTypeMapper mapper = new GnxJackson2JavaTypeMapper();
    mapper.setTrustedPackages("com.byzaneo.xtrade.broker.bean.*");
    Map<String, Class<?>> idClassMapping = new HashMap<>();
    idClassMapping.put("invoice.create", InvoiceCreateMessage.class);
    idClassMapping.put("invoice.status", InvoiceStatusMessage.class);
    idClassMapping.put("process.execute", ProcessExecutionMessage.class);
    mapper.setIdClassMapping(idClassMapping);

    return mapper;
  }

  @Bean
  public Jackson2JsonMessageConverter jackson2JsonMessageConverter() {
    Jackson2JsonMessageConverter converter = new Jackson2JsonMessageConverter();
    converter.setClassMapper(gnxJackson2JavaTypeMapper());
    return converter;
  }

  @Bean
  @Conditional(BrokerEnabledCondition.class)
  public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
    return new RabbitTemplate(connectionFactory);
  }

  @Bean
  @Conditional(BrokerEnabledCondition.class)
  public RabbitMQPublisher rabbitMQPublisher(RabbitTemplate rabbitTemplate, Jackson2JsonMessageConverter jackson2JsonMessageConverter) {
    return new RabbitMQPublisher(rabbitTemplate, jackson2JsonMessageConverter, processQueue());
  }
}
