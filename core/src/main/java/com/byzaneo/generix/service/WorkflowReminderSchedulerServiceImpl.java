package com.byzaneo.generix.service;

import static org.slf4j.LoggerFactory.getLogger;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.quartz.*;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.Service;

import com.byzaneo.commons.util.SpringContextHelper;
import com.byzaneo.generix.bean.Instance;
import com.byzaneo.security.bean.Company;
import com.byzaneo.security.service.SecurityService;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.service.*;

/**
 * Service that deals with scheduling jobs for sending reminders
 * <AUTHOR> <<EMAIL>>
 *
 */
@Service(WorkflowReminderSchedulerService.SERVICE_NAME)
public class WorkflowReminderSchedulerServiceImpl implements WorkflowReminderSchedulerService {

  private static final Logger log = getLogger(WorkflowReminderSchedulerServiceImpl.class);
  
  private static final String JOB_KEY_PREFIX = "WORKFLOW-REMINDER_";
  public static final String INSTANCE = "INSTANCE";
  public static final String WKF_STEP = "WKF_STEP";
  
  
  @Autowired(required = false)
  @Qualifier("comSchedulerFactory")
  private Scheduler scheduler;
  
  @Autowired
  @Qualifier(WorkflowService.SERVICE_NAME)
  private WorkflowService workflowService;
  
  @Autowired
  @Qualifier(SecurityService.SERVICE_NAME)
  protected SecurityService securityService;
  
  @Autowired
  @Qualifier(InstanceService.SERVICE_NAME)
  private InstanceService instanceService;
  
  
  /**
   * When the server is restarted, the triggers should be relaunched
   */
  @Override
  @PostConstruct
  public void init() throws SchedulerException {
    List<Workflow> workflows = workflowService.getWorkflowsForWhichToScheduleReminders();
    for (Workflow workflow : workflows) {
      Company company = securityService.getCompanyByCode(workflow
          .getOwners());
      Instance instance = company != null ? instanceService.getInstanceByCode(company.getName()) : null;
      scheduleJob(workflow, instance);
    }
  }
  
  /**
   *  Schedule a job with the set of associated triggers for each individual workflow
   */
  @Override
  public void scheduleJob(Workflow workflow, Instance instance)
      throws SchedulerException {
    JobDetail jobDetail = createWorkflowJob(workflow, instance);
    Set<Trigger> triggers = createTriggers(jobDetail, workflow.getWorkflowSteps());
    this.scheduler.scheduleJob(jobDetail, triggers, true);
  }
  
  @Override
  public void deleteJob(Workflow workflow) throws SchedulerException {
    this.scheduler.deleteJob(new JobKey(JOB_KEY_PREFIX + workflow.getId()));
  }
  
  public JobDetail createWorkflowJob(Workflow workflow, Instance instance) throws SchedulerException {
    JobKey jobKey = new JobKey(JOB_KEY_PREFIX + workflow.getId());
    
    if (scheduler.checkExists(jobKey)) {
      scheduler.deleteJob(jobKey);
    }
    final JobDataMap dmap = new JobDataMap();
    dmap.put(INSTANCE, instance);

    JobDetail jobDetail = JobBuilder.newJob(MethodInvokingJob.class)
        .withIdentity(jobKey)
        .storeDurably(true)
        .usingJobData(dmap)
        .build();
    this.scheduler.addJob(jobDetail, true);
    
    return jobDetail;
  }
  
  /**
   * For each step in a workflow, a trigger is created that will be launched according to the cron expression set in the step
   */
  public Set<Trigger> createTriggers(JobDetail job, List<WorkflowStep> workflowSteps) {
    Set<Trigger> triggers = new HashSet<>();
    List<WorkflowStep> steps = workflowSteps.stream()
        .filter(ws -> ws.getReminderTemplate() != null)
        .collect(Collectors.toList());
    for (WorkflowStep step : steps) {
      int numStep = step.getId()
          .getNumStep();
      int wkfId = step.getId()
          .getWorkflow();
      TriggerKey triggerKey = new TriggerKey(wkfId + "_" + numStep);
      Trigger trigger = TriggerBuilder
          .newTrigger()
          .forJob(job)
          .withIdentity(triggerKey)
          .withSchedule(CronScheduleBuilder.cronSchedule(step.getReminderFrequency()))
          .build();
      triggers.add(trigger);
      job.getJobDataMap()
          .put(WKF_STEP + "_" + triggerKey.getName(), step);
    }
    return triggers;
  }
  
  /**
   * Quartz Job implementation that invokes a specified method.
   */
  @DisallowConcurrentExecution
  @PersistJobDataAfterExecution
  public static class MethodInvokingJob implements org.quartz.Job {

    /** @see org.quartz.Job#execute(org.quartz.JobExecutionContext) */
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
      try {
        Map<String, Object> parameters = context.getJobDetail()
            .getJobDataMap()
            .getWrappedMap();
        Instance instance = (Instance) parameters.get(INSTANCE);
        WorkflowStep workflowStep = (WorkflowStep) parameters.get(WKF_STEP + "_" + context.getTrigger().getKey().getName());
        SpringContextHelper.getBean(WorkflowReminderService.class, WorkflowReminderService.SERVICE_NAME)
            .sendWorkflowReminders(instance, workflowStep);
      }
      catch (Exception ex) {
        log.error("An error occurred while launching workflow reminders job: ", ex);
      }
    }
  }
  
}
