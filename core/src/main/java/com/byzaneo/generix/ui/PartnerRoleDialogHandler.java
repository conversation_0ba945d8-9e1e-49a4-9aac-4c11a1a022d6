package com.byzaneo.generix.ui;

import static com.byzaneo.commons.ui.util.JSFHelper.getRequestParameter;
import static com.byzaneo.commons.ui.util.JSFHelper.getSpringBean;
import static java.lang.Boolean.TRUE;
import static java.util.Arrays.asList;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.primefaces.context.RequestContext.getCurrentInstance;

import java.io.Serializable;
import java.util.*;

import javax.annotation.*;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;

import org.apache.commons.lang3.StringUtils;

import com.byzaneo.commons.util.Assert;
import com.byzaneo.generix.service.SecurityService;
import com.byzaneo.generix.util.RoleHelper;
import com.byzaneo.security.bean.*;
import com.byzaneo.task.ui.ClosableDialog;
import com.google.common.collect.*;

/**
 * Used to manage the role through the PrimeFaces dialog framework.
 * 
 * <AUTHOR> Pirjolea <<EMAIL>>
 */
@ManagedBean(name = PartnerRoleDialogHandler.MANAGED_BEAN_NAME)
@ViewScoped
public class PartnerRoleDialogHandler implements Serializable, ClosableDialog {

  private static final long serialVersionUID = 8125352209363200452L;

  public static final String MANAGED_BEAN_NAME = "gnxPartnerRoleDialogHandler";

  /** Deploy dialog component options */
  public static final Map<String, Object> PERMISSION_DIALOG_OPTIONS = ImmutableMap.<String, Object> builder()
      .put("modal", true)
      .put("draggable", true)
      .put("resizable", true)
      .put("width", 790)
      .put("height", 400)
      .put("contentWidth", "100%")
      .put("contentHeight", "100%")
      .build();

  /** Dialog parameters */
  public enum RequestParameter {
    /** Dialog Title */
    title,
    /** Partner code */
    pcode,
    /** Partner company code */
    pcpy,
    /** Read-only */
    ro,
    /** Help fragment */
    helpFragment;
    @SuppressWarnings("unchecked")
    public <T> T getValue() {
      if (this.equals(ro))
        return (T) Boolean.valueOf(getRequestParameter(this.toString(), TRUE.toString()));
      return (T) getRequestParameter(this.toString());
    }
  }

  private transient SecurityService securityService;

  private String title;

  private boolean readonly;

  private String helpFragment;

  private Partner partner;

  private Role role;

  private List<SelectItem> roleItems;

  @PostConstruct
  public void init() {
    this.securityService = getSpringBean(SecurityService.class, SecurityService.SERVICE_NAME);

    Company company = this.securityService.getCompanyByCode(RequestParameter.pcpy.<String> getValue());
    this.partner = this.securityService.getPartner(RequestParameter.pcode.<String> getValue(), company);
    Assert.notNull(this.partner, "labels.warn_partner_missing", "Partner is missing (see pcode parameter)");
    this.readonly = RequestParameter.ro.getValue();
    this.title = RequestParameter.title.getValue();
    if (isBlank(this.title))
      this.title = this.partner.getFullname();

    // -- roles --
    // - available -
    this.roleItems = ImmutableList.<SelectItem> builder()
        .add(new SelectItem(null, ""))
        .addAll(this.securityService.getGroupsByParentAndDescription(company.getId(), Role.DESCRIPTION)
            .stream()
            .map(role -> new SelectItem(role, String.format("%s (%s)", role.getFullname(), role.getName())))
            .collect(toList()))
        .build();

    // - selected -
    String partnerRoleName = this.partner.getRole();
    this.role = !StringUtils.isEmpty(partnerRoleName) ? RoleHelper.findByName(company, partnerRoleName) : null;
    this.helpFragment = RequestParameter.helpFragment.getValue();
  }

  @PreDestroy
  public void reset() {
    this.title = null;
    this.readonly = true;
    this.partner = null;
    this.role = null;
    this.roleItems = null;
  }

  public void onSave(ActionEvent event) {
    this.securityService.savePartnerRole(this.partner, this.role);
    ClosableDialog.closeDialog(this.partner);
  }

  public static final void openPartnerRoleDialog(
      final String outcome, final Map<String, Object> options,
      final String title, final String partnerCode, final String companyCode, final boolean readonly,
      String helpFragment) {

    if (isBlank(partnerCode))
      return;

    getCurrentInstance().openDialog(outcome,
        // dialog options
        options == null ? PERMISSION_DIALOG_OPTIONS : options,
        // request parameters
        ImmutableMap.<String, List<String>> builder()
            .put(RequestParameter.title.toString(), asList(isBlank(title) ? "Rights" : title)) // I18N
            .put(RequestParameter.pcode.toString(), asList(partnerCode))
            .put(RequestParameter.pcpy.toString(), asList(companyCode))
            .put(RequestParameter.ro.toString(), asList(Boolean.toString(readonly)))
            .put(RequestParameter.helpFragment.toString(), asList(helpFragment))
            .build());
  }

  public String getTitle() {
    return title;
  }

  public boolean isReadonly() {
    return readonly;
  }

  public String getHelpFragment() {
    return helpFragment;
  }

  public Role getRole() {
    return role;
  }

  public void setRole(Role role) {
    this.role = role;
  }

  public List<SelectItem> getRoleItems() {
    return roleItems;
  }
}
