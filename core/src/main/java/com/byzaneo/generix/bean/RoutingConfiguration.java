package com.byzaneo.generix.bean;

import static com.byzaneo.commons.ui.util.JSFHelper.getManagedBean;
import static com.byzaneo.commons.ui.util.MessageHelper.error;
import static com.byzaneo.generix.util.RoutingHelper.getEndpointConfigurationTypes;
import static java.lang.String.format;

import java.util.*;
import java.util.stream.Collectors;

import com.byzaneo.generix.api.*;
import com.byzaneo.generix.ui.OrganizationDialogHandler;

import javax.faces.event.ActionEvent;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.Validate;
import org.slf4j.*;
import org.springframework.data.annotation.Transient;

/**
 * Document routing configuration variable.
 *
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Jun 5, 2015
 * @since 3.4 GNX-2231
 */
public class RoutingConfiguration implements Configuration {

  private static final Logger log = LoggerFactory.getLogger(RoutingConfiguration.class);

  // DATA
  /**
   * Endpoint configurations
   * 
   * @see EnpointConfiguration
   */
  private List<EndpointConfiguration> endpoints = new ArrayList<>();

  // TRANSIENT
  // Endpoint types
  private transient Class<? extends EndpointConfiguration> endpointType;

  /** Created or edited endpoint */
  @Transient
  private transient EndpointConfiguration endpoint;

  /* -- EVENTS -- */

  public void onNewEndpoint(ActionEvent event) {
    try {
      this.endpoint = this.endpointType == null ? null : this.endpointType.newInstance();
    }
    catch (InstantiationException | IllegalAccessException e) {
      log.error("Failed to get new endpoint", e);
    }
  }

  public void onSelectEndpoint(EndpointConfiguration endpoint) {
    this.endpoint = endpoint;
  }

  public void onAddEndpoint() {
    try {
      this.addEndpoint(endpoint);
      if ("authentication"
          .equals(endpoint.getShortType())) {
        getManagedBean(OrganizationDialogHandler.class, OrganizationDialogHandler.MANAGED_BEAN_NAME).setUpdateChannel(true);
      }
      this.endpoint = null;
    }
    catch (Exception e) {
      log.error("Failed to add new endpoint", e);
      error(e.getMessage());
    }
  }

  /* -- ACCESSORS -- */

  public List<Class<? extends EndpointConfiguration>> getEndpointTypes() {
    return getEndpointConfigurationTypes();
  }

  public Class<? extends EndpointConfiguration> getEndpointType() {
    return endpointType;
  }

  public void setEndpointType(Class<? extends EndpointConfiguration> endpointType) {
    this.endpointType = endpointType;
  }

  public EndpointConfiguration getEndpoint() {
    return endpoint;
  }

  public void setEndpoint(EndpointConfiguration endpoint) {
    this.endpoint = endpoint;
  }

  /**
   * Add the given endpoint to this configuration.
   * 
   * @param endpoint the enpoint configuration to add.
   * @return {@code true} if the endpoint is new in this configuration, {@code false} if if was already present.
   */
  public boolean addEndpoint(EndpointConfiguration endpoint) {
    if (endpoint == null) {
      return false;
    }
    // validates
    endpoint.validates();
    // updates?
    boolean added = !this.removeEndpoint(endpoint);
    // adds
    this.endpoints.add(endpoint);
    return added;
  }

  public boolean removeEndpoint(EndpointConfiguration endpoint) {
    if (endpoint == null) {
      return false;
    }
    return endpoints.remove(endpoint);
  }

  public List<EndpointConfiguration> getEndpoints() {
    return endpoints;
  }

  public void setEndpoints(List<EndpointConfiguration> endpoints) {
    this.endpoints = endpoints;
  }

  public boolean canAddThisEndpoint(EndpointConfiguration endpointConfiguration) {
    Validate.notNull(endpointConfiguration);
    if (endpointConfiguration.canHaveMultipleEndpoints()) {
      return true;
    }
    return getEndpoints()
        .stream()
        .noneMatch(e -> e.getShortType()
            .equals(endpointConfiguration.getShortType()));
  }

  public void checkEndpointsConfigurationValide() throws EndpointsConfigurationInvalideException {
    Set<String> invalidsMultiplesEndpoints = getEndpoints()
        .stream()
        .filter(e -> !e.canHaveMultipleEndpoints())
        .collect(Collectors.groupingBy(EndpointConfiguration::getShortType, Collectors.counting()))
        .entrySet()
        .stream()
        .filter(e -> e.getValue() > 1)
        .map(Map.Entry::getKey)
        .collect(Collectors.toSet());
    if (CollectionUtils.isNotEmpty(invalidsMultiplesEndpoints)) {
      throw new EndpointsConfigurationInvalideException(invalidsMultiplesEndpoints);
    }
  }

  /* -- OVERRIDE -- */

  @Override
  public String toString() {
    return format("RoutingConfiguration: endpoints=%s", endpoints);
  }
}
