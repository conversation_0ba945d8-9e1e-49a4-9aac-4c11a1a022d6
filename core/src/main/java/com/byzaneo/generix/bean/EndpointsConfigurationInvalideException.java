package com.byzaneo.generix.bean;

import java.util.Set;

import org.apache.commons.lang3.Validate;

public class EndpointsConfigurationInvalideException extends Exception {

  /**
   * 
   */
  private static final long serialVersionUID = 868121457289185987L;

  private Set<String> endpointsInvalide;

  public EndpointsConfigurationInvalideException(Set<String> endpointsInvalide) {
    Validate.notEmpty(endpointsInvalide);
    this.endpointsInvalide = endpointsInvalide;
  }

  public Set<String> getEndpointsInvalide() {
    return endpointsInvalide;
  }

}
