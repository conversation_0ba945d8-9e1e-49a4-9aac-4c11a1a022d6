package com.byzaneo.generix.bean;

import com.byzaneo.commons.bean.AbstractMetadata;
import com.google.gson.reflect.TypeToken;

import javax.persistence.*;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;

import static com.byzaneo.commons.util.GsonHelper.getGson;
import static com.byzaneo.generix.util.InstanceHelper.DEFAULT_LANGUAGE;
import static java.lang.Boolean.FALSE;
import static java.util.Collections.singletonList;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 * Embeddabble {@link Instance}'s properties.
 * <p>
 * Since AIO-5279, you should consider to use instance's configuration stored in NoSQL database.
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @version 2.1 GNX-242
 * @company Byzaneo
 * @date Feb 19, 2013
 */
@Embeddable
@AssociationOverrides({
    @AssociationOverride(name = "metaGroup", joinColumns = @JoinColumn(name = "CFG_PROPERTIES"))
})
public class InstanceProperties extends AbstractMetadata {
  private static final long serialVersionUID = 3855636982298911481L;

  private static final Type LOCALE_LIST_TYPE = new TypeToken<List<Locale>>() {
  }.getType();

  // LANGUAGES

  @Column(name = "CFG_LANGUAGE_DEFAULT")
  private Locale defaultLanguage;

  @Column(name = "CFG_LANGUAGES")
  private String jsonLanguages;

  @Transient
  private List<Locale> languages;

  @Column(name = "CFG_SELF_REGISTER")
  private Boolean selfRegister;

  @Column(name = "CFG_SELF_REGISTER_PH1")
  private String selfRegisterPh1;

  @Column(name = "CFG_SELF_REGISTER_PH2")
  private String selfRegisterPh2;

  // MAIL

  @Column(name = "CFG_FROM_ADDRESS", length = 512)
  private String fromAddress;

  @Column(name = "CFG_CONTACT", length = 512)
  private String contact;

  @Column(name = "CFG_CONTACT_SUBJECT", length = 512)
  private String contactSubject;

  @Column(name = "CFG_COOKIE")
  private Boolean cookie;

  @Column(name = "CFG_COOKIE_TITLE", length = 2048)
  private String cookieTitle;

  @Column(name = "CFG_COOKIE_DESCRIPTION", length = 2048)
  private String cookieDescription;

  @Column(name = "CFG_WORKFLOW_NOTIFICATION")
  private Boolean workflowNotification;

  /*
   * -- PERSISTENCY EVENTS -- not supported in embedded, so called by instance
   */

  public void postLoad() {
    languages = isNotBlank(jsonLanguages) ? getGson().<List<Locale>> fromJson(jsonLanguages, LOCALE_LIST_TYPE) : new ArrayList<Locale>(0);
  }

  public void prePersist() {
    this.jsonLanguages = null;
    if (isNotEmpty(languages)) {
      this.jsonLanguages = getGson().toJson(new ArrayList<>(new HashSet<>(languages)), LOCALE_LIST_TYPE);
      if (!languages.contains(defaultLanguage))
        defaultLanguage = null;
    }
    else {
      defaultLanguage = null;
    }
  }

  /*
   * -- ACCESSORS --
   */

  // LANGUAGES

  public Locale getDefaultLanguage() {
    if (defaultLanguage == null)
      defaultLanguage = getLanguages().get(0);
    return defaultLanguage;
  }

  public void setDefaultLanguage(Locale defautlLanguage) {
    this.defaultLanguage = defautlLanguage;
  }

  public List<Locale> getLanguages() {
    return isNotEmpty(languages)
        ? languages
        : (languages = new ArrayList<>(singletonList(DEFAULT_LANGUAGE)));
  }

  public void setLanguages(List<Locale> languages) {
    this.languages = languages;
  }

  public Boolean getSelfRegister() {
    return selfRegister == null ? FALSE : selfRegister;
  }

  public void setSelfRegister(Boolean selfRegister) {
    // empty fields when selfRegister is unchecked
    if (selfRegister == FALSE) {
      this.selfRegisterPh1 = null;
      this.selfRegisterPh2 = null;
    }
    this.selfRegister = selfRegister;
  }

  public String getSelfRegisterPh1() {
    return selfRegisterPh1;
  }

  public void setSelfRegisterPh1(String selfRegisterPh1) {
    this.selfRegisterPh1 = selfRegisterPh1;
  }

  public String getSelfRegisterPh2() {
    return selfRegisterPh2;
  }

  public void setSelfRegisterPh2(String selfRegisterPh2) {
    this.selfRegisterPh2 = selfRegisterPh2;
  }

  // MAIL

  public String getFromAddress() {
    return fromAddress;
  }

  public void setFromAddress(String fromAdress) {
    this.fromAddress = fromAdress;
  }

  public String getContact() {
    return contact;
  }

  public void setContact(String contact) {
    this.contact = contact;
  }

  public String getContactSubject() {
    return contactSubject;
  }

  public void setContactSubject(String contactSubject) {
    this.contactSubject = contactSubject;
  }

  public Boolean getCookie() {
    return cookie;
  }

  public void setCookie(Boolean cookie) {
    this.cookie = cookie;
  }

  public String getCookieTitle() {
    return cookieTitle;
  }

  public void setCookieTitle(String cookieTitle) {
    this.cookieTitle = cookieTitle;
  }

  public String getCookieDescription() {
    return cookieDescription;
  }

  public void setCookieDescription(String cookieDescription) {
    this.cookieDescription = cookieDescription;
  }

  public Boolean getWorkflowNotification() {
    return workflowNotification;
  }

  public void setWorkflowNotification(Boolean workflowNotification) {
    this.workflowNotification = workflowNotification;
  }
}
