<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml"
              xmlns:f="http://xmlns.jcp.org/jsf/core"
              xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
              xmlns:cc="http://xmlns.jcp.org/jsf/composite"
              xmlns:h="http://xmlns.jcp.org/jsf/html"
              xmlns:pe="http://primefaces.org/ui/extensions"
              xmlns:p="http://primefaces.org/ui">

    <!-- INTERFACE -->
    <cc:interface name="productAllowanceTax">
        <cc:attribute name="taskBean" required="true"/>
        <cc:attribute name="invoice" default="true"/>
    </cc:interface>
    <!-- IMPLEMENTATION -->
    <cc:implementation>
        <p:outputPanel styleClass="psAllChrg">
	        <p:outputPanel styleClass="psAllowanceMessages">
	            <p:dataList value="#{cc.attrs.taskBean.messages}" 
	                var="message" 
	                type="unordered" 
	                emptyMessage=""
	                style="list-style-type: none;background-color:#f2dede!important;text-align:left;"
	                styleClass="ui-messages-error ui-corner-all psAllowanceMessages"  
	                rendered="#{cc.attrs.taskBean.messages != null and !cc.attrs.taskBean.messages.isEmpty()}"
	                >
	               <h:outputText value="#{message}" styleClass="ui-messages-error-summary" style="background-color:#f2dede!important;text-align:left;"/>
	            </p:dataList>
	        </p:outputPanel>
            <p:selectOneMenu id="refAllowOrChrgLine"
                             styleClass="float-right psChargeMnu"
                             value="#{cc.attrs.taskBean.currentTax}"
                             converter="#{cc.attrs.taskBean.taxConverter}"
                             disabled="#{cc.attrs.taskBean.exoTvaChecked || cc.attrs.taskBean.allowOrChrgLineEdit}">
                <p:ajax event="change" process="@this @(.psAllChrg)"
                        update="@this @(.psAllChrg)"
                        listener="#{cc.attrs.taskBean.onSelectAllowOrChargeLine()}"
                        oncomplete="$('[id$=allowOrChargeDlg] .ui-datatable-data tr').last().find('span.ui-icon-pencil').each(function() {$(this).click();});"/>
                <f:selectItem itemLabel="#{craddinvtsklbls.add_tax}"/>
                <f:selectItems value="#{cc.attrs.taskBean.listOfTaxLines}"
                               var="tax"
                               itemValue="#{tax}"
                               itemLabel="#{tax.allowanceOrChargeDescription.listOfDescription}"/>
            </p:selectOneMenu>

            <p:commandButton value="#{craddinvtsklbls.add_allowance}"
                             styleClass="reset-btn btn-grey float-right psAddAllowanceBtn"
                             actionListener="#{cc.attrs.taskBean.onSelectAllowOrChargeLine()}"
                             disabled="#{cc.attrs.taskBean.allowOrChrgLineEdit}"
                             process="@this @(.psAllChrg)"
                             update="@(.psAllChrg)"
                             oncomplete="$('[id$=allowOrChargeDlg] .ui-datatable-data tr').last().find('span.ui-icon-pencil').each(function() {$(this).click();});"
                             style="height:36px;"/>


            <p:spacer height="40px"/>

            <p:dataTable id="allowOrChargeDlg"
                         value="#{cc.attrs.taskBean.product.invoicePricingDetail.itemAllowancesOrCharges.allowOrCharge}"
                         editable="true"
                         resizableColumns="true"
                         var="alwcChrg"
                         styleClass="psAllowOrChargeDlg"
                         emptyMessage="#{labels.no_records_found}">
                <p:ajax event="rowEditInit" listener="#{cc.attrs.taskBean.onRowInitAllOrChrgLine}" 
                        update="@(.psAllowanceBtn)"/>
                <p:ajax event="rowEdit" listener="#{cc.attrs.taskBean.onRowEditAllOrChrgLine}"
                        update="@(.psAllowOrChargeDlg) @(.psAllowanceBtn) @(.psAllowanceMessages) @(.psAddAllowanceBtn) @(.psChargeMnu)"/>
                <p:ajax event="rowEditCancel"
                        listener="#{cc.attrs.taskBean.onRowCancelLine}" 
                        update="@(.psAllowanceBtn)"/>
                <p:column width="15%"
                          headerText="#{craddinvtsklbls.invoice_tax_ean}">
                    <h:outputText
                            value="#{alwcChrg.indicatorCoded == 'LINE_ITEM_ALLOWANCE' ? '' : alwcChrg.allowanceOrChargeDescription.refID}"/>
                </p:column>
                <p:column width="40%"
                          headerText="#{gnxxcblinvlbls.description}">
                    <p:cellEditor>
                        <f:facet name="output"> <h:outputText
                                value="#{alwcChrg.allowanceOrChargeDescription.listOfDescription}"/> </f:facet>
                        <f:facet name="input">
                            <p:inputText value="#{alwcChrg.allowanceOrChargeDescription.listOfDescription}"
                            			 validator="xssValidator"
                                         rendered="#{alwcChrg.indicatorCoded == 'LINE_ITEM_ALLOWANCE'}"
                                         style="width: 100%;text-align: center;"/>
                            <h:outputText value="#{alwcChrg.allowanceOrChargeDescription.listOfDescription}"
                                          rendered="#{alwcChrg.indicatorCoded != 'LINE_ITEM_ALLOWANCE'}"/>
                        </f:facet>
                    </p:cellEditor>
                </p:column>
                <p:column width="15%"
                          headerText="#{gnxxcblinvlbls.indicator_coded}">
                    <h:outputText value="#{gnxxcblinvlbls[alwcChrg.indicatorCoded]}"/>
                </p:column>
                <p:column width="10%"
                          headerText="#{craddinvtsklbls.percent_symbol}">
                    <p:cellEditor id="allowChrgPercentageId">
                        <f:facet name="output">
                            <h:outputText
                                    value="#{alwcChrg.typeOfAllowanceOrCharge.percentageAllowanceOrCharge.percent.value}%"/>
                        </f:facet>
                        <f:facet name="input">
                            <pe:inputNumber value="#{alwcChrg.typeOfAllowanceOrCharge.percentageAllowanceOrCharge.percent.value}"
                                            required="true"
                                            requiredMessage="#{gnxxcblinvlbls.value} : #{gnxxcblcomlbls.value_required}"
                                            styleClass="input-number-resize" maxValue="100.00" minValue="0"
                                            symbol="%" symbolPosition="suffix"
                                            disabled="#{alwcChrg.indicatorCoded == 'SERVICE'}">
                                <p:ajax event="change" process="@this" update="allowChrgAmountId"
                                        listener="#{cc.attrs.taskBean.onPercentageModified(alwcChrg)}"
                                        global="false"/>
                            </pe:inputNumber>
                        </f:facet>
                    </p:cellEditor>
                </p:column>
                <p:column width="10%"
                          headerText="#{gnxxcblinvlbls.monetary_amount}">
                    <p:cellEditor id="allowChrgAmountId">
                        <f:facet name="output">
                            <h:outputText value="#{alwcChrg.typeOfAllowanceOrCharge.monetaryValue.monetaryAmount}"
                                          style="float:right;">
                                <f:converter converterId="gnxBigDecimalConverter"/>
                                <f:attribute name="scale" value="#{cc.attrs.taskBean.detailPrecision}"/>
                            </h:outputText>
                        </f:facet>
                        <f:facet name="input">
                            <pe:inputNumber value="#{alwcChrg.typeOfAllowanceOrCharge.monetaryValue.monetaryAmount}"
                                            required="true"
                                            requiredMessage="#{gnxxcblinvlbls.value} : #{gnxxcblcomlbls.value_required}"
                                            styleClass="input-number-resize"
                                            decimalPlaces="#{cc.attrs.taskBean.detailPrecision}">
                                <p:ajax event="change" process="@this" update="allowChrgPercentageId"
                                        listener="#{cc.attrs.taskBean.onAllowanceAmountModified(alwcChrg)}"
                                        global="false"/>
                            </pe:inputNumber>
                        </f:facet>
                    </p:cellEditor>
                </p:column>
                <p:column width="10%"
                          headerText="#{gnxxcblcomlbls.action}">
                    <p:rowEditor style="float:left;"/>
                    <p:commandLink styleClass="ui-icon ui-icon-trash"
                                   action="#{cc.attrs.taskBean.onRemoveLineAllowOrCharge(alwcChrg)}"
                                   process="@this"
                                   update="@(.psAllowOrChargeDlg) @(.psChargeMnu) @(.psAddAllowanceBtn) @(.psAllowanceBtn)"
                                   title="#{craddinvtsklbls.invoice_alloworchrg_remove}"
                                   style="float:left"/>
                </p:column>
            </p:dataTable>

            <p:commandButton value="#{craddinvtsklbls.validate}"
                             styleClass="reset-btn btn-grey float-right psAllowanceBtn"
                             style="margin-top: 10px !important;"
                             action="#{cc.attrs.taskBean.saveProduct(cc.attrs.taskBean.product)}"
                             disabled="#{cc.attrs.taskBean.allowOrChrgLineEdit}"
                             update="@(.psDetailTable) @(.psAllowanceMessages)"
                             oncomplete="PF('addRemTaxDlg').hide();"
                             global="false">
            </p:commandButton>
            <p:commandButton value="#{craddinvtsklbls.cancel}"
                             process="@(.psDetails)"
                             update="@(.psDetails) @(.impInfPnl)"
                             onclick="cancelDialog()"
                             styleClass="reset-btn btn-grey float-right"
                             style="margin-top: 10px !important;"
                             oncomplete="PF('addRemTaxDlg').hide();">
            </p:commandButton>
        </p:outputPanel>

    </cc:implementation>
</ui:component>