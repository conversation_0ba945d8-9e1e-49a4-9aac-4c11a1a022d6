
/*
 * Copyright (c) 2024.
 * Created by Mansour SRIDI
 */

package com.byzaneo.generix.service.repository.dao.sql.aap;

import com.byzaneo.commons.dao.hibernate.GenericJpaDAO;
import com.byzaneo.generix.service.repository.bean.aap.AapInvActPosting;
import com.byzaneo.generix.service.repository.bean.aap.AapItem;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.TypedQuery;

@Repository(AapItemDao.DAO_NAME)
public class AapItemDaoImpl extends GenericJpaDAO<AapItem, Long> implements AapItemDao {

}

