webapp.dir = target/test-classes/webapp
data.dir   = target/test-classes/data

# -- D A T A B A S E --
database.flyway.disable = true
#database.type       = mysql
#database.driver		= com.mysql.jdbc.Driver
#database.url		= *********************************************************************************************************************************************************************
#database.username	= ci
#database.password	= ci
#database.target		= com.byzaneo.commons.dao.hibernate.support.MySQL5Dialect
database.schema=gnx
database.type=postgres
database.driver=org.postgresql.Driver
database.url=****************************************************
database.username=cipg
database.password=cipg
database.target=org.hibernate.dialect.PostgreSQLDialect
database.datasource=pooledDataSource
database.showSql	= false
# validate | update | create | create-drop
database.generateDdl		= true
database.generateDdl.mode	= create-drop
database.process.generateDdl = drop-create
database.generateDdl.imports=/META-INF/data/cpg.${database.type}.sql



index.mongo.uri=mongodb://ci:ci@ci-mongo/ci