package com.byzaneo.generix.xcbl.service;

import static com.byzaneo.commons.service.ConfigurationService.ConfigurationKey.DATA_DIR;
import static com.byzaneo.commons.service.ConfigurationService.ConfigurationKey.OUTPUT_DIR;
import static com.byzaneo.commons.ui.util.JSFHelper.getLocale;
import static com.byzaneo.generix.service.SecurityService.GroupName.NOTIFICATION;
import static com.byzaneo.query.builder.Clauses.equal;
import static com.byzaneo.query.builder.Clauses.in;
import static com.byzaneo.query.builder.QueryBuilder.createBuilder;
import static com.byzaneo.query.operator.Operator.NOT_IN;
import static com.byzaneo.security.util.PrincipalHelper.isInGroupNames;
import static java.math.BigDecimal.ZERO;
import static java.util.Optional.of;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;

import java.io.*;
import java.math.BigDecimal;
import java.text.*;
import java.util.*;

import javax.mail.internet.InternetAddress;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.*;
import org.apache.commons.lang3.SerializationUtils;
import org.joda.time.*;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.io.CsvStream;
import com.byzaneo.commons.service.*;
import com.byzaneo.commons.ui.util.MessageHelper;
import com.byzaneo.commons.util.*;
import com.byzaneo.generix.bean.Instance;
import com.byzaneo.generix.service.*;
import com.byzaneo.generix.service.repository.bean.Product;
import com.byzaneo.generix.service.repository.service.RepositoryService;
import com.byzaneo.generix.xcbl.api.*;
import com.byzaneo.generix.xcbl.bean.*;
import com.byzaneo.generix.xcbl.dao.HistoryDAO;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.*;
import com.byzaneo.security.bean.*;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.api.Indexable;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.Document_;
import com.byzaneo.xtrade.dao.DocumentDAO.DocumentField;
import com.byzaneo.xtrade.service.DocumentService;
import com.byzaneo.xtrade.xcbl.bean.*;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.*;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.*;
import com.mongodb.DBObject;

/**
 * <AUTHOR> Farnier <<EMAIL>>
 * @company Generix Group
 * @date 18 févr. 2015
 * @version 1.0
 */
@Service(XcblService.SERVICE_NAME)
public class XcblServiceImpl implements XcblService {
  private static final Logger log = getLogger(XcblServiceImpl.class);

  private static final String QUANTITY_AWAITING_DELIVERY = "QuantityAwaitingDelivery";
  private static final String LATE = "Late";
  private static final String ADVANCE = "QuantityEarned";

  @Autowired
  @Qualifier(ConfigurationService.SERVICE_NAME)
  private ConfigurationService configService;

  @Autowired
  @Qualifier(DocumentService.SERVICE_NAME)
  private transient DocumentService documentService;

  @Autowired
  @Qualifier(MailService.SERVICE_NAME)
  private transient MailService mailService;

  @Autowired
  @Qualifier(SecurityService.SERVICE_NAME)
  private transient SecurityService securityService;

  @Autowired
  @Qualifier(MessagingService.SERVICE_NAME)
  private transient MessagingService messagingService;

  @Autowired
  @Qualifier(InstanceService.SERVICE_NAME)
  private transient InstanceService instanceService;

  @Autowired
  @Qualifier(RepositoryService.SERVICE_NAME)
  private transient RepositoryService repositoryService;

  @Autowired(required = false)
  @Qualifier(XcblService.INDEX_OPERATIONS_NAME)
  private IndexOperations indexOperations;

  @Autowired(required = false)
  private Map<String, LabelService> labelServices;

  @Autowired(required = false)
  private Map<String, DrawingService> drawingServices;

  @Autowired
  @Qualifier(HistoryDAO.DAO_NAME)
  private HistoryDAO historyDao;

  private Map<Integer, Invoice> invoiceMap;

  @Override
  public void checkPenaltyFlowsIntegration(String owner, String date) throws Exception {
    DateFormat df = new SimpleDateFormat("yyyyMMdd");
    DatePeriod period = new DatePeriod();

    Calendar calendar = Calendar.getInstance();

    if (StringUtils.isNotBlank(date)) {
      calendar.setTime(df.parse(date));
    }
    else {
      calendar.setTime(new Date());
    }

    // Hier a 00h00m00s000ms
    calendar.set(Calendar.HOUR_OF_DAY, 0);
    calendar.set(Calendar.MINUTE, 0);
    calendar.set(Calendar.SECOND, 0);
    calendar.set(Calendar.MILLISECOND, 0);
    calendar.add(Calendar.DATE, -1);
    period.setStartDate(calendar.getTime());

    // Hier a 23h59m59s999ms
    calendar.add(Calendar.DATE, 1);
    calendar.add(Calendar.MILLISECOND, -1);
    period.setEndDate(calendar.getTime());

    List<DatePeriod> periods = new ArrayList<>();
    periods.add(period);

    List<DocumentField> fields = new ArrayList<>();
    fields.add(DocumentField.owners);

    List<String> types = new ArrayList<>();
    types.add(DocumentType.PENALTY.toString());

    Map<String, Map<String, Number>> reportCount = documentService.getReportCount(fields, periods, null, owner,
        types, null, null, true, null);

    if (reportCount.isEmpty()) {
      log.warn(MessageHelper.getMessage("labels.xcbl-labels.noFlowsFileReceived", "", null,
          df.format(period.getStartDate())));

      Company company = securityService.getCompanyByCode(owner);

      String fileName = "/penalty/RapportPenalitesFrn" + (company != null ? company.getDuns() : owner) + "_F01F05_" +
          df.format(period.getStartDate()) + "999999.txt";

      File file = new File(this.configService.getString(OUTPUT_DIR), fileName);

      try (FileWriter fw = new FileWriter(file.getAbsoluteFile())) {
        fw.write(MessageHelper.getMessage("labels.xcbl-labels.noFlowsFileReceived", "", null,
            df.format(period.getStartDate())));
      }
      catch (IOException e) {
        throw new ServiceException(e, "Error creating report: %s", getRootCauseMessage(e));
      }
    }
    else {
      log.info("Flow file received.");
    }
  }

  @Override
  public void notifyPenaltyFlowsIntegration(String owner, String date) {
    DatePeriod period = new DatePeriod();

    Calendar calendar = Calendar.getInstance();

    if (StringUtils.isNotBlank(date)) {
      DateFormat df = new SimpleDateFormat("yyyyMMdd");
      try {
        calendar.setTime(df.parse(date));
      }
      catch (ParseException e) {
        log.error(e.getMessage(), e);
      }
    }
    else {
      calendar.setTime(new Date());
    }

    // Hier a 00h00m00s000ms
    calendar.set(Calendar.HOUR_OF_DAY, 0);
    calendar.set(Calendar.MINUTE, 0);
    calendar.set(Calendar.SECOND, 0);
    calendar.set(Calendar.MILLISECOND, 0);
    calendar.add(Calendar.DATE, -1);
    period.setStartDate(calendar.getTime());

    // Hier a 23h59m59s999ms
    calendar.add(Calendar.DATE, 1);
    calendar.add(Calendar.MILLISECOND, -1);
    period.setEndDate(calendar.getTime());

    Criteria criteria = new Criteria();

    criteria.andOperator(
        Criteria.where("_type")
            .is(DocumentType.PENALTY),
        Criteria.where("_owner")
            .is(owner),
        Criteria.where("goodsReceiptHeader.goodsReceiptPurpose.purposeCodedOther")
            .is("F01"),
        Criteria.where("goodsReceiptHeader.goodsReceiptDates.listOfDateCoded.dateCoded")
            .elemMatch(
                Criteria.where("dateQualifier.dateQualifierCoded")
                    .is("PublicationDate")
                    .andOperator(
                        Criteria.where("date")
                            .gte(
                                new DateTime(period.getStartDate()
                                    .getTime(), DateTimeZone.UTC)),
                        Criteria.where("date")
                            .lte(
                                new DateTime(period.getEndDate()
                                    .getTime(), DateTimeZone.UTC)))));

    AggregationOperation match = match(criteria);
    List<org.bson.Document> foundDocuments = documentService.aggregate(newAggregation(Penalty.class, match), org.bson.Document.class);

    Map<String, String> tos_name = foundDocuments.stream()
        .collect(
            toMap(document -> document.get("_to")
                .toString(),
                document -> ((MongoTemplate) indexOperations)
                    .getConverter()
                    .read(Penalty.class, document)
                    .getGoodsReceiptHeader()
                    .getGoodsReceiptParty()
                    .getSellerParty()
                    .getNameAddress()
                    .getName1(),
                (keepFirst, ignoreOther) -> keepFirst));

    InternetAddress from = mailService.getFromAddress();

    Company company = securityService.getCompanyByCode(owner);

    for (String key : tos_name.keySet()) {
      Partner partner = securityService.getPartner(key, company);

      if (partner != null) {
        if (partner.getUsers() != null) {
          for (User user : partner.getUsers()) {
            if (StringUtils.isNotBlank(user.getEmail())) {
              String subject = MessageHelper.getMessage(
                  "labels.xcbl-labels.mailMessageNewPenaltiesSubject", null, getLocale(),
                  new Object[] { company.getFullname() });
              String textContent = MessageHelper.getMessage(
                  "labels.xcbl-labels.mailMessageNewPenaltiesText", null, getLocale(), new Object[] {
                      partner.getFullname(), company.getFullname(), configService.getFullContextUrl() });
              String htmlContent = MessageHelper.getMessage(
                  "labels.xcbl-labels.mailMessageNewPenaltiesHtml", null, getLocale(), new Object[] {
                      company.getFullname(), configService.getFullContextUrl() });

              try {
                mailService.sendMessage(from,
                    new InternetAddress[] { new InternetAddress(user.getEmail()) }, null, null,
                    subject, textContent, htmlContent, null, null);
              }
              catch (Exception e) {
                log.error(e.getMessage(), e);
              }
            }
          }
        }
      }
      else {
        Collection<User> users = securityService.getUsers(company, true, false);

        if (users != null) {
          for (User user : users) {
            if (isInGroupNames(user, NOTIFICATION.toString())) {
              if (StringUtils.isNotBlank(user.getEmail())) {
                String subject = MessageHelper.getMessage(
                    "labels.xcbl-labels.mailMessageUnknownSupplierSubject", null, getLocale(),
                    new Object[] { company.getFullname() });
                String textContent = MessageHelper.getMessage(
                    "labels.xcbl-labels.mailMessageUnknownSupplierText",
                    null,
                    getLocale(),
                    new Object[] { key, tos_name.get(key), company.getFullname(),
                        configService.getFullContextUrl() });
                String htmlContent = MessageHelper.getMessage(
                    "labels.xcbl-labels.mailMessageUnknownSupplierHtml",
                    null,
                    getLocale(),
                    new Object[] { key, tos_name.get(key), company.getFullname(),
                        configService.getFullContextUrl() });
                try {
                  mailService.sendMessage(from,
                      new InternetAddress[] { new InternetAddress(user.getEmail()) }, null, null,
                      subject, textContent, htmlContent, null, null);
                }
                catch (Exception e) {
                  log.error(e.getMessage(), e);
                }
              }
            }
          }
        }
      }
    }
  }

  @Override
  public void processPenaltyTimeout(String owner, String date) throws Exception {
    Calendar calendar = Calendar.getInstance();

    if (StringUtils.isNotBlank(date)) {
      DateFormat df = new SimpleDateFormat("yyyyMMdd");
      calendar.setTime(df.parse(date));
    }
    else {
      calendar.setTime(new Date());
    }

    calendar.set(Calendar.HOUR_OF_DAY, 0);
    calendar.set(Calendar.MINUTE, 0);
    calendar.set(Calendar.SECOND, 0);
    calendar.set(Calendar.MILLISECOND, 0);

    List<DocumentStatus> statusList = new ArrayList<>();
    statusList.add(DocumentStatus.NONE);
    statusList.add(DocumentStatus.READ);

    Criteria criteria = new Criteria();

    criteria.andOperator(
        Criteria.where("_type")
            .is(DocumentType.PENALTY),
        Criteria.where("_owner")
            .is(owner),
        Criteria.where("_state")
            .in(statusList),
        Criteria.where("goodsReceiptHeader.goodsReceiptPurpose.purposeCodedOther")
            .is("F01"),
        Criteria.where("goodsReceiptHeader.goodsReceiptDates.listOfDateCoded.dateCoded")
            .elemMatch(
                Criteria.where("dateQualifier.dateQualifierCoded")
                    .is("Contestability")
                    .and("date")
                    .lt(new DateTime(calendar.getTime(), DateTimeZone.UTC))));

    AggregationOperation match = match(criteria);
    List<org.bson.Document> foundDocuments = documentService.aggregate(newAggregation(Penalty.class, match), org.bson.Document.class);

    List<Document> documents = new ArrayList<>();

    for (org.bson.Document bsonDocument : foundDocuments) {
      Document document = documentService.getDocument(Document.class, new Long(bsonDocument.get("_entity_id")
          .toString()), true);

      if (document != null) {
        document.setStatusWithEnumValue(DocumentStatus.TIMEOUT);
        documents.add(document);
      }
    }

    documentService.saveDocuments(documents);
  }

  @Override
  public void processSuppliersResponses(String owner) throws Exception {
    StringBuilder dirSb = new StringBuilder();
    dirSb.append(SpringContextHelper.getConfigurationService()
        .getString(OUTPUT_DIR));
    dirSb.append("/penalty/");

    File[] files = new File(dirSb.toString()).listFiles((dir, name) -> {
      StringBuilder filenameSb = new StringBuilder();
      filenameSb.append(DocumentType.PENALTY);
      filenameSb.append("-");
      filenameSb.append(owner);
      filenameSb.append("-");
      filenameSb.append("F04");
      filenameSb.append("-");
      filenameSb.append("[0-9]{17}");
      filenameSb.append(FileType.XML.getExtension());

      return name.matches(filenameSb.toString());
    });

    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");

    Company company = securityService.getCompanyByCode(owner);

    String companyCode = company != null ? company.getDuns() : owner;

    try (FileWriter fw = new FileWriter(dirSb + "PenalitesFrn" + companyCode + "_F04_" + sdf.format(new Date()) + ".csv")) {
      fw.append("Flux_ID;Entrepot;Annee;Command;Relicat;Produit;VC;VL;TypePenalite;CodeFournisseurPenalise;NomFournisseurPenalise");
      fw.append(";CodeFournisseurMarchand;NomFournisseurMarchand;NomEntrepot;DateCommande;DateLivraisonSouhaitee;DatePublicationPortail");
      fw.append(";DateMaxContestation;EAN;LibelleProduit;QuantiteCommandee;QuantiteReceptionnee;DateReceptionAnnulation;MontantPenalite");
      fw.append(";DateHeureLecteurFournisseur;ReponseFournisseur;CommentaireFournisseur;DateHeureReponseFournisseur;Reponse" + companyCode +
          ";Commentaire" + companyCode);

      for (File file : files) {
        Penalty penalty = JAXBHelper.unmarshal(Penalty.class, file);

        for (GoodsReceiptItemDetailType goodsReceiptItemDetailType : penalty.getGoodsReceiptDetail()
            .getListOfGoodsReceiptItemDetail()
            .getGoodsReceiptItemDetail()) {
          fw.append(System.getProperty("line.separator"));
          fw.append("F04");
          fw.append(";");
          fw.append(penalty.getGoodsReceiptHeader()
              .getGoodsReceiptParty()
              .getShipToParty()
              .getPartyID()
              .getIdent());
          fw.append(";");
          for (DateCodedType dateCodedType : penalty.getGoodsReceiptHeader()
              .getGoodsReceiptDates()
              .getListOfDateCoded()
              .getDateCoded()) {
            if ("YearToDate".equals(dateCodedType.getDateQualifier()
                .getDateQualifierCoded())) {
              fw.append(new SimpleDateFormat("yy").format(dateCodedType.getDate()));
              break;
            }
          }
          fw.append(";");
          fw.append(penalty.getGoodsReceiptHeader()
              .getGoodsReceiptReferences()
              .getListOfPurchaseOrderReference()
              .getPurchaseOrderReference()
              .get(0)
              .getBuyerOrderNumber());
          fw.append(";");
          fw.append(goodsReceiptItemDetailType.getGoodsReceiptBaseItemDetail()
              .getItemIdentifiers()
              .getPartNumbers()
              .getSellerPartNumber()
              .getRevisionNumber());
          fw.append(";");
          fw.append(goodsReceiptItemDetailType.getGoodsReceiptBaseItemDetail()
              .getItemIdentifiers()
              .getPartNumbers()
              .getSellerPartNumber()
              .getPartID());
          fw.append(";");
          for (ProductIdentifierCodedType productIdentifierCodedType : goodsReceiptItemDetailType
              .getGoodsReceiptBaseItemDetail()
              .getItemIdentifiers()
              .getPartNumbers()
              .getOtherItemIdentifiers()
              .getProductIdentifierCoded()) {
            if ("Other".equals(productIdentifierCodedType.getProductIdentifierQualifierCoded()) &&
                "VC".equals(productIdentifierCodedType.getProductIdentifierQualifierCodedOther())) {
              fw.append(productIdentifierCodedType.getProductIdentifier());
              break;
            }
          }
          fw.append(";");
          for (ProductIdentifierCodedType productIdentifierCodedType : goodsReceiptItemDetailType
              .getGoodsReceiptBaseItemDetail()
              .getItemIdentifiers()
              .getPartNumbers()
              .getOtherItemIdentifiers()
              .getProductIdentifierCoded()) {
            if ("Other".equals(productIdentifierCodedType.getProductIdentifierQualifierCoded()) &&
                "VL".equals(productIdentifierCodedType.getProductIdentifierQualifierCodedOther())) {
              fw.append(productIdentifierCodedType.getProductIdentifier());
              break;
            }
          }
          fw.append(";");
          switch (goodsReceiptItemDetailType.getListOfLineItemGoodsCondition()
              .getLineItemGoodsCondition()
              .get(0)
              .getDiscrepancyCoded()) {
          case QUANTITY_SHORT:
            fw.append("quantite");
            break;
          case DELIVERED_TOO_LATE:
            fw.append("date");
            break;
          default:
            break;
          }
          fw.append(";");
          fw.append(penalty.getGoodsReceiptHeader()
              .getGoodsReceiptParty()
              .getSellerParty()
              .getPartyID()
              .getIdent());
          fw.append(";");
          fw.append(penalty.getGoodsReceiptHeader()
              .getGoodsReceiptParty()
              .getSellerParty()
              .getNameAddress()
              .getName1());
          fw.append(";");
          fw.append(penalty.getGoodsReceiptHeader()
              .getGoodsReceiptParty()
              .getShipFromParty()
              .getPartyID()
              .getIdent());
          fw.append(";");
          fw.append(penalty.getGoodsReceiptHeader()
              .getGoodsReceiptParty()
              .getShipFromParty()
              .getNameAddress()
              .getName1());
          fw.append(";");
          fw.append(penalty.getGoodsReceiptHeader()
              .getGoodsReceiptParty()
              .getShipToParty()
              .getNameAddress()
              .getName1());

          DateFormat fdf = new SimpleDateFormat("yyyy-MM-dd");

          fw.append(";");
          fw.append(fdf.format(penalty.getGoodsReceiptHeader()
              .getGoodsReceiptReferences()
              .getListOfPurchaseOrderReference()
              .getPurchaseOrderReference()
              .get(0)
              .getPurchaseOrderDate()));
          fw.append(";");
          fw.append(fdf.format(penalty.getGoodsReceiptHeader()
              .getGoodsReceiptDates()
              .getRequestedDeliveryDate()));
          fw.append(";");
          for (DateCodedType dateCodedType : penalty.getGoodsReceiptHeader()
              .getGoodsReceiptDates()
              .getListOfDateCoded()
              .getDateCoded()) {
            if ("PublicationDate".equals(dateCodedType.getDateQualifier()
                .getDateQualifierCoded())) {
              fw.append(fdf.format(dateCodedType.getDate()));
              break;
            }
          }
          fw.append(";");
          for (DateCodedType dateCodedType : penalty.getGoodsReceiptHeader()
              .getGoodsReceiptDates()
              .getListOfDateCoded()
              .getDateCoded()) {
            if ("Contestability".equals(dateCodedType.getDateQualifier()
                .getDateQualifierCoded())) {
              fw.append(fdf.format(dateCodedType.getDate()));
              break;
            }
          }
          fw.append(";");
          // EAN
          fw.append("");
          fw.append(";");
          fw.append(goodsReceiptItemDetailType.getGoodsReceiptBaseItemDetail()
              .getItemIdentifiers()
              .getItemDescription());
          fw.append(";");
          fw.append(goodsReceiptItemDetailType.getGoodsReceiptBaseItemDetail()
              .getGoodsReceiptQuantities()
              .getOrderedQuantity()
              .getQuantityValue()
              .getValue()
              .toString());
          fw.append(";");
          fw.append(goodsReceiptItemDetailType.getGoodsReceiptBaseItemDetail()
              .getGoodsReceiptQuantities()
              .getReceivedQuantity()
              .getQuantityValue()
              .getValue()
              .toString());
          fw.append(";");
          for (DateCodedType dateCodedType : goodsReceiptItemDetailType.getGoodsReceiptDeliveryDetail()
              .getOtherItemDates()
              .getDateCoded()) {
            if ("CancelIfNotDeliveredByThisDate".equals(dateCodedType.getDateQualifier()
                .getDateQualifierCoded())) {
              fw.append(fdf.format(dateCodedType.getDate()));
              break;
            }
          }
          fw.append(";");
          fw.append(goodsReceiptItemDetailType.getListOfLineItemGoodsCondition()
              .getLineItemGoodsCondition()
              .get(0)
              .getQuantity()
              .getQuantityValue()
              .getValue()
              .toString());
          fw.append(";");
          for (DateCodedType dateCodedType : penalty.getGoodsReceiptHeader()
              .getGoodsReceiptDates()
              .getListOfDateCoded()
              .getDateCoded()) {
            if ("OpeningDate".equals(dateCodedType.getDateQualifier()
                .getDateQualifierCoded())) {
              fw.append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateCodedType.getDate()));
              break;
            }
          }
          fw.append(";");
          if (goodsReceiptItemDetailType.getListOfStructuredNote() != null && goodsReceiptItemDetailType.getListOfStructuredNote()
              .getStructuredNote() != null) {
            for (StructuredNoteType structuredNoteType : goodsReceiptItemDetailType
                .getListOfStructuredNote()
                .getStructuredNote()) {
              if ("SUPPLIERRESPONSE".equals(structuredNoteType.getNoteID())) {
                switch (structuredNoteType.getTextTypeCodedOther()) {
                case "PENALTY_ACCEPTED":
                  fw.append("penalite acceptee");
                  break;
                case "PENALTY_CONTESTED":
                  fw.append("penalite contestee");
                  break;
                default:
                  break;
                }
                fw.append(";");
                if (StringUtils.isNotBlank(structuredNoteType.getGeneralNote())) {
                  structuredNoteType.setGeneralNote(structuredNoteType.getGeneralNote()
                      .replace(';',
                          ','));
                  structuredNoteType
                      .setGeneralNote(structuredNoteType.getGeneralNote()
                          .replace(
                              CharUtils.toString(CharUtils.CR) + CharUtils.toString(CharUtils.LF), " - "));
                }
                fw.append(structuredNoteType.getGeneralNote());
                fw.append(";");
              }
            }
          }
          else {
            fw.append(";");
            fw.append(";");
          }

          for (DateCodedType dateCodedType : penalty.getGoodsReceiptHeader()
              .getGoodsReceiptDates()
              .getListOfDateCoded()
              .getDateCoded()) {
            if ("ValidationDate".equals(dateCodedType.getDateQualifier()
                .getDateQualifierCoded())) {
              fw.append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateCodedType.getDate()));
              break;
            }
          }

          fw.append(";");
          fw.append(";");
        }
      }
    }
  }

  @Override
  public List<Document> isPenaltiesExist(List<Document> documents) throws Exception {
    List<Document> returnDocuments = new ArrayList<>();

    for (Document document : documents) {
      final QueryBuilder qb = createBuilder();

      Penalty penalty = (Penalty) document.getIndex()
          .getValue();

      qb.and(equal("type", DocumentType.PENALTY),
          equal("owners", document.getOwners()),
          equal("goodsReceiptHeader.goodsReceiptReferences.listOfPurchaseOrderReference.purchaseOrderReference.buyerOrderNumber",
              penalty.getGoodsReceiptHeader()
                  .getGoodsReceiptReferences()
                  .getListOfPurchaseOrderReference()
                  .getPurchaseOrderReference()
                  .get(0)
                  .getBuyerOrderNumber()),
          equal("goodsReceiptHeader.goodsReceiptPurpose.purposeCoded", "OTHER"),
          equal("goodsReceiptHeader.goodsReceiptPurpose.purposeCodedOther", "F01"));

      List<Document> foundDocuments = this.documentService.searchIndexedDocuments(Document.class, Penalty.class,
          qb.query(), null);

      if (foundDocuments != null && foundDocuments.size() == 1) {
        returnDocuments.add(document);
      }
    }

    if (documents.size() != returnDocuments.size()) {
      log.error("Unknown penalties during company response importation.");
    }

    return returnDocuments;
  }

  /**
   * @see XcblService#importInvoices(Company, Partner, InputStream)
   */
  @Transactional
  @Override
  public synchronized Logs importInvoices(Company cpy, Partner p, InputStream stream) throws ServiceException {
    return null;
  }

  // Accessors
  @Override
  public Map<Integer, Invoice> getInvoiceMap() {
    if (invoiceMap == null) {
      invoiceMap = new HashMap<>();
    }
    return invoiceMap;
  }

  @Override
  public LabelService getLabelService(Instance instance) {
    String name = instance.getName();
    return labelServices != null && labelServices.containsKey(name) ? labelServices.get(name) : null;
  }

  @Override
  public Map<String, DrawingService> getDrawingServices() {
    return MapUtils.isEmpty(drawingServices) ? Collections.emptyMap() : drawingServices;
  }

  @Override
  public Map<String, LabelService> getLabelServices() {
    return MapUtils.isEmpty(labelServices) ? Collections.emptyMap() : labelServices;
  }

  @Override
  public DrawingService getDrawingService(Instance instance) {
    String name = instance.getName();
    return drawingServices != null && drawingServices.containsKey(name) ? drawingServices.get(name) : null;
  }

  @Override
  public List<Document> createHistory(List<Document> documents) {
    for (Document document : documents) {
      Indexable indexable = document.getIndex()
          .getValue();

      QueryBuilder contractQb = createBuilder();
      contractQb.and(equal(Document_.owners.getName(), document.getOwners()));
      contractQb.and(equal(Document_.to.getName(), document.getTo()));
      List<Contract> contracts = this.documentService.searchIndexables(Contract.class, contractQb.query(), null)
          .getContent();

      if (contracts != null && contracts.size() > 0) {
        if (DocumentType.ORDERS.name()
            .equals(document.getType())) {
          QueryBuilder historyQb = createBuilder();
          historyQb.and(equal(Document_.owners.getName(), document.getOwners()));
          historyQb.and(equal(Document_.to.getName(), document.getTo()));
          historyQb.and(equal("dpoNumber", ((Order) indexable).getOrderHeader()
              .getOrderNumber()
              .getSellerOrderNumber()));

          List<Document> historyDocuments = this.documentService.searchIndexedDocuments(Document.class, History.class, historyQb.query(),
              null);

          if (historyDocuments != null && historyDocuments.size() == 1) {
            Document historyDocument = historyDocuments.get(0);

            History history = historyDocument.getIndexValue();

            history.setDeliveryDate(((Order) indexable).getOrderHeader()
                .getOrderDates()
                .getRequestedDeliverByDate());
            history.setOrderNumber(((Order) indexable).getOrderHeader()
                .getOrderNumber()
                .getBuyerOrderNumber());
            if (!setWeight(history, ((Order) indexable).getOrderDetail()
                .getListOfItemDetail()
                .getItemDetail()
                .get(0)
                .getBaseItemDetail()
                .getTotalQuantity())) {
              log.error("Contract {} without weight information.", history.getContractNumber());
              break;
            }

            boolean found = false;

            for (Contract contract : contracts) {
              if (history.getContractNumber()
                  .equals(contract.getShippingScheduleHeader()
                      .getScheduleID())) {
                history.setFromPlace(contract.getShippingScheduleHeader()
                    .getScheduleParty()
                    .getShipFromParty()
                    .getNameAddress()
                    .getCity());
                history.setSaleCondition(contract.getShippingScheduleHeader()
                    .getTermsOfDelivery()
                    .getShipmentMethodOfPaymentCodedOther());
                if (StringUtils.isNotBlank(contract.getShippingScheduleHeader()
                    .getScheduleParty()
                    .getBuyerParty()
                    .getNameAddress()
                    .getName1())) {
                  history.setToName(contract.getShippingScheduleHeader()
                      .getScheduleParty()
                      .getBuyerParty()
                      .getNameAddress()
                      .getName1());
                }
                else {
                  Company company = new Company();
                  company.setCode(document.getOwners());
                  Partner partner = securityService.getPartner(document.getTo(), company);

                  if (partner != null) {
                    history.setToName(partner.getFullname());
                  }
                }

                history.setProductDescription(contract.getListOfMaterialGroupedShippingDetail()
                    .getMaterialGroupedShippingDetail()
                    .get(0)
                    .getBaseShippingDetail()
                    .getItemIdentifiers()
                    .getItemDescription());

                found = true;

                break;
              }
            }

            if (found) {
              historyDocument.setIndexValue(history);

              documentService.saveDocument(historyDocument);

              documentService.saveDocument(document);
            }
            else {
              log.error("contract {} not found.", history.getContractNumber());
            }
          }
          else if (historyDocuments == null || historyDocuments.size() == 0) {
            Document historyDocument = new Document();
            historyDocument.setOwners(document.getOwners());
            historyDocument.setFrom(document.getFrom());
            historyDocument.setTo(document.getTo());
            historyDocument.setType("HISTORY");

            document.setStatusWithEnumValue(DocumentStatus.SENT);
            historyDocument.setStatusWithEnumValue(DocumentStatus.SENT);

            History history = new History();
            history.setContractNumber(((Order) indexable).getOrderHeader()
                .getOrderReferences()
                .getContractReferences()
                .getContract()
                .get(0)
                .getContractID()
                .getIdent());
            history.setDeliveryDate(((Order) indexable).getOrderHeader()
                .getOrderDates()
                .getRequestedDeliverByDate());
            history.setDpoNumber(((Order) indexable).getOrderHeader()
                .getOrderNumber()
                .getSellerOrderNumber());
            history.setOrderNumber(((Order) indexable).getOrderHeader()
                .getOrderNumber()
                .getBuyerOrderNumber());

            if (((Order) indexable).getOrderHeader()
                .getOrderReferences()
                .getOtherOrderReferences() != null) {
              history.setWebNumber(((Order) indexable).getOrderHeader()
                  .getOrderReferences()
                  .getOtherOrderReferences()
                  .getReferenceCoded()
                  .get(0)
                  .getPrimaryReference()
                  .getRefNum());
            }

            if (!setWeight(history, ((Order) indexable).getOrderDetail()
                .getListOfItemDetail()
                .getItemDetail()
                .get(0)
                .getBaseItemDetail()
                .getTotalQuantity())) {
              log.error("Contract {} without weight information.", history.getContractNumber());
              break;
            }

            boolean found = false;

            for (Contract contract : contracts) {
              if (history.getContractNumber()
                  .equals(contract.getShippingScheduleHeader()
                      .getScheduleID())) {
                history.setFromPlace(contract.getShippingScheduleHeader()
                    .getScheduleParty()
                    .getShipFromParty()
                    .getNameAddress()
                    .getCity());
                history.setSaleCondition(contract.getShippingScheduleHeader()
                    .getTermsOfDelivery()
                    .getShipmentMethodOfPaymentCodedOther());

                if (StringUtils.isNotBlank(contract.getShippingScheduleHeader()
                    .getScheduleParty()
                    .getBuyerParty()
                    .getNameAddress()
                    .getName1())) {
                  history.setToName(contract.getShippingScheduleHeader()
                      .getScheduleParty()
                      .getBuyerParty()
                      .getNameAddress()
                      .getName1());
                }
                else {
                  Company company = new Company();
                  company.setCode(document.getOwners());
                  Partner partner = securityService.getPartner(document.getTo(), company);

                  if (partner != null) {
                    history.setToName(partner.getFullname());
                  }
                }

                history.setProductDescription(contract.getListOfMaterialGroupedShippingDetail()
                    .getMaterialGroupedShippingDetail()
                    .get(0)
                    .getBaseShippingDetail()
                    .getItemIdentifiers()
                    .getItemDescription());

                found = true;

                break;
              }
            }

            if (found) {
              historyDocument.setIndexValue(history);

              documentService.saveDocument(historyDocument);

              documentService.saveDocument(document);
            }
            else {
              log.error("contract {} not found.", history.getContractNumber());
            }
          }
        }
      }
    }

    return new ArrayList<Document>();
  }

  @Override
  public List<Document> updateHistory(List<Document> documents) {
    for (Document document : documents) {
      log.info("Search order number {}", document.getFileReference());
      Indexable indexable = document.getIndex()
          .getValue();

      QueryBuilder contractQb = createBuilder();
      contractQb.and(equal(Document_.owners.getName(), document.getOwners()));
      contractQb.and(equal(Document_.to.getName(), document.getTo()));
      List<Contract> contracts = this.documentService.searchIndexables(Contract.class, contractQb.query(), null)
          .getContent();

      QueryBuilder qb = createBuilder();

      if (contracts != null && contracts.size() > 0) {
        if (DocumentType.ORDRSP.name()
            .equals(document.getType())) {
          qb.and(equal(Document_.owners.getName(), document.getOwners()));
          qb.and(equal(Document_.to.getName(), document.getTo()));
          qb.and(equal("contractNumber", ((OrderResponse) indexable).getOrderResponseHeader()
              .getOriginalOrderHeaderWithChanges()
              .getOrderReferences()
              .getContractReferences()
              .getContract()
              .get(0)
              .getContractID()
              .getIdent()));
          if (((OrderResponse) indexable).getOrderResponseHeader()
              .getOriginalOrderHeaderWithChanges()
              .getOrderReferences()
              .getOtherOrderReferences() != null) {
            qb.and(equal("webNumber", ((OrderResponse) indexable).getOrderResponseHeader()
                .getOriginalOrderHeaderWithChanges()
                .getOrderReferences()
                .getOtherOrderReferences()
                .getReferenceCoded()
                .get(0)
                .getPrimaryReference()
                .getRefNum()));
          }
          else {
            qb.and(equal("dpoNumber", ((OrderResponse) indexable).getOrderResponseHeader()
                .getOrderResponseNumber()
                .getSellerOrderResponseNumber()));
          }

          List<Document> foundDocuments = this.documentService.searchIndexedDocuments(Document.class,
              History.class, qb.query(), null);

          if (foundDocuments != null && foundDocuments.size() == 1) {
            log.info("Order {} found", document.getFileReference());
            log.info("Previous status: {}", foundDocuments.get(0)
                .getStatus());
            Document historyDocument = foundDocuments.get(0);

            if (DocumentStatus.PENDING.equals(historyDocument.getStatusAsEnumValue()) ||
                DocumentStatus.SENT.equals(historyDocument.getStatusAsEnumValue()) ||
                DocumentStatus.ACCEPTED.equals(historyDocument.getStatusAsEnumValue())) {
              switch (((OrderResponse) indexable).getOrderResponseDetail()
                  .getListOfOrderResponseItemDetail()
                  .getOrderResponseItemDetail()
                  .get(0)
                  .getItemDetailResponseCoded()) {
              case ACCEPTED_WITH_AMENDMENT:
                document.setStatusWithEnumValue(DocumentStatus.ACCEPTED_WITH_AMENDMENT);
                historyDocument.setStatusWithEnumValue(DocumentStatus.ACCEPTED_WITH_AMENDMENT);
                break;
              case ITEM_REJECTED:
                document.setStatusWithEnumValue(DocumentStatus.REFUSED);
                historyDocument.setStatusWithEnumValue(DocumentStatus.REFUSED);
                log.info("New status: {}", DocumentStatus.REFUSED);
                break;
              case CANCELLED:
                document.setStatusWithEnumValue(DocumentStatus.CANCEL);
                historyDocument.setStatusWithEnumValue(DocumentStatus.CANCEL);
                log.info("New status: {}", DocumentStatus.CANCEL);
                break;
              case ITEM_DELETED:
                document.setStatusWithEnumValue(DocumentStatus.REMOVED);
                historyDocument.setStatusWithEnumValue(DocumentStatus.REMOVED);
                log.info("New status: {}", DocumentStatus.REMOVED);
                break;
              case ITEM_ACCEPTED:
              default:
                document.setStatusWithEnumValue(DocumentStatus.ACCEPTED);
                historyDocument.setStatusWithEnumValue(DocumentStatus.ACCEPTED);
                log.info("New status: {}", DocumentStatus.ACCEPTED);
                break;
              }

              History history = historyDocument.getIndexValue();

              history.setDeliveryDate(((OrderResponse) indexable).getOrderResponseHeader()
                  .getOriginalOrderHeaderWithChanges()
                  .getOrderDates()
                  .getRequestedDeliverByDate());
              history.setDpoNumber(((OrderResponse) indexable).getOrderResponseHeader()
                  .getOrderResponseNumber()
                  .getSellerOrderResponseNumber());
              if (((OrderResponse) indexable).getOrderResponseHeader()
                  .getOriginalOrderHeaderWithChanges()
                  .getOrderReferences()
                  .getOtherOrderReferences() != null) {
                history.setWebNumber(((OrderResponse) indexable).getOrderResponseHeader()
                    .getOriginalOrderHeaderWithChanges()
                    .getOrderReferences()
                    .getOtherOrderReferences()
                    .getReferenceCoded()
                    .get(0)
                    .getPrimaryReference()
                    .getRefNum());
              }

              if (!setWeight(history, ((OrderResponse) indexable).getOrderResponseDetail()
                  .getListOfOrderResponseItemDetail()
                  .getOrderResponseItemDetail()
                  .get(0)
                  .getOriginalItemDetailWithChanges()
                  .getBaseItemDetail()
                  .getResponseQuantity())) {
                log.error("Order response {} without weight information.", history.getContractNumber());
                break;
              }
              documentService.saveDocument(historyDocument);

              documentService.saveDocument(document);
            }
          }
          else if (foundDocuments == null || foundDocuments.size() == 0) {
            log.info("Previous status: {}", document.getStatus());
            Document historyDocument = new Document();
            historyDocument.setOwners(document.getOwners());
            historyDocument.setFrom(document.getFrom());
            historyDocument.setTo(document.getTo());
            historyDocument.setType("HISTORY");

            switch (((OrderResponse) indexable).getOrderResponseDetail()
                .getListOfOrderResponseItemDetail()
                .getOrderResponseItemDetail()
                .get(0)
                .getItemDetailResponseCoded()) {
            case ACCEPTED_WITH_AMENDMENT:
              document.setStatusWithEnumValue(DocumentStatus.ACCEPTED_WITH_AMENDMENT);
              historyDocument.setStatusWithEnumValue(DocumentStatus.ACCEPTED_WITH_AMENDMENT);
              log.info("New status: {}", DocumentStatus.ACCEPTED_WITH_AMENDMENT);
              break;
            case ITEM_REJECTED:
              document.setStatusWithEnumValue(DocumentStatus.REFUSED);
              historyDocument.setStatusWithEnumValue(DocumentStatus.REFUSED);
              log.info("New status: {}", DocumentStatus.REFUSED);
              break;
            case CANCELLED:
              document.setStatusWithEnumValue(DocumentStatus.CANCEL);
              historyDocument.setStatusWithEnumValue(DocumentStatus.CANCEL);
              log.info("New status: {}", DocumentStatus.CANCEL);
              break;
            case ITEM_DELETED:
              document.setStatusWithEnumValue(DocumentStatus.REMOVED);
              historyDocument.setStatusWithEnumValue(DocumentStatus.REMOVED);
              log.info("New status: {}", DocumentStatus.REMOVED);
              break;
            case ITEM_ACCEPTED:
            default:
              document.setStatusWithEnumValue(DocumentStatus.ACCEPTED);
              historyDocument.setStatusWithEnumValue(DocumentStatus.ACCEPTED);
              log.info("New status: {}", DocumentStatus.ACCEPTED);
              break;
            }

            History history = new History();
            history.setContractNumber(((OrderResponse) indexable).getOrderResponseHeader()
                .getOriginalOrderHeaderWithChanges()
                .getOrderReferences()
                .getContractReferences()
                .getContract()
                .get(0)
                .getContractID()
                .getIdent());
            history.setDeliveryDate(((OrderResponse) indexable).getOrderResponseHeader()
                .getOriginalOrderHeaderWithChanges()
                .getOrderDates()
                .getRequestedDeliverByDate());
            history.setDpoNumber(((OrderResponse) indexable).getOrderResponseHeader()
                .getOrderResponseNumber()
                .getSellerOrderResponseNumber());
            history.setOrderNumber(((OrderResponse) indexable).getOrderResponseHeader()
                .getOrderReference()
                .getRefNum());
            if (((OrderResponse) indexable).getOrderResponseHeader()
                .getOriginalOrderHeaderWithChanges()
                .getOrderReferences()
                .getOtherOrderReferences() != null) {
              history.setWebNumber(((OrderResponse) indexable).getOrderResponseHeader()
                  .getOriginalOrderHeaderWithChanges()
                  .getOrderReferences()
                  .getOtherOrderReferences()
                  .getReferenceCoded()
                  .get(0)
                  .getPrimaryReference()
                  .getRefNum());
            }

            if (!setWeight(history, ((OrderResponse) indexable).getOrderResponseDetail()
                .getListOfOrderResponseItemDetail()
                .getOrderResponseItemDetail()
                .get(0)
                .getOriginalItemDetailWithChanges()
                .getBaseItemDetail()
                .getResponseQuantity())) {
              log.error("Contract {} without weight information.", history.getContractNumber());
              break;
            }
            boolean found = false;

            for (Contract contract : contracts) {
              if (history.getContractNumber()
                  .equals(contract.getShippingScheduleHeader()
                      .getScheduleID())) {
                history.setFromPlace(contract.getShippingScheduleHeader()
                    .getScheduleParty()
                    .getShipFromParty()
                    .getNameAddress()
                    .getCity());
                history.setSaleCondition(contract.getShippingScheduleHeader()
                    .getTermsOfDelivery()
                    .getShipmentMethodOfPaymentCodedOther());

                if (StringUtils.isNotBlank(contract.getShippingScheduleHeader()
                    .getScheduleParty()
                    .getBuyerParty()
                    .getNameAddress()
                    .getName1())) {
                  history.setToName(contract.getShippingScheduleHeader()
                      .getScheduleParty()
                      .getBuyerParty()
                      .getNameAddress()
                      .getName1());
                }
                else {
                  Company company = new Company();
                  company.setCode(document.getOwners());
                  Partner partner = securityService.getPartner(document.getTo(), company);

                  if (partner != null) {
                    history.setToName(partner.getFullname());
                  }
                }

                history.setProductDescription(contract.getListOfMaterialGroupedShippingDetail()
                    .getMaterialGroupedShippingDetail()
                    .get(0)
                    .getBaseShippingDetail()
                    .getItemIdentifiers()
                    .getItemDescription());

                found = true;

                break;
              }
            }

            if (found) {
              historyDocument.setIndexValue(history);

              documentService.saveDocument(historyDocument);

              documentService.saveDocument(document);
            }
            else {
              log.error("contract {} not found.", history.getContractNumber());
            }
          }
          else {
            log.warn("Order {} not found", document.getFileReference());
          }
        }
        else if (DocumentType.DESADV.name()
            .equals(document.getType())) {
              qb.and(equal(Document_.owners.getName(), document.getOwners()));
              qb.and(equal(Document_.to.getName(), document.getTo()));
              qb.and(equal("contractNumber", ((AdvanceShipmentNotice) indexable).getASNHeader()
                  .getShippingReferences()
                  .getContractNumber()
                  .getRefNum()));

              String dpoNumber = "";

              List<ASNOrderNumberType> asnOrders = ((AdvanceShipmentNotice) indexable).getASNHeader()
                  .getASNOrderNumber();

              if (asnOrders.size() == 1) {
                dpoNumber = asnOrders.get(0)
                    .getBuyerOrderNumber();
              }
              else {
                dpoNumber = ((AdvanceShipmentNotice) indexable).getASNHeader()
                    .getASNNumber();
              }

              qb.and(equal("dpoNumber", dpoNumber));

              List<Document> foundDocuments = this.documentService.searchIndexedDocuments(Document.class,
                  History.class, qb.query(), null);

              if (foundDocuments != null && foundDocuments.size() == 1) {
                Document historyDocument = foundDocuments.get(0);

                if (DocumentStatus.ACCEPTED.equals(historyDocument.getStatusAsEnumValue()) ||
                    DocumentStatus.ACCEPTED_WITH_AMENDMENT.equals(historyDocument.getStatusAsEnumValue())) {
                  historyDocument.setStatusWithEnumValue(DocumentStatus.DELIVERED);

                  History history = historyDocument.getIndexValue();

                  history.setDeliveryDate(((AdvanceShipmentNotice) indexable).getASNHeader()
                      .getASNIssueDate());
                  if (!setWeight(history, ((AdvanceShipmentNotice) indexable).getASNDetail()
                      .getListOfASNItemDetail()
                      .getASNItemDetail()
                      .get(0)
                      .getASNBaseItemDetail()
                      .getASNQuantities()
                      .getShippedQuantity())) {
                    log.error("Desadv {} without weight information.", history.getContractNumber());
                    break;
                  }

                  documentService.saveDocument(historyDocument);
                }
              }
              else if (foundDocuments == null || foundDocuments.size() == 0) {
                Document historyDocument = new Document();
                historyDocument.setOwners(document.getOwners());
                historyDocument.setFrom(document.getFrom());
                historyDocument.setTo(document.getTo());
                historyDocument.setType("HISTORY");
                historyDocument.setStatusWithEnumValue(DocumentStatus.DELIVERED);

                History history = new History();
                history.setContractNumber(((AdvanceShipmentNotice) indexable).getASNHeader()
                    .getShippingReferences()
                    .getContractNumber()
                    .getRefNum());
                history.setDeliveryDate(((AdvanceShipmentNotice) indexable).getASNHeader()
                    .getASNIssueDate());
                history.setDpoNumber(((AdvanceShipmentNotice) indexable).getASNHeader()
                    .getASNNumber());
                history.setOrderNumber(((AdvanceShipmentNotice) indexable).getASNHeader()
                    .getASNOrderNumber()
                    .get(0)
                    .getBuyerOrderNumber());
                history.setProductDescription(((AdvanceShipmentNotice) indexable).getASNDetail()
                    .getListOfASNItemDetail()
                    .getASNItemDetail()
                    .get(0)
                    .getASNBaseItemDetail()
                    .getItemIdentifiers()
                    .getItemDescription());

                if (!setWeight(history, ((AdvanceShipmentNotice) indexable).getASNDetail()
                    .getListOfASNItemDetail()
                    .getASNItemDetail()
                    .get(0)
                    .getASNBaseItemDetail()
                    .getASNQuantities()
                    .getShippedQuantity())) {
                  log.error("Desadv {} without weight information.", history.getContractNumber());
                  break;
                }

                boolean found = false;

                for (Contract contract : contracts) {
                  if (history.getContractNumber()
                      .equals(contract.getShippingScheduleHeader()
                          .getScheduleID())) {
                    history.setFromPlace(contract.getShippingScheduleHeader()
                        .getScheduleParty()
                        .getShipFromParty()
                        .getNameAddress()
                        .getCity());
                    history.setSaleCondition(contract.getShippingScheduleHeader()
                        .getTermsOfDelivery()
                        .getShipmentMethodOfPaymentCodedOther());

                    if (StringUtils.isNotBlank(contract.getShippingScheduleHeader()
                        .getScheduleParty()
                        .getBuyerParty()
                        .getNameAddress()
                        .getName1())) {
                      history.setToName(contract.getShippingScheduleHeader()
                          .getScheduleParty()
                          .getBuyerParty()
                          .getNameAddress()
                          .getName1());
                    }
                    else {
                      Company company = new Company();
                      company.setCode(document.getOwners());
                      Partner partner = securityService.getPartner(document.getTo(), company);

                      if (partner != null) {
                        history.setToName(partner.getFullname());
                      }
                    }

                    found = true;

                    break;
                  }
                }

                if (found) {
                  historyDocument.setIndexValue(history);

                  documentService.saveDocument(historyDocument);
                }
                else {
                  log.error("contract {} not found.", history.getContractNumber());
                }
              }
            }
      }

      qb = null;
    }

    return new ArrayList<Document>();
  }

  @Override
  public void splitPreInvoice(List<Document> documents) {
    List<Document> invoiceDocuments = new ArrayList<Document>();

    for (Document document : documents) {
      // Generation du pdf

      if (isNotEmpty(this.documentService.search(createBuilder()
          .and(equal("reference", document.getReference()), equal("to", document.getTo()))
          .query()))) {
        log.warn("Duplicate document: {}", document);
        document.setStatusWithEnumValue(DocumentStatus.DUPLICATE);
        document.setWarning("Duplicate document");
        // documentService.saveDocument(document);
      }

      Indexable indexable = document.getIndex()
          .getValue();

      if (indexable instanceof Invoice) {
        for (InvoiceItemDetailType invoiceItemDetail : ((Invoice) indexable).getInvoiceDetail()
            .getListOfInvoiceItemDetail()
            .getInvoiceItemDetail()) {
          Invoice invoice = new Invoice();
          Invoice clone = SerializationUtils.clone((Invoice) indexable);
          invoice.setInvoiceHeader(clone.getInvoiceHeader());

          InvoiceDetailType invoiceDetail = new InvoiceDetailType();
          ListOfInvoiceItemDetailType listOfInvoiceItemDetail = new ListOfInvoiceItemDetailType();
          listOfInvoiceItemDetail.getInvoiceItemDetail()
              .add(SerializationUtils.clone(invoiceItemDetail));
          invoiceDetail.setListOfInvoiceItemDetail(listOfInvoiceItemDetail);
          invoice.setInvoiceDetail(invoiceDetail);

          invoice.setInvoiceSummary(clone.getInvoiceSummary());

          Document invoiceDocument = new Document();
          invoiceDocument.setOwners(document.getOwners());
          invoiceDocument.setFrom(document.getFrom());
          invoiceDocument.setTo(document.getTo());
          invoiceDocument.setType(DocumentType.CONPVA);
          invoiceDocument.setCreationDate(document.getCreationDate());
          invoiceDocument.setModificationDate(document.getCreationDate());
          invoiceDocument.setStatus(document.getStatus());
          invoiceDocument.setParent(document);
          invoiceDocument.setIndexValue(invoice);

          // invoiceDocument.addFile(new DocumentFile(file, FileType.XML, null, null, invoiceDocument, DocumentType.INVOIC.toString()));

          invoiceDocuments.add(invoiceDocument);
        }

        documentService.saveDocuments(invoiceDocuments);
        invoiceDocuments.clear();
      }
    }
  }

  @Override
  public List<Document> updateAdvanceLate(List<Document> documents) {
    Calendar cal = Calendar.getInstance();

    if (documents != null) {
      for (Document document : documents) {
        if (document.getIndexValue() instanceof Contract) {
          Contract contract = ((Contract) document.getIndexValue());

          QueryBuilder contractQb = createBuilder();
          contractQb.and(equal(Document_.owners.getName(), document.getOwners()));
          contractQb.and(equal(Document_.to.getName(), document.getTo()));
          contractQb.and(equal("shippingScheduleHeader.scheduleID", contract.getShippingScheduleHeader()
              .getScheduleID()));

          List<Contract> contracts = this.documentService.searchIndexables(Contract.class, contractQb.query(), null)
              .getContent();

          BigDecimal firstOfMonthAdvance = null;
          BigDecimal firstOfMonthLate = null;
          BigDecimal firstOfMonthQuantityAwaitingDelivery = null;
          BigDecimal advance = null;
          BigDecimal late = null;
          BigDecimal quantityAwaitingDelivery = null;

          if (contracts == null || contracts.size() == 0) {
            List<QuantityCodedType> contractQuantities = contract.getListOfMaterialGroupedShippingDetail()
                .getMaterialGroupedShippingDetail()
                .get(0)
                .getBaseShippingDetail()
                .getListOfQuantityCoded()
                .getQuantityCoded();

            for (QuantityCodedType contractQuantity : contractQuantities) {
              BigDecimal quantity = of(contractQuantity).map(QuantityCodedType::getQuantityValue)
                  .map(QuantityValueType::getValue)
                  .orElse(null);
              if (ADVANCE.equals(contractQuantity.getQuantityQualifierCoded())) {
                advance = quantity;
              }
              else if (LATE.equals(contractQuantity.getQuantityQualifierCoded())) {
                late = quantity;
              }
            }

            if (advance == null) {
              QuantityCodedType advanceQuantityCoded = new QuantityCodedType();
              advanceQuantityCoded.setQuantityQualifierCoded(ADVANCE);

              QuantityValueType advanceQuantityValue = new QuantityValueType();
              advanceQuantityValue.setValue(BigDecimal.ZERO);
              advanceQuantityCoded.setQuantityValue(advanceQuantityValue);

              UnitOfMeasurementType initOfMeasurement = new UnitOfMeasurementType();
              initOfMeasurement.setUOMCoded("KGM");
              advanceQuantityCoded.setUnitOfMeasurement(initOfMeasurement);

              contractQuantities.add(advanceQuantityCoded);
            }

            if (late == null) {
              QuantityCodedType lateQuantityCoded = new QuantityCodedType();
              lateQuantityCoded.setQuantityQualifierCoded(LATE);

              QuantityValueType lateQuantityValue = new QuantityValueType();
              lateQuantityValue.setValue(BigDecimal.ZERO);
              lateQuantityCoded.setQuantityValue(lateQuantityValue);

              UnitOfMeasurementType initOfMeasurement = new UnitOfMeasurementType();
              initOfMeasurement.setUOMCoded("KGM");
              lateQuantityCoded.setUnitOfMeasurement(initOfMeasurement);

              contractQuantities.add(lateQuantityCoded);
            }
          }
          else if (contracts.size() == 1) {
            Calendar modificationDate = Calendar.getInstance();
            Document contractDocument = documentService.getDocument(contracts.get(0));

            modificationDate.setTimeInMillis(contractDocument.getCreationDate()
                .getTime());

            if (cal.get(Calendar.MONTH) != modificationDate.get(Calendar.MONTH)) {
              List<QuantityCodedType> contractQuantities = contract.getListOfMaterialGroupedShippingDetail()
                  .getMaterialGroupedShippingDetail()
                  .get(0)
                  .getBaseShippingDetail()
                  .getListOfQuantityCoded()
                  .getQuantityCoded();

              for (QuantityCodedType contractQuantity : contractQuantities) {
                BigDecimal quantity = of(contractQuantity).map(QuantityCodedType::getQuantityValue)
                    .map(QuantityValueType::getValue)
                    .orElse(null);
                if (ADVANCE.equals(contractQuantity.getQuantityQualifierCoded())) {
                  advance = quantity;
                }
                else if (LATE.equals(contractQuantity.getQuantityQualifierCoded())) {
                  late = quantity;
                }
              }

              if (advance == null) {
                QuantityCodedType advanceQuantityCoded = new QuantityCodedType();
                advanceQuantityCoded.setQuantityQualifierCoded(ADVANCE);

                QuantityValueType advanceQuantityValue = new QuantityValueType();
                advanceQuantityValue.setValue(BigDecimal.ZERO);
                advanceQuantityCoded.setQuantityValue(advanceQuantityValue);

                UnitOfMeasurementType initOfMeasurement = new UnitOfMeasurementType();
                initOfMeasurement.setUOMCoded("KGM");
                advanceQuantityCoded.setUnitOfMeasurement(initOfMeasurement);

                contractQuantities.add(advanceQuantityCoded);
              }

              if (late == null) {
                QuantityCodedType lateQuantityCoded = new QuantityCodedType();
                lateQuantityCoded.setQuantityQualifierCoded(LATE);

                QuantityValueType lateQuantityValue = new QuantityValueType();
                lateQuantityValue.setValue(BigDecimal.ZERO);
                lateQuantityCoded.setQuantityValue(lateQuantityValue);

                UnitOfMeasurementType initOfMeasurement = new UnitOfMeasurementType();
                initOfMeasurement.setUOMCoded("KGM");
                lateQuantityCoded.setUnitOfMeasurement(initOfMeasurement);

                contractQuantities.add(lateQuantityCoded);
              }
            }
            else {
              List<QuantityCodedType> firstOfMonthQuantities = contracts.get(0)
                  .getListOfMaterialGroupedShippingDetail()
                  .getMaterialGroupedShippingDetail()
                  .get(0)
                  .getBaseShippingDetail()
                  .getListOfQuantityCoded()
                  .getQuantityCoded();

              List<QuantityCodedType> contractQuantities = contract.getListOfMaterialGroupedShippingDetail()
                  .getMaterialGroupedShippingDetail()
                  .get(0)
                  .getBaseShippingDetail()
                  .getListOfQuantityCoded()
                  .getQuantityCoded();

              for (QuantityCodedType contractQuantity : firstOfMonthQuantities) {
                BigDecimal quantity = of(contractQuantity).map(QuantityCodedType::getQuantityValue)
                    .map(QuantityValueType::getValue)
                    .orElse(null);
                if (ADVANCE.equals(contractQuantity.getQuantityQualifierCoded())) {
                  firstOfMonthAdvance = quantity;
                }
                else if (LATE.equals(contractQuantity.getQuantityQualifierCoded())) {
                  firstOfMonthLate = quantity;
                }
                else if (QUANTITY_AWAITING_DELIVERY.equals(contractQuantity.getQuantityQualifierCoded()))
                  firstOfMonthQuantityAwaitingDelivery = quantity;
              }

              for (QuantityCodedType contractQuantity : contractQuantities) {
                BigDecimal quantity = of(contractQuantity).map(QuantityCodedType::getQuantityValue)
                    .map(QuantityValueType::getValue)
                    .orElse(null);
                if (ADVANCE.equals(contractQuantity.getQuantityQualifierCoded())) {
                  advance = quantity;
                }
                else if (LATE.equals(contractQuantity.getQuantityQualifierCoded())) {
                  late = quantity;
                }
                else if (QUANTITY_AWAITING_DELIVERY.equals(contractQuantity.getQuantityQualifierCoded()))
                  quantityAwaitingDelivery = quantity;
              }

              if (advance == null) {
                QuantityCodedType advanceQuantityCoded = new QuantityCodedType();
                advanceQuantityCoded.setQuantityQualifierCoded(ADVANCE);

                QuantityValueType advanceQuantityValue = new QuantityValueType();
                advanceQuantityValue.setValue(firstOfMonthAdvance != null ? firstOfMonthAdvance : BigDecimal.ZERO);
                advanceQuantityCoded.setQuantityValue(advanceQuantityValue);

                UnitOfMeasurementType initOfMeasurement = new UnitOfMeasurementType();
                initOfMeasurement.setUOMCoded("KGM");
                advanceQuantityCoded.setUnitOfMeasurement(initOfMeasurement);

                contractQuantities.add(advanceQuantityCoded);
              }
              else {
                for (QuantityCodedType contractQuantity : contractQuantities) {
                  if (ADVANCE.equals(contractQuantity.getQuantityQualifierCoded())) {
                    contractQuantity.getQuantityValue()
                        .setValue(firstOfMonthAdvance != null ? firstOfMonthAdvance : BigDecimal.ZERO);
                  }
                }
              }

              if (late == null) {
                QuantityCodedType lateQuantityCoded = new QuantityCodedType();
                lateQuantityCoded.setQuantityQualifierCoded(LATE);

                QuantityValueType lateQuantityValue = new QuantityValueType();
                lateQuantityValue.setValue(firstOfMonthLate != null ? firstOfMonthLate : BigDecimal.ZERO);
                lateQuantityCoded.setQuantityValue(lateQuantityValue);

                UnitOfMeasurementType initOfMeasurement = new UnitOfMeasurementType();
                initOfMeasurement.setUOMCoded("KGM");
                lateQuantityCoded.setUnitOfMeasurement(initOfMeasurement);

                contractQuantities.add(lateQuantityCoded);
              }
              else {
                for (QuantityCodedType contractQuantity : contractQuantities) {
                  if (LATE.equals(contractQuantity.getQuantityQualifierCoded())) {
                    contractQuantity.getQuantityValue()
                        .setValue(firstOfMonthLate != null ? firstOfMonthLate : BigDecimal.ZERO);
                  }
                }
              }

              if (quantityAwaitingDelivery == null) {
                QuantityCodedType quantityAwaitingDeliveryCoded = new QuantityCodedType();
                quantityAwaitingDeliveryCoded.setQuantityQualifierCoded(QUANTITY_AWAITING_DELIVERY);

                QuantityValueType quantityAwaitingDeliveryValue = new QuantityValueType();
                quantityAwaitingDeliveryValue
                    .setValue(firstOfMonthQuantityAwaitingDelivery != null ? firstOfMonthQuantityAwaitingDelivery : BigDecimal.ZERO);
                quantityAwaitingDeliveryCoded.setQuantityValue(quantityAwaitingDeliveryValue);

                UnitOfMeasurementType initOfMeasurement = new UnitOfMeasurementType();
                initOfMeasurement.setUOMCoded("KGM");
                quantityAwaitingDeliveryCoded.setUnitOfMeasurement(initOfMeasurement);

                contractQuantities.add(quantityAwaitingDeliveryCoded);
              }
              else {
                for (QuantityCodedType contractQuantity : contractQuantities) {
                  if (QUANTITY_AWAITING_DELIVERY.equals(contractQuantity.getQuantityQualifierCoded())) {
                    contractQuantity.getQuantityValue()
                        .setValue(firstOfMonthQuantityAwaitingDelivery != null ? firstOfMonthQuantityAwaitingDelivery : BigDecimal.ZERO);
                  }
                }
              }
            }
          }
          else {
            log.warn("More than one contract with reference : " + contract.getShippingScheduleHeader()
                .getScheduleID());
          }
        }
      }
    }

    return documents;
  }

  @Override
  public void createUnknownProduct(String owner, List<Document> documents) {
    if (documents != null) {
      for (Document document : documents) {
        if (document.getIndexValue() instanceof Contract) {
          String partnerCode = ((Contract) document.getIndexValue()).getShippingScheduleHeader()
              .getScheduleParty()
              .getBuyerParty()
              .getPartyID()
              .getIdent();

          String productCode = ((Contract) document.getIndexValue()).getListOfMaterialGroupedShippingDetail()
              .getMaterialGroupedShippingDetail()
              .get(0)
              .getBaseShippingDetail()
              .getItemIdentifiers()
              .getPartNumbers()
              .getSellerPartNumber()
              .getPartID();

          String productName = ((Contract) document.getIndexValue()).getListOfMaterialGroupedShippingDetail()
              .getMaterialGroupedShippingDetail()
              .get(0)
              .getBaseShippingDetail()
              .getItemIdentifiers()
              .getItemDescription();

          Company company = securityService.getCompanyByCode(owner);

          if (company != null) {
            Partner partner = securityService.getPartner(partnerCode, company);

            if (partner != null) {
              List<Product> products = repositoryService.findAllByGroup(Product.class, partner);

              Optional<Product> productOpt = products.stream()
                  .filter(p -> productCode.equals(p.getReference()))
                  .findFirst();

              if (productOpt.isPresent()) {
                Product product = productOpt.get();
                if (!product.getProductName()
                    .equals(productName)) {
                  product.setProductName(productName);
                  repositoryService.save(Product.class, product);
                }
              }
              else {
                Product product = new Product();
                product.setOwners(owner);
                product.setTo(partnerCode);
                product.setProductName(productName);
                product.setReference(productCode);
                product.setCreationDate(new Date());

                repositoryService.save(Product.class, product);
              }
            }
          }
        }
      }
    }
  }

  @Override
  public List<Document> unloadOrders(List<Document> documents) throws Exception {
    List<Document> returnDocuments = new ArrayList<Document>();

    if (documents != null) {
      for (Document document : documents) {
        if (document.getIndexValue() instanceof Order) {
          String partnerCode = ((Order) document.getIndexValue()).getOrderHeader()
              .getOrderParty()
              .getBuyerParty()
              .getPartyID()
              .getIdent();

          if (((Order) document.getIndexValue()).getOrderHeader()
              .getOrderReferences()
              .getOtherOrderReferences() == null) {
            returnDocuments.add(document);
          }
          else {
            File file = new File(this.configService.getString(DATA_DIR), "deployments.txt");

            if (file != null && file.exists()) {
              InputStream inputStream = new FileInputStream(file);

              CsvStream csv = new CsvStream(new InputStreamReader(inputStream));

              boolean found = false;
              for (List<String> fields; (fields = csv.nextRecord()) != null && !found;) {
                if (!fields.isEmpty()) {
                  if (partnerCode.equalsIgnoreCase(fields.get(0))) {
                    found = true;
                  }
                }
              }

              if (!found) {
                returnDocuments.add(document);
              }

              csv.close();
            }
            else {
              returnDocuments.add(document);
            }
          }
        }
      }
    }

    return returnDocuments;
  }

  @Override
  public void disableContracts() {
    List<DocumentStatus> statusList = new ArrayList<DocumentStatus>();
    statusList.add(DocumentStatus.PENDING);
    statusList.add(DocumentStatus.SENT);

    QueryBuilder historyQb = QueryBuilder.createBuilder();
    historyQb.and(in(Document_.status.getName(), statusList));
    historyQb.asc("contractNumber");

    List<History> histories = this.documentService.searchIndexables(History.class, historyQb.query(), null)
        .getContent();

    List<String> contractsNumber = new ArrayList<String>();
    String contractNumber = null;

    QueryBuilder contractQb = QueryBuilder.createBuilder();

    if (histories != null && histories.size() > 0) {
      for (History history : histories) {
        if (StringUtils.isBlank(contractNumber) || !history.getContractNumber()
            .equals(contractNumber)) {
          contractNumber = history.getContractNumber();
          contractsNumber.add(contractNumber);
        }
      }

      contractQb.and(Clauses.clause("shippingScheduleHeader.scheduleID", NOT_IN, contractsNumber));
    }

    List<Document> contractsDocument = this.documentService.searchIndexedDocuments(Document.class, Contract.class, contractQb.query(),
        null);

    for (Document contractDocument : contractsDocument) {
      contractDocument.setStatusWithEnumValue(DocumentStatus.CANCEL);
    }

    this.documentService.saveDocuments(contractsDocument);

    log.info(contractsDocument.size() + " contracts disabled.");
  }

  @Override
  public List<HistoryProduct> getAllHistoryProducts(Group group) {
    return historyDao.getAllHistoryProducts(group);
  }

  @Override
  public List<String> findDistinctStatusFromHistory(String partnerCode) {
    return historyDao.findDistinctStatus(partnerCode);
  }

  @Override
  public List<String> findDistinctStatusFromHistory() {
    return historyDao.findDistinctStatus();
  }

  /* -- PRIVATE METHODS */
  private boolean setWeight(History history, QuantityType quantityType) {
    BigDecimal quantity = Optional.ofNullable(quantityType)
        .map(QuantityType::getQuantityValue)
        .map(QuantityValueType::getValue)
        .orElse(null);
    String contractNumber = history.getContractNumber();
    String orderNumber = history.getOrderNumber();
    if (quantity == null) {
      log.info("No weight to set for contract {} with order number {}", contractNumber, orderNumber);
      return false;
    }

    BigDecimal weight = history.getWeight();
    if (weight != null)
      log.info("Old contract {} with order number {} weight value: {}", contractNumber, orderNumber, weight);
    log.info("Setting weight for contract {} with order number {} with value: {}", contractNumber, orderNumber, quantity);
    history.setWeight(quantity);
    return true;

  }

  @Override
  public BigDecimal getTotalTonnage(Query query, BigDecimal reducer) {
    BigDecimal totalTonnage = ZERO;
    // using the sum aggregation didn't work well, mostly likely because it converts it to double not decimal,
    // and conversion operations are not available in versions less than 4
    ProjectionOperation projection = Aggregation.project("weight");
    List<DBObject> list = this.historyDao.aggregate(History.class, DBObject.class, query, projection);
    totalTonnage = list.stream()
        .map(d -> d.get("weight"))
        .filter(Objects::nonNull)
        .map(String::valueOf)
        .map(s -> new BigDecimal(s))
        .reduce(BigDecimal.ZERO, BigDecimal::add);
    totalTonnage = totalTonnage.divide(reducer);
    return totalTonnage;
  }
}
