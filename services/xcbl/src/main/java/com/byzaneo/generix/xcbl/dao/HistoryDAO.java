package com.byzaneo.generix.xcbl.dao;

import java.util.List;

import com.byzaneo.generix.xcbl.bean.*;
import com.byzaneo.security.bean.Group;
import com.byzaneo.xtrade.xcbl.dao.XcblDAO;

public interface HistoryDAO extends XcblDAO<History> {
  public static final String DAO_NAME = "xcblHistoryDAO";

  List<HistoryProduct> getAllHistoryProducts(Group group);

  List<String> findDistinctStatus(String partnerCode);

  List<String> findDistinctStatus();
}
