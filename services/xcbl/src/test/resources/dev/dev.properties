# -----------------------------------------------------------------------------
#  C I   P R O P E R T I E S
# -----------------------------------------------------------------------------

# -- D I R E C T O R I E S   L A Y O U T --
webapp.dir = /tmp/XTD
data.dir   = ${webapp.dir}/data
temp.dir   = ${data.dir}/tmp
work.dir   = ${data.dir}/work
backup.dir = ${data.dir}/backup
input.dir  = ${data.dir}/input
output.dir = ${data.dir}/output
bin.dir = ${data.dir}/bin

# -- D A T A B A S E --
database.flyway.disable = true
#  H2 (memory DB)
database.type       = h2
database.driver   = org.h2.Driver
database.url      = jdbc:h2:mem:dev;DB_CLOSE_ON_EXIT=FALSE
database.username =
database.password =
database.target   = org.hibernate.dialect.H2Dialect

database.showSql	= false
# - DDL -
# validate | update | create | create-drop
database.generateDdl		= true
database.generateDdl.mode	= create-drop
database.generateDdl.imports= /data/import.${database.type}.sql
