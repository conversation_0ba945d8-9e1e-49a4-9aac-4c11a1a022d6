package com.byzaneo.generix.signature;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.byzaneo.xtrade.bean.Document;

/**
 * This object is intended to be a cache for PKCS#11 handlers, in particular in order to avoid holding multiple connections to the device
 * and thus overflowing the max connections it can handle, since disconnection seem to be problematic while the current JVM is not closed.
 * Beware thread safety
 */
@Component
public class GnxSignatureHandlerPkcs11Holder {

  public static final String COMPONENT_NAME = "gnxSignatureHandlerPkcs11Holder";

  private Map<Document, GnxSignatureHandlerPkcs11> certificateToSignatureHandler = new HashMap<>();

  public GnxSignatureHandlerPkcs11 put(Document certificate, GnxSignatureHandlerPkcs11 gnxSignatureHandlerPkcs11) {
    return certificateToSignatureHandler.put(certificate, gnxSignatureHandlerPkcs11);
  }

  public GnxSignatureHandlerPkcs11 remove(Document certificate) {
    return certificateToSignatureHandler.remove(certificate);
  }

  public boolean containsKey(Document certificate) {
    return certificateToSignatureHandler.containsKey(certificate);
  }

  public GnxSignatureHandlerPkcs11 get(Document certificate) {
    return certificateToSignatureHandler.get(certificate);

  }
}
