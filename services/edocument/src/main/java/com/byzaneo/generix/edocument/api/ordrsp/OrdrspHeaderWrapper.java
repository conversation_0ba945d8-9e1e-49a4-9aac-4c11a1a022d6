package com.byzaneo.generix.edocument.api.ordrsp;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.byzaneo.commons.api.BeanDescriptorWrapper;
import com.byzaneo.commons.bean.BeanDescriptor;
import com.byzaneo.commons.io.BeanImportContext;
import com.byzaneo.commons.service.BeanService;
import com.byzaneo.commons.service.PropertiesException;
import com.byzaneo.commons.service.ServiceException;
import com.byzaneo.generix.edocument.bean.xcbl.CsvHeader;
import com.byzaneo.generix.edocument.util.CsvImportHelper;
import com.byzaneo.xtrade.xcbl.bean.OrderResponse;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.CurrencyCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.CurrencyType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.LanguageCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.LanguageType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PartyType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ResponseTypeCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ResponseTypeType;
import com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement.ListOfOrderResponseItemDetailType;
import com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement.ListOfOrderResponsePackageDetailType;
import com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement.OrderDatesType;
import com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement.OrderHeaderType;
import com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement.OrderPartyType;
import com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement.OrderResponseDetailType;
import com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement.OrderResponseDocTypeCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement.OrderResponseHeaderType;
import com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement.OrderResponseNumberType;

@Component("xcblOrdrspHeader")
public class OrdrspHeaderWrapper extends BeanDescriptorWrapper<CsvHeader> {

  @Autowired
  protected BeanService beanService;

  @Override
  protected BeanDescriptor createBeanDescriptor() {
    try {
      return beanService.fromClasspath("com/byzaneo/generix/edocument/bean/ordrsp/ordrspHeaderDescriptor.xml");
    }
    catch (IOException ex) {
      throw new RuntimeException(ex);
    }
  }

  @Override
  public Iterable<CsvHeader> getBeans(Object owner) {
    return null;
  }

  @Override
  public CsvHeader getBean() {
    return new CsvHeader();
  }

  @Override
  public void validate(CsvHeader bean, BeanImportContext context) throws PropertiesException {
    Map<String, Exception> exceptions = new HashMap<>();
    OrderResponse ordrsp = (OrderResponse) CsvImportHelper.getImportedIndexable();

    if (ordrsp == null) {
      ordrsp = initNewOrderResponse();
    }
    else {
      exceptions.put("multiple", new ServiceException("labels.import-xcbl.multiple_beans.", "Too much!!", null));
      CsvImportHelper.getImportedIndexable()
          .setReference("-1");
      bean = null;
      throw new PropertiesException(exceptions);
    }

    if (!exceptions.isEmpty()) {
      throw new PropertiesException(exceptions);
    }

    switch (bean.getDocumentType()) {
    case "231":
      ordrsp.getOrderResponseHeader()
          .getResponseType()
          .setResponseTypeCoded(ResponseTypeCodeType.ACCEPTED);
      break;
    default:
      exceptions.put("type",
          new ServiceException("labels.import-xcbl.unknown_ordrsp_type", "Unknown order response type %s", null, bean.getDocumentType()));
    }

    ordrsp.getOrderResponseHeader()
        .getOrderResponseNumber()
        .setBuyerOrderResponseNumber(bean.getDocumentNumber());
    ordrsp.getOrderResponseHeader()
        .getOrderResponseNumber()
        .setSellerOrderResponseNumber(bean.getDocumentNumber());

    ordrsp.getOrderResponseHeader()
        .setOrderResponseIssueDate(bean.getDocumentDate());

    if (isNotBlank(bean.getCurrency())) {
      ordrsp.getOrderResponseHeader()
          .getOriginalOrderHeaderWithChanges()
          .setOrderCurrency(new CurrencyType());
      ordrsp.getOrderResponseHeader()
          .getOriginalOrderHeaderWithChanges()
          .getOrderCurrency()
          .setCurrencyCoded(CurrencyCodeType.fromValue(bean.getCurrency()));
    }

    if (isNotBlank(bean.getLanguage())) {
      ordrsp.getOrderResponseHeader()
          .getOriginalOrderHeaderWithChanges()
          .setOrderLanguage(new LanguageType());
      ordrsp.getOrderResponseHeader()
          .getOriginalOrderHeaderWithChanges()
          .getOrderLanguage()
          .setLanguageCoded(LanguageCodeType.fromValue(bean.getLanguage()));
    }
    ordrsp.getOrderResponseHeader()
        .getOriginalOrderHeaderWithChanges()
        .setOrderDates(new OrderDatesType());
    ordrsp.getOrderResponseHeader()
        .getOriginalOrderHeaderWithChanges()
        .getOrderDates()
        .setRequestedDeliverByDate(bean.getDeliveryDate());

    if (!exceptions.isEmpty()) {
      ordrsp.setReference("-1");
      throw new PropertiesException(exceptions);
    }

    CsvImportHelper.setImportedIndexable(ordrsp);
  }

  private OrderResponse initNewOrderResponse() {
    OrderResponse ordrsp = new OrderResponse();
    // Header
    ordrsp.setOrderResponseHeader(new OrderResponseHeaderType());
    ordrsp.getOrderResponseHeader()
        .setOrderResponseNumber(new OrderResponseNumberType());
    ordrsp.getOrderResponseHeader()
        .setOrderResponseDocTypeCoded(OrderResponseDocTypeCodeType.ORDER_RESPONSE);
    ordrsp.getOrderResponseHeader()
        .setBuyerParty(new PartyType());
    ordrsp.getOrderResponseHeader()
        .setSellerParty(new PartyType());
    ordrsp.getOrderResponseHeader()
        .setResponseType(new ResponseTypeType());
    ordrsp.getOrderResponseHeader()
        .setOriginalOrderHeaderWithChanges(new OrderHeaderType());
    ordrsp.getOrderResponseHeader()
        .getOriginalOrderHeaderWithChanges()
        .setOrderParty(new OrderPartyType());

    // Detail
    ordrsp.setOrderResponseDetail(new OrderResponseDetailType());
    ordrsp.getOrderResponseDetail()
        .setListOfOrderResponseItemDetail(new ListOfOrderResponseItemDetailType());
    ordrsp.getOrderResponseDetail()
        .setListOfOrderResponsePackageDetail(new ListOfOrderResponsePackageDetailType());

    return ordrsp;
  }

  @Override
  public boolean persist(CsvHeader bean) {
    return false;
  }

}
