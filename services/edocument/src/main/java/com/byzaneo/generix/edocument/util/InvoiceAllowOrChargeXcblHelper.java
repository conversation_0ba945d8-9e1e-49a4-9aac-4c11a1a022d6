package com.byzaneo.generix.edocument.util;

import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.getCalculationGross;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.getQuantity;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.setCalculationNet;
import static com.byzaneo.generix.edocument.util.InvoiceTaxXcblHelper.createInvoiceTaxtype;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.initEuroAsDefaultCurrency;
import static com.byzaneo.generix.edocument.util.TaxXcblHelper.TAX_PRECISION;
import static com.byzaneo.generix.edocument.util.XcblHelper.BigDecimal_CENT;
import static com.byzaneo.generix.service.repository.util.product.AllowanceOrCharge.valueOf;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getTaxCategoryCodedOtherFromInvoiceTaxType;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.toComplexBigDecimalType;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.toComplexStringType;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.BasisCodeType.MONETARY_AMOUNT;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.BasisCodeType.PERCENT;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.IndicatorCodeType.SERVICE;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.MethodOfHandlingCodeType.CALCULATE_AND_ADDTO_INVOICE;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.MethodOfHandlingCodeType.CHARGE_TO_BE_PAID_BY_CUSTOMER;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.AllowOrChargeTreatmentCodeType.OTHER;
import static java.math.BigDecimal.ZERO;
import static java.math.RoundingMode.HALF_EVEN;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;
import static java.util.stream.Stream.empty;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.EMPTY;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import org.apache.commons.lang3.*;

import com.byzaneo.generix.service.repository.bean.ObjectAttribute;
import com.byzaneo.generix.service.repository.util.product.*;
import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.*;

public class InvoiceAllowOrChargeXcblHelper extends AllowOrChargeXcblHelper {

  /**
   * Make sure the SERVICES charges are placed last
   */
  private static final Comparator<InvoiceAllowOrChargeType> compareByIndicatorCoded = new Comparator<InvoiceAllowOrChargeType>() {
    @Override
    public int compare(InvoiceAllowOrChargeType a1, InvoiceAllowOrChargeType a2) {
      if (SERVICE.equals(a1.getIndicatorCoded())) return Integer.MAX_VALUE;
      else if (SERVICE.equals(a2.getIndicatorCoded())) return Integer.MIN_VALUE;
      else return a1.getIndicatorCoded()
          .compareTo(a2.getIndicatorCoded());
    }
  };

  public static BigDecimal getAllowOrChargeValue(InvoiceAllowOrChargeType charge,
      BigDecimal totalTaxableValue, int precision) {
    if (PERCENT.equals(charge.getBasisCoded())) {
      if (isNotEmpty(charge.getTax()) && charge.getTax()
          .get(0)
          .getTaxCategoryCodedOther() == null) {
        InvoiceTaxType invoiceTax = charge.getTax()
            .get(0);
        invoiceTax.setTaxCategoryCodedOther(toComplexStringType(invoiceTax.getTaxCategoryCodedOther(), "0.0"));
      }

      return getTaxableAmountForAllowanceChargePercentTypeAndCompute(totalTaxableValue,
          charge, precision);
    }
    else {
      // Allows you to manage the case where you want to delete a
      // discount/charge while you are entering another one
      BigDecimal monetaryTmp = getMonetaryValue(charge);

      monetaryTmp = monetaryTmp.setScale(precision, HALF_EVEN);
      return computeHeaderAllowanceChargeVATValue(charge, monetaryTmp, precision);
    }
  }

  private static BigDecimal getTaxableAmountForAllowanceChargePercentTypeAndCompute(
      BigDecimal totalTaxableValue, InvoiceAllowOrChargeType charge, int precision) {
    // Allows you to manage the case where you want to delete a
    // discount/charge while you are entering another
    BigDecimal value = getPercentValue(charge);
    value = value.setScale(precision, HALF_EVEN);
    // When value of allowance/charge is zero in the case of percent, don't
    // need to continue the computing based on multiply
    if (value.compareTo(ZERO) == 0) return ZERO.setScale(precision, HALF_EVEN);

    final BigDecimal percentValue = totalTaxableValue.multiply(value)
        .divide(BigDecimal_CENT, HALF_EVEN);
    return computeHeaderAllowanceChargeVATValue(charge, percentValue, precision);
  }

  public static BigDecimal computeHeaderAllowanceChargeVATValue(InvoiceAllowOrChargeType charge,
      final BigDecimal allowanceOrChargeValue, int precision) {
    if (charge.getTax() == null || charge.getTax()
        .isEmpty())
      return ZERO;
    InvoiceTaxType tax = charge.getTax()
        .get(0);
    if (StringUtils.isNotBlank(getTaxCategoryCodedOtherFromInvoiceTaxType(tax))) {
      BigDecimal valueInBG = new BigDecimal(getTaxCategoryCodedOtherFromInvoiceTaxType(tax));
      if (tax.getTaxPercent() == null) {
        tax.setTaxPercent(new PercentType());
      }
      Optional<PercentType> raxPecent = of(tax.getTaxPercent());
      raxPecent.ifPresent(taxPercent -> taxPercent.setValue(valueInBG));
      return raxPecent.map(PercentType::getValue)
          .map(e -> {
            InvoiceTaxXcblHelper.setTaxableAmount(tax, allowanceOrChargeValue, TAX_PRECISION);
            InvoiceTaxXcblHelper.setTaxAmount(tax, allowanceOrChargeValue, TAX_PRECISION);
            tax.setTaxCategoryCodedOther(toComplexStringType(tax.getTaxCategoryCodedOther(), e.toString()));
            return allowanceOrChargeValue;
          })
          .orElse(ZERO);
    }
    // tva not provided or removed so reinit tax object
    else {
      tax.setTaxableAmount(ZERO);
      tax.setTaxAmount(toComplexBigDecimalType(tax.getTaxAmount(), ZERO));
      tax.setTaxAmountInTaxAccountingCurrency(null);
      tax.setTaxCategoryCodedOther(null);
      if (PERCENT.equals(charge.getBasisCoded()))
        setPercentageMonetaryValue(charge, allowanceOrChargeValue);
      return allowanceOrChargeValue;
    }
  }

  public static InvoiceAllowOrChargeType createInvoiceAllowOrCharge() {
    InvoiceAllowOrChargeType allowOrChargeType = new InvoiceAllowOrChargeType();
    allowOrChargeType.setMethodOfHandlingCoded(CALCULATE_AND_ADDTO_INVOICE);
    InvoiceTypeOfAllowanceOrChargeType chargeType = new InvoiceTypeOfAllowanceOrChargeType();
    // PERCENT
    chargeType.setPercentageAllowanceOrCharge(getPercentageAllowanceOrChargeType(ZERO));
    // AMOUNT
    chargeType.setMonetaryValue(new InvoiceMonetaryValueType());
    allowOrChargeType.setTypeOfAllowanceOrCharge(chargeType);
    InvoiceAllowOrChgDescType allowOrChgDescType = new InvoiceAllowOrChgDescType();
    allowOrChgDescType.setListOfDescription(new ComplexStringType());
    allowOrChargeType.setAllowanceOrChargeDescription(allowOrChgDescType);
    allowOrChargeType.getTax()
        .add(createInvoiceTaxtype(of(ZERO), of(ZERO), TaxCategoryCodeType.OTHER));
    return allowOrChargeType;
  }
  
  public static InvoiceAllowOrChargeType createInvoiceParafiscalTax() {
    InvoiceAllowOrChargeType allowOrChargeType = new InvoiceAllowOrChargeType();
    allowOrChargeType.setMethodOfHandlingCoded(CHARGE_TO_BE_PAID_BY_CUSTOMER);
    AllowOrChargeTreatmentType allowOrChargeTreatmentType = new AllowOrChargeTreatmentType();
    allowOrChargeTreatmentType.setAllowOrChargeTreatmentCoded(OTHER);
    allowOrChargeTreatmentType.setAllowOrChargeTreatmentCodedOther("FirstStepOfCalculation");
    allowOrChargeType.setAllowOrChargeTreatment(allowOrChargeTreatmentType);
    InvoiceTypeOfAllowanceOrChargeType chargeType = new InvoiceTypeOfAllowanceOrChargeType();
    // PERCENT
    chargeType.setPercentageAllowanceOrCharge(getPercentageAllowanceOrChargeType(ZERO));
    // AMOUNT
    chargeType.setMonetaryValue(new InvoiceMonetaryValueType());
    allowOrChargeType.setTypeOfAllowanceOrCharge(chargeType);
    InvoiceAllowOrChgDescType allowOrChgDescType = new InvoiceAllowOrChgDescType();
    allowOrChgDescType.setListOfDescription(new ComplexStringType());
    allowOrChgDescType.setRefID(allowOrChgDescType.getRefID());
    allowOrChgDescType.setServiceCoded("Tax regulatory-tax");
    allowOrChargeType.setAllowanceOrChargeDescription(allowOrChgDescType);
    return allowOrChargeType;
  }

  public static void initAllowanceCharge(InvoiceAllowOrChargeType alwOrCharge, Invoice invoice, BigDecimal monetaryAmount,
      BigDecimal percentValue) {
    if (!ZERO.equals(monetaryAmount)) {
      initMonetaryAllowanceOrChargeToValue(alwOrCharge, invoice, monetaryAmount);
    }
    else if (!ZERO.equals(percentValue)) {
      initPercentAllowanceOrChargeToValue(alwOrCharge, invoice, percentValue);
    }

    if (alwOrCharge.getAllowanceOrChargeDescription()
        .getServiceCoded() == null)
      alwOrCharge.getAllowanceOrChargeDescription()
          .setServiceCoded(alwOrCharge.getIndicatorCoded()
              .value());
  }

  public static void initMonetaryAllowanceOrChargeToValue(
      InvoiceAllowOrChargeType alwOrCharge, Invoice invoice, BigDecimal monetaryAmount) {
    InvoiceMonetaryValueType monetary = new InvoiceMonetaryValueType();
    monetary.setCurrency(invoice != null
        ? invoice.getInvoiceHeader()
            .getPaymentCurrency()
        : initEuroAsDefaultCurrency());
    monetary.setMonetaryAmount(toComplexBigDecimalType(monetaryAmount));
    alwOrCharge.getTypeOfAllowanceOrCharge()
        .setMonetaryValue(monetary);
    alwOrCharge.setBasisCoded(MONETARY_AMOUNT);
  }

  public static void initPercentAllowanceOrChargeToValue(
      InvoiceAllowOrChargeType alwOrCharge, Invoice invoice, BigDecimal percentValue) {
    alwOrCharge.getTypeOfAllowanceOrCharge()
        .setPercentageAllowanceOrCharge(
            AllowOrChargeXcblHelper.getPercentageAllowanceOrChargeType(percentValue));
    alwOrCharge.getTypeOfAllowanceOrCharge()
        .getPercentageAllowanceOrCharge()
        .getPercentageMonetaryValue()
        .setCurrency(invoice != null
            ? invoice.getInvoiceHeader()
                .getPaymentCurrency()
            : initEuroAsDefaultCurrency());
    alwOrCharge.setBasisCoded(PERCENT);
  }

  public static void initAllowanceOrChargeToZero(InvoiceAllowOrChargeType alwOrCharge, Invoice invoice) {
    InvoiceMonetaryValueType monetary = new InvoiceMonetaryValueType();
    monetary.setCurrency(invoice != null
        ? invoice.getInvoiceHeader()
            .getPaymentCurrency()
        : initEuroAsDefaultCurrency());
    monetary.setMonetaryAmount(toComplexBigDecimalType(ZERO));
    alwOrCharge.getTypeOfAllowanceOrCharge()
        .setMonetaryValue(monetary);
    alwOrCharge.getTypeOfAllowanceOrCharge()
        .setPercentageAllowanceOrCharge(getPercentageAllowanceOrChargeType(ZERO));
    alwOrCharge.getTypeOfAllowanceOrCharge()
        .getPercentageAllowanceOrCharge()
        .getPercentageMonetaryValue()
        .setCurrency(invoice != null
            ? invoice.getInvoiceHeader()
                .getPaymentCurrency()
            : initEuroAsDefaultCurrency());
  }

  // In the case of a Carrefour invoice and Safran invoice there have to appear both percentage and amount(only 1 of them has a value, the
  // other is set to 0) while in the case of a regular invoice there has to be only 1 displayed.
  // So that is why there is a boolean parameter.
  public static void populateAllowanceOrCharge(InvoiceAllowOrChargeType allowOrChargeType, ObjectAttribute object,
      boolean bothPercentAndAmountDisplayed) {
    if (object == null)
      return;

    if (object.getCodeAttribute() != null && object.getType() != null &&
        EnumUtils.isValidEnum(AllowanceOrCharge.class, object.getCodeAttribute())) {
      InvoiceAllowOrChgDescType desc = allowOrChargeType.getAllowanceOrChargeDescription();
      desc.setListOfDescription(toComplexStringType(desc.getListOfDescription(), object.getDescription()));

      if (AllowanceOrCharge.valueOf(object.getCodeAttribute()) == AllowanceOrCharge.LINE_ITEM_TAX_REGULATORY_TAX ||
          valueOf(object.getCodeAttribute()) == AllowanceOrCharge.TAX_REGULATORY_TAX) {
        allowOrChargeType.setIndicatorCoded(IndicatorCodeType.SERVICE);
        allowOrChargeType.getAllowanceOrChargeDescription()
            .setServiceCoded(TAX_REGULATORY_TAX);
      }
      else {
        allowOrChargeType.setIndicatorCoded(IndicatorCodeType.valueOf(object.getCodeAttribute()));
        allowOrChargeType.getAllowanceOrChargeDescription()
            .setServiceCoded(IndicatorCodeType.valueOf(object.getCodeAttribute())
                .value());
      }

      if (TypeAttributeValue.valueOf(object.getType()) == TypeAttributeValue.PERCENT && allowOrChargeType.getTypeOfAllowanceOrCharge()
          .getPercentageAllowanceOrCharge() != null) {
        BigDecimal percent = object.getValue() == null ? null
            : new BigDecimal(object.getValue()
                .toString());
        InvoiceAllowOrChargeXcblHelper.setPercentValue(allowOrChargeType, percent);
        InvoiceMonetaryValueType mvt = new InvoiceMonetaryValueType();
        mvt.setMonetaryAmount(toComplexBigDecimalType(mvt.getMonetaryAmount(), BigDecimal.ZERO));
        allowOrChargeType.setBasisCoded(PERCENT);
        allowOrChargeType.getTypeOfAllowanceOrCharge()
            .setMonetaryValue(bothPercentAndAmountDisplayed ? mvt : null);
      }

      if (TypeAttributeValue.valueOf(object.getType()) == TypeAttributeValue.AMOUNT && allowOrChargeType.getTypeOfAllowanceOrCharge()
          .getMonetaryValue() != null) {
        BigDecimal amount = object.getValue() == null ? null
            : new BigDecimal(object.getValue()
                .toString());
        InvoiceAllowOrChargeXcblHelper.setMonetaryValue(allowOrChargeType, amount);
        PercentageAllowanceOrChargeType pact = new PercentageAllowanceOrChargeType();
        PercentType pt = new PercentType();
        pt.setValue(BigDecimal.ZERO);
        pact.setPercent(pt);
        allowOrChargeType.setBasisCoded(MONETARY_AMOUNT);
        allowOrChargeType.getTypeOfAllowanceOrCharge()
            .setPercentageAllowanceOrCharge(bothPercentAndAmountDisplayed ? pact : null);
      }
    }
  }

  public static void computeTaxOnInvoiceAllowanceOrCharge(InvoiceAllowOrChargeType allOrChrg, InvoiceItemDetailType detail,
      BigDecimal netPriceComputed) {

    computeInvoiceTaxOnComputedPrice(allOrChrg, detail, computeAllowenceOrCharge(allOrChrg, netPriceComputed));
  }

  /**
   * Computes the tax amount for the given allowance / charge
   * 
   * @param allOrChrg
   * @param detail
   * @param netPriceComputed
   */
  public static void computeInvoiceTaxOnComputedPrice(InvoiceAllowOrChargeType allOrChrg, InvoiceItemDetailType detail,
      BigDecimal netPriceComputed) {
    /* if not already set set a new MonetaryValueType */
    if (PERCENT.equals(allOrChrg.getBasisCoded()))
      getOptionalPercentageAllowenceOrCharge(allOrChrg).map(PERCENT_VALUE_OF_ALLOWANCE_OR_CHARGE);

    allOrChrg.getTax()
        .stream()
        .findFirst()
        .ifPresent(tax -> {
          InvoiceItemDetailXcblHelper.getFirstTax(detail)
              .ifPresent(taxDetail -> {

                BigDecimal value = netPriceComputed.multiply(getQuantity(detail).orElse(ZERO));
                tax.setTaxPercent(taxDetail.getTaxPercent());
                setTaxableAmount(tax, value, TAX_PRECISION);
                setTaxAmount(tax, value, TAX_PRECISION);
                tax.setTaxCategoryCodedOther(toComplexStringType(taxDetail.getTaxPercent()
                    .getValue()
                    .toString()));

              });
        });
  }

  public static void applyInvoiceAllowanceOrChargeOnDetail(InvoiceItemDetailType detail, int detailPrecision) {
    final AtomicReference<BigDecimal> netPriceComputed = new AtomicReference<>(getCalculationGross(detail).orElse(ZERO));

    of(detail)
        .map(InvoiceItemDetailType::getInvoicePricingDetail)
        .map(InvoicePricingDetailType::getItemAllowancesOrCharges)
        .map(ListOfInvoiceAllowOrChargeType::getAllowOrCharge)
        .map(List::stream)
        .orElse(empty())
        .sorted(compareByIndicatorCoded)
        .forEach(allOrChrg -> {
          // apply allowance and charges
          BigDecimal computedPrice = computeInvoiceAllowanceOrChargeOnSelectedDetail(allOrChrg, netPriceComputed.get());
          // compute the taxes based on that price
          computeInvoiceTaxOnComputedPrice(allOrChrg, detail, computedPrice);
          // set the new price to be used for further taxes/allowances
          netPriceComputed.set(computedPrice);
        });

    // update net price
    setCalculationNet(detail, netPriceComputed.get(), detailPrecision, HALF_EVEN);
  }

  public static BigDecimal computeInvoiceAllowanceOrChargeOnSelectedDetail(InvoiceAllowOrChargeType allowOrCharge,
      BigDecimal netPriceComputed) {
    setRightChargeAllowanceIndicatorCodeForLine(allowOrCharge);

    // calculate the PercentageMonetaryValue typeOfAllowanceOrCharge.percentageAllowanceOrCharge.percentageMonetaryValue.monetaryAmount
    if (PERCENT.equals(allowOrCharge.getBasisCoded()))
      setPercentageMonetaryValue(allowOrCharge, computePercentValue(netPriceComputed, getPercentValue(allowOrCharge)));

    // compute the new netPrice
    return computeAllowenceOrCharge(allowOrCharge, netPriceComputed);
  }

  public static void setRightChargeAllowanceIndicatorCodeForLine(InvoiceAllowOrChargeType allowOrCharge) {
    // Escape vendor freight
    if (VENDOR_FREIGHT.equalsIgnoreCase(allowOrCharge.getAllowanceOrChargeDescription()
        .getServiceCoded()))
      return;
    if (IndicatorCodeType.ALLOWANCE == allowOrCharge.getIndicatorCoded())
      allowOrCharge.setIndicatorCoded(IndicatorCodeType.LINE_ITEM_ALLOWANCE);

    else if (IndicatorCodeType.CHARGE == allowOrCharge.getIndicatorCoded())
      allowOrCharge.setIndicatorCoded(IndicatorCodeType.LINE_ITEM_CHARGE);
    
    else if (IndicatorCodeType.SERVICE == allowOrCharge.getIndicatorCoded())
      allowOrCharge.setIndicatorCoded(IndicatorCodeType.SERVICE);
  }

  public static String inline(InvoiceAllowOrChargeType allowOrCharge) {

    final StringBuilder builder = new StringBuilder(ofNullable(allowOrCharge).map(InvoiceAllowOrChargeType::getAllowanceOrChargeDescription)
        .map(InvoiceAllowOrChgDescType::getListOfDescription)
        .map(ComplexStringType::getValue)
        .map(String::trim)
        .orElse(EMPTY));

    builder.append(" : ");

    switch (of(allowOrCharge)
        .map(InvoiceAllowOrChargeType::getIndicatorCoded)
        .orElse(IndicatorCodeType.OTHER)) { // if value == null -> skip switch
    case ALLOWANCE:
    case LINE_ITEM_ALLOWANCE:
      builder.append("-");
      break;
    case CHARGE:
    case SERVICE:
      builder.append("+");
      break;
    default:
      break;
    }

    switch (of(allowOrCharge)
        .map(InvoiceAllowOrChargeType::getBasisCoded)
        .orElse(BasisCodeType.OTHER)) { // if value == null -> skip switch
    case PERCENT:
      builder.append(getPercentValue(allowOrCharge))
          .append("%");
      break;
    case MONETARY_AMOUNT:
      builder.append(getMonetaryValue(allowOrCharge));
      break;
    default:
      builder.append(ZERO);
      break;
    }
    return builder.toString();
  }

  public static InvoiceAllowOrChargeType createInvoiceTaxLines(Invoice invoice, String codeEAN, String label) {
    InvoiceAllowOrChargeType allowOrChargeType = createInvoiceAllowOrCharge();
    allowOrChargeType.setIndicatorCoded(IndicatorCodeType.SERVICE);
    allowOrChargeType.getAllowanceOrChargeDescription()
        .setRefID(codeEAN);
    allowOrChargeType.getAllowanceOrChargeDescription()
        .setListOfDescription(toComplexStringType(allowOrChargeType.getAllowanceOrChargeDescription()
            .getListOfDescription(), label));
    allowOrChargeType.getAllowanceOrChargeDescription()
        .setServiceCoded(TAX_REGULATORY_TAX);
    initAllowanceOrChargeToZero(allowOrChargeType, invoice);
    return allowOrChargeType;
  }

  /**
   * Taxable amount : net price of the item * qty
   * 
   * @param tax
   * @param price
   * @param precision
   */
  public static void setTaxableAmount(InvoiceTaxType tax, BigDecimal price, int precision) {
    tax.setTaxableAmount(price.setScale(precision, HALF_EVEN));
  }

  /**
   * TaxAmount : taxable amount * tax value / 100
   * 
   * @param tax
   * @param price
   * @param precision
   */
  public static void setTaxAmount(InvoiceTaxType tax, BigDecimal price, int precision) {
    BigDecimal taxAmount = BigDecimal.ZERO;
    if (!BigDecimal.ZERO.equals(price)) {
      taxAmount = price
          .multiply(tax.getTaxPercent()
              .getValue())
          .divide(BigDecimal_CENT, HALF_EVEN);
    }
    tax.setTaxAmount(toComplexBigDecimalType(taxAmount.setScale(precision, HALF_EVEN)));
  }

  public static Optional<InvoiceTaxType> getFirstTax(InvoiceAllowOrChargeType allCh) {
    return ofNullable(allCh)
        .map(InvoiceAllowOrChargeType::getTax)
        .map(List::stream)
        .orElse(empty())
        .findFirst();
  }

  public static BigDecimal getMonetaryValue(InvoiceAllowOrChargeType allCh) {
    return of(allCh)
        .map(InvoiceAllowOrChargeType::getTypeOfAllowanceOrCharge)
        .map(InvoiceTypeOfAllowanceOrChargeType::getMonetaryValue)
        .map(InvoiceMonetaryValueType::getMonetaryAmount)
        .map(ComplexBigDecimalType::getValue)
        .orElse(ZERO);
  }

  public static BigDecimal getPercentValue(InvoiceAllowOrChargeType allCh) {
    return getOptionalPercentageAllowenceOrCharge(allCh)
        .map(PercentageAllowanceOrChargeType::getPercent)
        .map(PercentType::getValue)
        .orElse(ZERO);
  }

  public static BigDecimal getPercentageMonetaryValue(InvoiceAllowOrChargeType allCh) {
    return getOptionalPercentageAllowenceOrCharge(allCh)
        .map(PercentageAllowanceOrChargeType::getPercentageMonetaryValue)
        .map(MonetaryValueType::getMonetaryAmount)
        .orElse(ZERO);
  }

  private static Optional<PercentageAllowanceOrChargeType> getOptionalPercentageAllowenceOrCharge(InvoiceAllowOrChargeType allCh) {
    return of(allCh)
        .map(InvoiceAllowOrChargeType::getTypeOfAllowanceOrCharge)
        .map(InvoiceTypeOfAllowanceOrChargeType::getPercentageAllowanceOrCharge);
  }

  public static void setPercentValue(InvoiceAllowOrChargeType allowOrCharge, BigDecimal value) {
    ofNullable(allowOrCharge)
        .map(InvoiceAllowOrChargeType::getTypeOfAllowanceOrCharge)
        .map(InvoiceTypeOfAllowanceOrChargeType::getPercentageAllowanceOrCharge)
        .map(PercentageAllowanceOrChargeType::getPercent)
        .ifPresent(percent -> percent.setValue(value));
  }

  public static void setMonetaryValue(InvoiceAllowOrChargeType allowOrCharge, BigDecimal value) {
    ofNullable(allowOrCharge)
        .map(InvoiceAllowOrChargeType::getTypeOfAllowanceOrCharge)
        .map(InvoiceTypeOfAllowanceOrChargeType::getMonetaryValue)
        .ifPresent(amount -> amount.setMonetaryAmount(toComplexBigDecimalType(amount.getMonetaryAmount(), value)));
  }

  public static void setPercentageMonetaryValue(InvoiceAllowOrChargeType allowOrCharge, BigDecimal value) {
    ofNullable(allowOrCharge)
        .map(InvoiceAllowOrChargeType::getTypeOfAllowanceOrCharge)
        .map(InvoiceTypeOfAllowanceOrChargeType::getPercentageAllowanceOrCharge)
        .map(PercentageAllowanceOrChargeType::getPercentageMonetaryValue)
        .ifPresent(amount -> amount.setMonetaryAmount(value));
  }

  private static BigDecimal computeAllowenceOrCharge(InvoiceAllowOrChargeType allowOrCharge, BigDecimal netPriceComputed) {
    IndicatorCodeType key = ofNullable(allowOrCharge.getIndicatorCoded()).orElse(IndicatorCodeType.OTHER);
    switch (key) {
    case LINE_ITEM_ALLOWANCE:
      netPriceComputed = computeInvoiceAllowanceOnSelectedDetail(allowOrCharge, netPriceComputed);
      break;
    case LINE_ITEM_CHARGE:
    case SERVICE:
      netPriceComputed = computeChargeOnSelectedDetail(allowOrCharge, netPriceComputed);
      break;
    default:
      break;
    }
    return netPriceComputed;
  }

  /**
   * Subtracts the monetary value (calculated percent value) from the given unitCost
   * 
   * @param allowOrCharge
   * @param unitCost
   * @return
   */
  private static BigDecimal computeInvoiceAllowanceOnSelectedDetail(InvoiceAllowOrChargeType allowOrCharge, BigDecimal unitCost) {
    BasisCodeType key = ofNullable(allowOrCharge.getBasisCoded()).orElse(BasisCodeType.OTHER);
    switch (key) {
    case MONETARY_AMOUNT:
      unitCost = unitCost.subtract(getMonetaryValue(allowOrCharge));
      break;
    case PERCENT:
      unitCost = unitCost.subtract(computePercentValue(unitCost, getPercentValue(allowOrCharge)));
      break;
    default:
      break;
    }
    return unitCost;
  }

  private static BigDecimal computeChargeOnSelectedDetail(InvoiceAllowOrChargeType allowOrCharge, BigDecimal unitCost) {
    BasisCodeType key = ofNullable(allowOrCharge.getBasisCoded()).orElse(BasisCodeType.OTHER);
    switch (key) {
    case MONETARY_AMOUNT:
      return unitCost.add(getMonetaryValue(allowOrCharge));
    case PERCENT:
      return unitCost.add(computePercentValue(unitCost, getPercentValue(allowOrCharge)));
    default:
      return unitCost;
    }
  }

  public static void clearTypeOfInvoiceAllowOrChargeType(List<InvoiceAllowOrChargeType> items) {
    items.forEach(item -> {
      if (PERCENT.equals(item.getBasisCoded()))
        item.getTypeOfAllowanceOrCharge()
            .setMonetaryValue(null);
      else if (MONETARY_AMOUNT.equals(item.getBasisCoded()))
        item.getTypeOfAllowanceOrCharge()
            .setPercentageAllowanceOrCharge(null);
    });
  }

}
