package com.byzaneo.generix.edocument.util;

import java.util.ArrayList;

import com.byzaneo.commons.bean.LabelSet;
import com.byzaneo.commons.util.GsonHelper;

/**
 * JSON-serializable wrappers for Document.error as it is completed through the process. Basic implem : ArrayList<LabelSet>
 */
@SuppressWarnings("serial")
public class EDocumentErrors extends ArrayList<LabelSet> {

  @Override
  public String toString() {
    return GsonHelper.getGson()
        .toJson(this);
  }
}