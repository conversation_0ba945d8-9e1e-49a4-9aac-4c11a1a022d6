<!DOCTYPE html SYSTEM "http://www.thymeleaf.org/dtd/xhtml1-strict-thymeleaf-4.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
<head>
<title><span th:text="${instance_name}">nom_du_portail</span> - <span th:text="${nom_client}">nom_client</span> - <span th:text="${category_name}">category_name</span> - <span th:text="${subcategory_name}">subcategory_name</span></title>
<meta charset="utf-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<style type="text/css">
    /* CLIENT-SPECIFIC STYLES */
    body, table, td, a{-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;} /* Prevent WebKit and Windows mobile changing default text sizes */
    table, td{mso-table-lspace: 0pt; mso-table-rspace: 0pt;} /* Remove spacing between tables in Outlook 2007 and up */
    img{-ms-interpolation-mode: bicubic;} /* Allow smoother rendering of resized image in Internet Explorer */

    /* RESET STYLES */
    img{border: 0; height: auto; line-height: 100%; outline: none; text-decoration: none;}
    table{border-collapse: collapse !important;}
    body{height: 100% !important; margin: 0 !important; padding: 0 !important; width: 100% !important;}

    /* iOS BLUE LINKS */
    a[x-apple-data-detectors] {
        color: inherit !important;
        text-decoration: none !important;
        font-size: inherit !important;
        font-family: inherit !important;
        font-weight: inherit !important;
        line-height: inherit !important;
    }

    /* MOBILE STYLES */
    @media screen and (max-width: 525px) {

        /* ALLOWS FOR FLUID TABLES */
        .wrapper {
          width: 100% !important;
            max-width: 100% !important;
        }

        /* ADJUSTS LAYOUT OF LOGO IMAGE */
        .logo img {
          margin: 0 auto !important;
        }

        /* USE THESE CLASSES TO HIDE CONTENT ON MOBILE */
        .mobile-hide {
          display: none !important;
        }

        .img-max {
          max-width: 100% !important;
          width: 100% !important;
          height: auto !important;
        }

        /* FULL-WIDTH TABLES */
        .responsive-table {
          width: 100% !important;
        }

        /* UTILITY CLASSES FOR ADJUSTING PADDING ON MOBILE */
        .padding {
          padding: 10px 5% 15px 5% !important;
        }

        .padding-meta {
          padding: 30px 5% 0px 5% !important;
          text-align: center;
        }

        .padding-copy {
             padding: 10px 5% 10px 5% !important;
          text-align: center;
        }

        .no-padding {
          padding: 0 !important;
        }

        .section-padding {
          padding: 50px 15px 50px 15px !important;
        }

        /* ADJUST BUTTONS ON MOBILE */
        .mobile-button-container {
            margin: 0 auto;
            width: 100% !important;
        }

        .mobile-button {
            padding: 15px !important;
            border: 0 !important;
            font-size: 16px !important;
            display: block !important;
        }

    }

    /* ANDROID CENTER FIX */
    div[style*="margin: 16px 0;"] { margin: 0 !important; }
</style>
<script type="text/javascript" src="https://gc.kis.v2.scr.kaspersky-labs.com/D823E3D5-09BC-5D40-944F-13D0260095FB/main.js" charset="UTF-8"></script></head>
	<body style="margin: 0 !important; padding: 0 !important;">


	<!-- HEADER -->
	<table border="0" cellpadding="0" cellspacing="0" width="100%">
		<tr>
			<td bgcolor="#ffffff" align="center">
				<table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 750px;" class="wrapper">
					<tr>
						<td align="center" valign="top" style="padding: 10px 0;" class="logo">
							<a th:href="@{'http://'+${instance_host_name}}" target="_blank">
						<!--<img th:attr="src=@{'https://'+${instance_host_name}+'/aio/resources/template/'+${spec_template}+'/images/logo.png'}" alt="Logo" width="30%" height="30%" style="display: block; font-family: Helvetica, Arial, sans-serif; color: #ffffff; font-size: 16px;" border="0"/>-->
								<img th:attr="src=@{'https://'+${instance_host_name}+'/aio/resources/template/generix/images/logo.png'}" alt="Logo" width="40%" border="0"/>
              </a>
						</td>
					</tr>
				</table>
			</td>
		</tr>
    <tr>
      <td bgcolor="#ffffff" align="center" style="padding: 2px;">
        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 750px;" class="responsive-table">
          <tr>
            <td>
              <!-- COPY -->
              <table width="100%" border="0" cellspacing="0" cellpadding="0">
                <tr>
                  <td align="center" style="font-size: 32px; font-family: Helvetica, Arial, sans-serif; color: #333333; padding-top: 30px;" class="padding-copy"><span th:text="#{hello}">Bonjour</span></td>
                </tr>
                <tr>
                  <td align="left" style="padding: 20px 0 0 0; font-size: 16px; line-height: 25px; font-family: Helvetica, Arial, sans-serif; color: #666666;" class="padding-copy"><span th:text="#{count_part9}">Le client</span> <span th:text="${nom_client}">nom_client</span> <span th:text="#{count_part10}">vous sollicite pour la raison suivante :</span> <span th:text="${category_name}">category_name</span> <span th:text="#{count_part11}">et</span> <span th:text="${subcategory_name}">subcategory_name</span></td>
                </tr>
                <tr>
                  <td align="left" style="padding: 20px 0 0 0; font-size: 16px; line-height: 25px; font-family: Helvetica, Arial, sans-serif; color: #666666;" class="padding-copy"><span th:text="#{count_part1}">Récapitulatif des informations envoyées:</span></td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </td>
    </tr>
	  <tr>
      <td bgcolor="#ffffff" align="center" style="padding: 15px;" class="padding">
        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 750px;" class="responsive-table">
          <tr>
            <td style="padding: 10px 0 0 0; border-top: 1px dashed #aaaaaa;">
              <!-- TWO COLUMNS -->
              <table cellspacing="0" cellpadding="0" border="0" width="100%">
                <tr>
                  <td valign="top" class="mobile-wrapper">
                    <!-- LEFT COLUMN -->
                    <table cellpadding="0" cellspacing="0" border="0" width="47%" style="width: 47%;" align="left">
                      <tr>
                        <td style="padding: 0 0 10px 0;">
                          <table cellpadding="0" cellspacing="0" border="0" width="100%">
                            <tr>
                              <td align="left" style="font-family: Arial, sans-serif; color: #333333; font-size: 16px;"><span th:text="#{count_part2}">Adresse email à laquelle lui répondre</span></td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                    </table>
                    <!-- RIGHT COLUMN -->
                    <table cellpadding="0" cellspacing="0" border="0" width="47%" style="width: 47%;" align="right">
                      <tr>
                        <td style="padding: 0 0 10px 0;">
                          <table cellpadding="0" cellspacing="0" border="0" width="100%">
                            <tr>
                              <td align="right" style="font-family: Arial, sans-serif; color: #333333; font-size: 16px;"><span th:text="${adr_email}">adr_email</span></td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
                <tr>
                  <td style="padding: 0px 0 0px 0; border-top: 1px dashed #aaaaaa;">
                  </td>
                </tr>
              </table>
            </td>
          </tr>
          <tr>
            <td style="padding: 0px 0 0px 0; border-top: 1px dashed #aaaaaa;">
            </td>
          </tr>
        </table>
      </td>
    </tr>
	 <tr>
      <td bgcolor="#ffffff" align="center" style="padding: 0px;" class="padding">
        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 750px;" class="responsive-table">
          <tr>
            <td style="padding: 5px 0 0 0; border-top: 1px;">
              <!-- TWO COLUMNS -->
              <table cellspacing="0" cellpadding="0" border="0" width="100%">
                <tr>
                  <td valign="top" class="mobile-wrapper">
                    <!-- LEFT COLUMN -->
                    <table cellpadding="0" cellspacing="0" border="0" width="47%" style="width: 47%;" align="left">
                      <tr>
                        <td style="padding: 0 0 10px 0;">
                          <table cellpadding="0" cellspacing="0" border="0" width="100%">
                            <tr>
                              <td align="left" style="font-family: Arial, sans-serif; color: #333333; font-size: 16px;"><span th:text="#{count_part3}">Message du client :</span></td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                    </table>
                  </td>
							  </tr>
						  </table>
					  </td>
				  </tr>
			  </table>
      </td>
    </tr>
	  <tr>
      <td bgcolor="#ffffff" align="center" style="padding: 0px;" class="padding">
        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 750px;" class="responsive-table">
          <tr>
					  <td style="padding: 0 0 10px 0;">
						  <table cellpadding="0" cellspacing="0" border="0" width="100%">
							  <tr>
								  <td align="left" style="font-family: Arial, sans-serif; color: #9da9b9; font-size: 16px;"><p th:utext="${#strings.replace( #strings.escapeXml( email_msg ),'&#10;','&lt;br /&gt;')}" style="margin-top: 0px; margin-bottom: 0px;" ></p></td>
							  </tr>
						  </table>
					  </td>
          </tr>
				  <tr>
					  <td style="padding: 0 0 10px 0;">
						  <table cellpadding="0" cellspacing="0" border="0" width="100%">
							  <tr>
								  <td align="left" style="font-family: Arial, sans-serif; color: #333333; font-size: 16px;"><span th:text="#{count_part4}">Une pièce jointe a pu être envoyée par le client (ci-jointe).</span></td>
							  </tr>
						  </table>
					  </td>
          </tr>
				  <tr>
            <td style="padding: 2px 0 0px 0; border-top: 1px dashed #aaaaaa;">
            </td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td bgcolor="#ffffff" align="center">
        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 750px;" class="responsive-table">
          <tr>
            <td>
              <table width="100%" border="0" cellspacing="0" cellpadding="0">
                <tr>
                  <td align="left" style="padding: 5px 0 0 0; font-size: 16px; line-height: 25px; font-family: Helvetica, Arial, sans-serif; color: #666666;" class="padding-copy">
                    <span th:text="#{count_part5}">Merci de bien vouloir traiter cette demande.</span>
                  </td>
                </tr>
							  <tr>
                  <td align="left" style="padding: 5px 0 0 0; font-size: 16px; line-height: 25px; font-family: Helvetica, Arial, sans-serif; color: #666666;" class="padding-copy">
                    <span th:text="#{count_part6}">Cordialement</span>
                  </td>
                </tr>
							  <tr>
                  <td align="left" style="padding: 5px 0 0 0; font-size: 16px; line-height: 25px; font-family: Helvetica, Arial, sans-serif; color: #666666;" class="padding-copy">
                <!--<span th:text="#{count_part7}">nom_du_portail</span>-->
                    <span th:text="${instance_name}">nom_du_portail</span>
                  </td>
                </tr>
                <tr>
                  <td align="center">
                    <!-- BULLETPROOF BUTTON -->
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                      <tr>
                        <td align="center" style="padding-top: 25px;" class="padding">
                          <table border="0" cellspacing="0" cellpadding="0" class="mobile-button-container">
                            <tr>
                              <td align="center" style="border-radius: 10px;" bgcolor="##0da9ef">
                                <a th:href="@{'http://'+${instance_host_name}}" target="_blank" style="font-size: 16px; font-family: Helvetica, Arial, sans-serif; color: #ffffff; text-decoration: none; color: #ffffff; text-decoration: none; border-radius: 3px; padding: 15px 25px; border: 0px solid #256F9C; display: inline-block;" class="mobile-button">
                                  <span th:text="#{count_part8}">Se connecter</span>
                                </a>
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td bgcolor="#ffffff" align="center" style="padding: 20px 0px;">
        <!-- UNSUBSCRIBE COPY -->
        <table width="100%" border="0" cellspacing="0" cellpadding="0" align="center" style="max-width: 750px;" class="responsive-table">
          <tr>
            <td align="center" style="font-size: 12px; line-height: 18px; font-family: Helvetica, Arial, sans-serif; color:#666666;">
          <!--<span th:text="#{count_part7}">nom_du_portail</span>-->
              <span th:text="${instance_name}" />
              <br/>
              <a th:href="@{'http://'+${instance_host_name}}" target="_blank" style="color: #666666; text-decoration: none;"><span th:text="${instance_host_name}" /></a>
            </td>
          </tr>
        </table>
      </td>
    </tr>
</table>

</body>
</html>
