package com.byzaneo.generix.rest.service.jwt.files;

import com.byzaneo.commons.event.BeanDescriptorTopicFactory;
import com.byzaneo.commons.event.EventBuilder;
import com.byzaneo.commons.event.TopicFactory;
import com.byzaneo.commons.service.ExecutorService;
import com.byzaneo.generix.bean.Template;
import com.byzaneo.generix.service.TaskService;
import com.byzaneo.generix.util.OrganizationHelper;
import com.byzaneo.generix.xtrade.util.File;
import com.byzaneo.security.bean.User;
import com.byzaneo.task.annotation.TaskEvent;
import com.byzaneo.task.api.TaskDefinition;
import com.byzaneo.task.api.TaskType;
import com.byzaneo.xtrade.api.DocumentStatus;
import com.byzaneo.xtrade.api.Report;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.ReportDocument;
import com.byzaneo.xtrade.service.DocumentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static com.byzaneo.commons.util.PersistentHelper.isPersisted;
import static com.byzaneo.generix.edocument.service.impl.IndexDataServiceImpl.log;
import static com.byzaneo.generix.util.FormPageHelper.createContext;
import static com.byzaneo.xtrade.util.DocumentBuilder.createDocument;
import static java.lang.String.valueOf;
import static java.util.Optional.ofNullable;
import static org.springframework.util.Assert.isTrue;

@Service(FileUploadService.SERVICE_NAME)
public class FileUploadServiceImpl implements FileUploadService {
  public static final String FILE_UPLOAD = "FileUpload";
  public static final String DOCUMENT_TOPIC = "document-topic";
  public static final String PORTLET_ACTION = "portlet_action";
  public static final String USER_LOGIN = "user_login";
  public static final String USER_ID = "user_id";
  public static final String GROUP_NAME = "group_name";

  @Autowired
  @Qualifier(TaskService.SERVICE_NAME)
  private transient TaskService taskService;

  @Autowired
  @Qualifier(DocumentService.SERVICE_NAME)
  private transient DocumentService documentService;

  @Autowired
  @Qualifier(ExecutorService.SERVICE_NAME)
  protected transient ExecutorService executorService;


  @Override
  public void startProcess(Long portletId, List<File> files, User user, String jsonFormPageValues, Template template, String documentType) {
    OrganizationHelper.UserOrganizations userOrg = OrganizationHelper.resolveUserOrganizations(user);
    TaskType taskType = taskService.getTaskType(portletId);
    com.byzaneo.task.bean.TaskDefinition taskDefinition = taskService.getTaskDefinition(portletId);
    CompletableFuture
        .runAsync(() ->
        {
          try {
            List<Document> documents = getDocumentsToProcess(files, userOrg.getCompanyCode(),
                userOrg.getPartner() != null ? userOrg.getPartnerCode() : userOrg.getCompanyCode(), documentType);
            EventBuilder.fromSource(documents)
                .origin(SERVICE_NAME)
                .topicFactory(topicFactory(taskDefinition, taskType))
                .context(getContext(user, jsonFormPageValues, template))
                .callbackFunction((report) -> createReport((Report) report, files))
                .authenticate()
                .publish(true);
          }
          catch (Exception e) {
            log.error("Errors while launching the process", e);
          }
        }, executorService.getExecutor());
  }

  private List<Document> getDocumentsToProcess(List<File> files, String companyCode, String primaryGroupCode,
      String documentType) {
    List<Document> documents = new ArrayList<>();
    for (File file : files) {
      ofNullable(file)
          .map(f -> createDocument()
              .reference(f.getName())
              .type(documentType)
              .owners(companyCode)
              .from(primaryGroupCode)
              .directory(f.getFile()
                  .getParentFile())
              .addExternalFile(f.getFile())
              .resolveType()
              .document()
              .build())
          .ifPresent(documents::add);
    }
    documents.forEach(document -> document.setStatusWithEnumValue(DocumentStatus.BEING_PROCESSED));
    return documents;
  }

  private Report createReport(Report report, List<File> files) {
    if (report == null)
      return null;
    for (ReportDocument doc : report.getDocuments()) {
      File file = files.stream()
          .filter(f -> Objects.equals(doc.getReference(), f.getDocumentFile()
              .getComment()))
          .findFirst()
          .orElse(null);
      if (file != null) {
        files.remove(file);
        file.setProcessReportId(report.getId());
        file.setProcessDocumentId(Optional.ofNullable(doc.getDocId())
            .map(Objects::toString)
            .orElse(null));
        documentService.setDocumentFile(file.getDocumentFile());
      }
    }
    return report;
  }

  private TopicFactory topicFactory(TaskDefinition def, TaskType taskType) throws IOException {
    isTrue(isPersisted(def), "Task has to be not null and persisted to publish event");
    final TopicFactory factory = new BeanDescriptorTopicFactory(DOCUMENT_TOPIC);
    factory.setPrefixSupplier(() -> TaskEvent.class.getSimpleName()
        .concat("/")
        .concat(taskType.getName())
        .concat("/")
        .concat(valueOf(def.getId()))
        .concat("/")
        .concat(FILE_UPLOAD));
    return factory;
  }

  private Map<String, Object> getContext(User user, String jsonFormPageValues, Template template) {
    Map<String, Object> context = new HashMap<>();
    if (template != null) {
      context = createContext(jsonFormPageValues);
    }
    context.put(PORTLET_ACTION, FILE_UPLOAD);
    if (user != null) {
      context.put(USER_LOGIN, user.getLogin());
      context.put(USER_ID, user.getId());
      context.put(GROUP_NAME, user.getPrimaryGroup()
          .getName());
    }
    return context;
  }

}
