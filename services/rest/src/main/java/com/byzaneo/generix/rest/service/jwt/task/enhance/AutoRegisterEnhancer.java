package com.byzaneo.generix.rest.service.jwt.task.enhance;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;

import lombok.Getter;

@Getter
public abstract class AutoRegisterEnhancer<T> implements TaskDefinitionEnhancer<T> {

  private transient TaskDefinitionEnhancerService enhancerService;

  @Autowired
  public void setEnhancerService(TaskDefinitionEnhancerService enhancerService) {
    this.enhancerService = enhancerService;
  }

  @PostConstruct
  private void register() {
    enhancerService.addEnhancer(getType(), this);
  }

  protected abstract Class<T> getType();
  protected abstract Class<?> getIndexableType();
}