cancel = Abbrechen
year = Jahr
ABANDONED_label = BEENDET
menu_message = Mail
channel_ftp_download = Download
channel_ftp_binary = Binary
organization_location = _location
channel_arkhineo_digitalVaultId_req = Digital vault id
info_portlet_removed = Portlet-Inhalt entfernt
themes = Themen
template_invalid_name = Der Name muss zwischen 3 und 18 Kleinbuchstaben bzw. Zahlen enthalten
organization_address_addressComplement = _address.addressComplement
company_view = Kunde {1} ({0})
invalid_file_type = Ung\u00FCltiges Dateiformat
move_left = Nach links
alphanumeric = Alphanumerisch
AtLeastOneUser = Benutzer mindestens eines Partners
channel_adressX121 = X121 address
task_adv_search_simple = Einfach
ks_comment = Beschreibung
version = Version
order_issuer = Besteller
docs_list = Dokumentenliste
folder = Ordner
configuration_page = Seite einrichten
selection = Auswahl
size = Gr\u00F6\u00DFe
stop = Stopp
ks_hasprivatekey = Mit privatem Schl\u00FCssel
left = Links
channel_Emc2VnxEndpointConfiguration = EMC\u00B2 VNX
channel_priority = Priority
doc_select = Ausw\u00E4hlen
domain_modif = Domain bearbeiten
gcnSubscriberIdentification_error = Die Generix Billing-ID darf h\u00F6chstens 64 alphanumerische Zeichen enthalten
error_no_company_specified = Kein Kunde f\u00FCr die Partneredition angegeben: {0}
role = Rolle
infinite = Unendlich
organization_email = _email
archiving = Archivieren
length_min = Mindestl\u00E4nge
doc_update = Aktualisieren
doc_update_information=aktualisiere meine Informationen
menurights = Men\u00DF-Rechte
companyinformation= Meine Firmeninformationen
result = Ergebnisse
occurence = Vorkommnisse
login_validator = Die Anmeldung muss mindestens 3 alphanumerische Zeichen, '_', '@' bzw. '.' enthalten
refuse = Ablehnen
reminder_subject = Thema Erinnerung
STOPPED_icon = fa fa-exclamation-circle
campaigns_new = New Campaign
close = Schlie\u00DFen
linked_document_import_selection = Eine oder mehrere Dateien ausw\u00E4hlen
connect = Verbinden
channel_arkhineo_sectionId_req = Section id
validator_password = Passwort ung\u00FCltig (mindestens 3 Zeichen).
preferences = Einstellungen
report_partners_completion = ABSCHL\u00DCSSE PARTNER
exception_no_backoffice_access = {0} ist keine Teil eines Partners oder Kunden und erh\u00E4lt keinen Zugriff auf das Backoffice
channel_timeout = Zeit\u00FCberschreitung
channel_url = URL
ui_required = Es wird ein Wert ben\u00F6tigt
error_save_user_no_env = Zur Benachrichtigung des Nutzer muss eine Umgebung ausgew\u00E4hlt sein
NONE_color = #d4d4d4
partner_import_error = Fehler beim Importieren der Partner: {0}
organization_fullname = Name
communication=Kommunikation
doc_upload_error = Fehler beim Hochladen des Dokuments: {0} ({1})
information = Information
period_to = bis
keyword_$currentDate = Aktuelles Datum
ui_validator = Der Wert ist ung\u00FCltig
channel_fax_only_one_endpoint_per_organization = You can have only one fax channel
error_removing_partner = Fehler beim Entfernen des Partners ({0})
error_saving_partner = Fehler beim Speichern des Partners ({0})
channel_ssl = SSL
doc_deleted = Dokument gel\u00F6scht
gcn_identification_group = Generix Collaborative Network
good = gut
error_uploding_logo = Fehler beim Laden des Logos {0}
add_below = Unterhalb hinzuf\u00FCgen
channel_ftp_privateKeyFile = Private key file
entry_add_title = Titel hinzuf\u00FCgen
organization_duns = Referenz
export = Exportieren
UNKNOWN_color = #d4d4d4
channel_save_ok = Channel saved.
doc_new_name = Neuer Name
info_host_saved = Domain {0} gespeichert
add = Hinzuf\u00FCgen
exception_id_duplication = Kennung duplizieren: {0}
imenu_integration_recent_business = Letzte Abl\u00E4ufe
ks_friendlyname = Benutzerlesbarer Name
generate_new_password_confirm = M\u00F6chten Sie wirklich ein neues Passwort erzeugen?
menu_messages = Mails
contact_user = Kontakt Nutzer
warn_select_partner_notification = Bitte w\u00E4hlen Sie die zu benachrichtigenden Partner aus
channel_ftp_disconnect = Disconnect
campaign_documents = Documents
encoding = Codierung
users = Benutzer
partner_field = Feld Partner
channel_initial = Initial
channel_subject = Subject
port = Anschluss
channel_mdn_type = Type
lang_english = Englisch
channel_ftp_localWorkDirectory = Local work directory
ks_remove_error = Fehler beim Entfernen der Datei: {0}
mail_sent = Gesendet
info_company_saved = Kunde {0} gespeichert
partner_import_no_user = Partner {0} hat keinen Nutzer
roles = Rollen
channel_ftp_fastExistsCheck = Fast existence check
channel_proxy_url = Proxy URL
choose = Ausw\u00E4hlen
create_notifications = Benachrichtigung erstellen
paste = Einf\u00FCgen
partner_delete_error = Fehler beim L\u00F6schen des Partners: {0}.
remove = Entfernen
channel_HttpEndpointConfiguration = HTTP/S
page_new = Neue Seite
ks_filename = Dateiname
user_blocked = Ihr Nutzerkonto wurde gesperrt. Bitte wenden Sie sich an Ihren Administrator.
organization_freeDate01 = _freeDate01
error_removing_user = Fehler beim Entfernen des Nutzers ({0})
configuration_portal = Portal einrichten
scope = Bereich
channel_ftp_compression = Compression level
entry_title = Titel
end = Ende
sort_order = Sortierreihenfolge
ks_config_require_name = Es wird der benutzerlesbare Name des KeyStore ben\u00F6tigt
organization_freeDate02 = _freeDate02
info_instance_toggled = {0} Umgebung umgestellt
partner_import_already_existing_users = Nutzer ist/sind bereits vorhanden
no_records_found = Keine Datens\u00E4tze gefunden
no_extensions_found = No extensions found for this database
label = Kennzeichnung
keyword_$password = Nutzerpasswort
message = Nachricht
modify = \u00C4ndern
imenu_security = Partner
menu_process_manage = Vorgangsverwaltung
ks_unexpected_error_determing_alias = Unerwarteter Fehler beim Ermitteln des Alias
STOPPING_icon = fa fa-exclamation-circle
generate_password = Passwort erzeugen
info_role_removed = Rolle {0} entfernt
family = Familie
OK_label = OK
channel_ftp_maximumReconnectAttempts = Maximum reconnect attempts
backup = Datensicherung
template_revision = Email-Vorlage importieren
info_user_role_saved = Nutzerrolle gespeichert
partner_contacts = Kontakte
channel = Kanal
add_above = Oberhalb hinzuf\u00FCgen
APPROVED_icon = fa fa-check-circle-o
channel_other = Other
menu_audit = Pr\u00FCfung
organization_phone = _phone
report_not_completed = Offen
order = Bestellung
organization_vat = MWSt
WARN_icon = fa fa-exclamation-circle
period = Zeitraum
trf_generic = allgemeines Format
upload_invalid_file = Ung\u00FCltiges Dateiformat
warn_host_already_used = Diese Domain wird bereits verwendet
channel_ftp_execPbsz = execPbsz
exception_backoffice_user = {0} ist ein Backoffice-Anwender
channel_organization = Organization
WARN_label = WARNUNG
info_user_new_password = Ein neues Passwort wurde per Email gesendet an {0}
keyword_$login = Nutzeranmeldung
channel_debug = Debug
ks_no_file_selected = Bitte w\u00E4hlen Sie eine KeyStore-Datei aus
ABANDONED_color = #cc1e00
exit = Beenden
partner_deleted = Gel\u00F6schter Partner: {0}.
quick_search = Schnellsuche
task_adv_search_avancee = Erweitert
analyze = Analysieren
integration = Ablauf
smtp_enableStartTLS = enableStartTLS
channel_X400EndpointConfiguration = X400
info_partner_file_import = {0} importiert
organization_logoLarge = _logoLarge
other = Sonstige
property_required = erforderlich.
save = Speichern
exception_user_associated = {0} verbundene Nutzer
login = Login
organization_address_country = Land
channel_duplicate_error = Error while copying channel
error_saving_role = Fehler beim Speichern der Rolle ({0})
imenu_repository_documents = Dokumente
campaign_date_start = Started Date
ERROR_color = #d15b47
entry_import_faq = FAQs importieren
boolean_true = Wahr
regenerate_all = Alle neu generieren
ks_entry_serialnumber = Seriennummer
channel_test = Test
reminder = Erinnerung
STARTED_color = #bebfbb
channel_used_keystore = Used keystore
exception_partner_subgroups = Partner {0} hat {1} Untergruppe(n)
permissions_dialog_page_title = Zugriffsrechte f\u00FCr die Seite festlegen
menu_partners = Partner
channel_add_error_duplicatename = A channel already exists with this name. This channel cannot be created.
removed = Entfernt
disable = Deaktivieren
error_saving_instance = Fehler beim Speichern der Umgebung ({0})
entry_add_question = Frage hinzuf\u00FCgen
unread_message = ungelesene Meldung(en)
completion_bounded = Abschluss begrenzt
organization_freeLongText01 = _freeLongText01
doc_downloads = Heruntergeladene Dokumente
organization_freeLongText02 = _freeLongText02
recent_mapping = Letzte Mappings
create_mail = Mail erstellen
report_partners_status_by_month = ENTWICKLUNG PARTNERSTATUS
invalid_generix_billing_id = Ung\u00FCltige Generix Billing-Kennung
menu_admin = Verwaltung
pages = Seiten
sort_order_descending = Absteigend
entry_radio = Radio
regenerate_all_password = Alle Passw\u00F6rter neu generieren
all = Alle
new = Neu
modif_contact_partner = Ansprechpartner f\u00FCr \u00C4nderungen
upload_files = Dateien hochladen
channel_generate_file = Automatic Generation File
collapse_all = Alle reduzieren
partner_delete_error_children_exist = L\u00F6schen von Partner und Unterpartnern fehlgeschlagen: {0}.
report_informations = Angaben
length_max = Maximale L\u00E4nge
exception_constraint_violation = {0} : {1}
channel_coded_mdn = Encrypted MDN
freetext = Freitext
template_saved = Vorlage gespeichert
sort_by = Sortieren nach
ui_converter = Fehler bei der Wertkonvertierung
deny_message = Bitte geben Sie einen Grund f\u00FCr die Ablehnung an
channel_deleted = Channel deleted
channel_encoding = Encoding
error_removing_portlet = Fehler beim Entfernen des Portlet-Inhalts ({0})
creation_mail_message_html = Hallo {0},<br/><br/>nachfolgend finden Sie die Informationen zur Verbindung mit der {1}-Website ({2}): <br/><br/> <li>Login: {3} </li><li> Passwort: {4}</li><br/><br/>Bis bald!
exception_instance_not_found = Nutzer {0} hat keine Umgebung eingerichtet f\u00FCr Domain {2}
ks_error_could_not_write_certificate_file = Dokumentdatei f\u00FCr dieses Zertifikat konnten nicht geschrieben werden
folder_in = Eingabeordner
button_edit = Bearbeiten
entry_answer = Antwort
permission_required = Auswahl erforderlich
select_one = Eine ausw\u00E4hlen
milestone = Geplantes Abschlussdatum
partner_import_file = Partnerdatei importieren (*.xls, *.xlsx)
exception_task_type_not_found = Tasktyp nicht gefunden {0}
account_creation_confirm = Ihr Konto wurde erfolgreich angelegt. Sie erhalten in K\u00FCrze eine Best\u00E4tigungs-Mail an Adresse:
imenu_integration = Ablauf
mark_as_read = Als gelesen kennzeichnen
channel_authentification_proxy = Proxy authentification
ks_publickeytab = Zertifikat \u00F6ffentlicher Schl\u00FCssel
partner_name = Name des Partners
channel_ftp_ftpClientTrustStoreId = Client truststore
STARTED_icon = fa fa-times-circle-o
warn_language_mandatory = Die Einstellung einer Sprache ist zwingend erforderlich
exception_message = Ein Fehler ist aufgetreten. \nBitte versuchen Sie es erneut oder wenden Sie sich an Ihren Administrator\n ({0})
organization_freeText09 = _freeText09
companies = Kunden
creation_mail_subject = Ihr Konto wurde erstellt f\u00FCr Website {0}
delete_select = M\u00F6chten Sie die ausgew\u00E4hlten Elemente wirklich dauerhaft l\u00F6schen?
error_removing_page = Fehler beim Entfernen der Seite ({0})
organization_freeText07 = _freeText07
organization_subscriberId = Generix Billing-ID
identification = Kennung
ks_entry_alias_optional = Optional, sofern das Alias nicht automatisch bestimmt werden kann
organization_freeText08 = _freeText08
organization_freeText05 = _freeText05
readonly = Schreibgesch\u00FCtzt
doc_uploaded = Hochgeladenes Dokument: {0}
organization_freeText06 = _freeText06
organization_freeText03 = _freeText03
organization_freeText04 = _freeText04
organization_freeText01 = _freeText01
organization_freeText02 = _freeText02
partner_view = Partner {1} ({0})
state = Staat
exception_user_not_partner = Nutzer {0} Hauptgruppe {1} ist weder Partner- noch Kundengruppe
FATAL_icon = fa fa-bomb
events = Ereignisse
tva = MWSt
exception_import_portal_not_empty = Portal f\u00FCr die Umgebung nicht leer {0}
hosts = Hosts
UNKNOWN_icon = fa fa-exclamation-circle
bad_account_or_password = Konto oder Passwort ist falsch.
error_getting_portlet_content = Portlet {0}, Inhalt {1}: {2}
channel_hashing_algorithm = Hashing algorithm
monitoring = Aktivit\u00E4t
move_top = Ganz nach oben
labels = Kennzeichnungen
menu_campaigns = Einf\u00FChrung
channel_filename_required = File name mask for automatic file
dictionary = Wirtschaftsw\u00F6rterb\u00FCcher
menu_notifications = Benachrichtigungen
rendered = Ausgef\u00FChrt
recipients = Empf\u00E4nger
boolean_false = Falsch
duns = Referenz
date_last_authentication = Letzte Verbindung
actions = Aktionen
edit_notifications = Benachrichtigung bearbeiten
cut = Ausschneiden
contact_admin = Bitte wenden Sie sich an Ihren Administrator.
other_variable = Sonstige Variable
document = Dokument
upload_file_limit = Dateibegrenzung erreicht
expand_all = Alle erweitern
label_search = Geben Sie hier Ihren Suchbegriff ein
info_user_removed = Nutzer {0} entfernt
lang_french = Franz\u00F6sisch
ERROR_label = FEHLER
menu_security_companies = Kunden
ks_remove_linked_partner_integrity = Bedienung gesperrt: das Zertifikat ist noch mit folgenden Gruppen verkn\u00FCpft: {0}
default = Standard
channel_common_name = Common name
organization_profile = Profil
reminder_number = Erinnerung Nummer
upload_with_conflict_conflict_message = Die folgenden Dateien werden \u00FCberschrieben:
disabled = Deaktiviert
exception_invalid_gson = Ung\u00FCltige Gson-Datei
report_partners_status = PARTNERSTATUS
channel_ftp_soTimeout = SocketOptions.SO_TIMEOUT (ms)
warn_only_parent_empty = Es darf nur die \u00FCbergeordnete Seite als leer definiert werden
organization_freeBoolean01 = _freeBoolean01
creation = Erstellungsdatum
ui_invalid_size = Ung\u00FCltige Dateigr\u00F6\u00DFe
menu_process = Ablauf
organization_freeBoolean02 = _freeBoolean02
validate = Validieren
FAILED_color = #cc1e00
rte = RTE
template_import = Vorlage importieren
languages = Sprachen
ks_modulus_length = Modull\u00E4nge (Bit)
refresh = Aktualisieren
partner_save_error = Fehler beim Speichern des Partners: {0}.
channel_server = Server name
ks_unrecognized = Die hochgeladenen Daten k\u00F6nnen nicht als KeyStore im Format PKCS11, PKCS12 oder JKS geladen werden.\n - \u00DCberpr\u00FCfen Sie Ihr Passwort.\n - \u00DCberpr\u00FCfen Sie Ihre Datei. \n - Versuchen Sie die \"Unlimited Strength Jurisdiction Policy Files\" Ihrer JRE zu \u00E4ndern.
organization_address_postalCode = PLZ
tout = Alle
about_contact_email = Email Ansprechpartner
STARTED_label = BEGONNEN
account_confirmation = Kontobest\u00E4tigung erstellen
channel_create_error = Error creating new channel
warn_no_type_selected = Bitte w\u00E4hlen eine Aufgabenart aus
organization_client = _client
is_expired = ist abgelaufen
ks_upload_select = Keystore (*.jks, *.p12, *.pfx)
partners = Partner
channel_proxy_used = Used a proxy server definition
generate_gcn_subscriber = ID erzeugen
entry_select_excel = Excel-Datei ausw\u00E4hlen (*.xls, *.xlsx)
organization_gcnSubscriber = Generix kunde
campaign_name_short = Short name
channel_delay = delay between attempts
documents = Dokumente
administration = Verwaltung
FAILED_label = FEHLGESCHLAGEN
accounting = Ansprechpartner Kunde
channel_ftp_timeout = Data timeout (ms)
organization_parent = _parent
secure = Sicher (HTTP/S)
ks_entry_notafter = G\u00FCltig bis
ks_upload = KeyStore hochladen
menu_security = Sicherheit
APPROVED_color = #87b87f
view = Anzeigen
domain_edit = Domain Edition
test_upload = Probedatei hochladen
keyword_$campaignvariable = Aktion, freie Variable
imenu_portal_einvoice = e-Rechnung
error_removing_template = Fehler beim Entfernen der Vorlage ({0})
channel_FaxEndpointConfiguration = Fax
version_technical = Technische Referenz
menu_statistics = Berichte
template_invalid_pathname = Der Pfadname des Archivs enth\u00E4lt ung\u00FCltige Zeichen
channel_ftp_privateKeyPassphrase = Private key file passphrase
new_file = Neue Datei
smtp_mailPort = Anschluss
channel_modify = Modify a channel
duplicate = Duplizieren
channel_ftp_knownHostsUri = known_hosts file URI
error_user_without_primary_group = Keine Hauptgruppe zum Duplizieren des Nutzers gefunden
APPROVED_label = OK
user_search = Ergebnisse durch Eingabe des Namens des \u00E4ndernden Nutzers eingrenzen
info_portlet_saved = Portlet-Inhalt gespeichert
channel_ftp_execProt = execProt
imenu_repository_templates = Vorlagen
switch_off = Zur\u00FCck zu Nutzer
add_left = Links hinzuf\u00FCgen
warn_template_with_associated_host = Mit der Vorlage sind einige Domains verkn\u00FCpft
partner_add = Partner hinzuf\u00FCgen
name = Name
page = Seite
installation_detail = Installierte Software
parameters = Parameter
parameter = Parameter
task_types_revision = Visualisierung Aufgabenfestlegung
channel_gcn_only_one_endpoint_per_organization = You can have only one GCN channel
configuration_portlet = Portlet einrichten
channel_ftp_sendNoop = Send noop
import = Importieren
channel_filename = FileName-<NUM>-<DATE>.edi
error_duplicate_template = Vorlage {0} bereits vorhanden
description = Beschreibung
edition = Bearbeitung
campaign_description = Description
rte_file_or_folder_invalid_name = Der Name muss aus alphanumerischen Zeichen ohne Leerstelle bestehen. '.', '_' und '-' werden akzeptiert.
exception_multiple_principal_groups_user = Anwender {0} hat mehr als eine Hauptgruppe ({1})
gcnIdentification_error = Die GCN-ID darf h\u00F6chstens 64 alphanumerische Zeichen enthalten
channel_mime = Mime
complementary_info = Erg\u00E4nzende Angaben
channel_unit4 = Organisation unit4
select_instance = Umgebung ausw\u00E4hlen
channel_unit1 = Organisation unit1
ks_error_could_not_find_nor_create_parent_for_friendly_name = Stammbezeichnung f\u00FCr benutzerlesbaren Namen konnte nicht gefunden oder erstellt werden {0}
channel_unit3 = Organisation unit3
channel_unit2 = Organisation unit2
day_of_week = Wochentag
channel_given_name = Given name
severity = Schweregrad
exception_removed_instance = Umgebung {0} wurde entfernt
hours = Stunden
info_partner_removed = Partner {0} entfernt
channel_directory_name = Directory name
yes = Ja
start = Start
OK_color = #87b87f
ks_cannot_determine_alias = Zu verwendendes Alias kann nicht bestimmt werden. Gefundene Aliase getrennt durch ; : {0}
partner_code_validator = Der Partnercode muss das korrekte Format beachten (drei Gro\u00DFbuchstaben gefolgt von der Partnerkennung)
fullname = Vollst\u00E4ndiger Name
channel_FtpEndpointConfiguration = FTP
channel_encryption_algorithm = Encryption algorithm
confirmation_mail_message_text = Hallo {0},\n\nIhr Konto wurde erfolgreich angelegt. Bitte kopieren Sie die nachfolgende Adresse in die Adresszeile Ihres Browsers, um das Kontol zu best\u00E4tigen: {1}.\n\nCompletion!=======
exception_user_no_associated_company = Nutzer {0} Partnergruppe {1} ist mit keinem Kunden verbunden
ks_library = Bibliothek
usage = Verwendung
ks_cannot_load_keystore = Keystore kann nicht geladen werden. Parameter \u00FCberpr\u00FCfen. Bitte wenden Sie sich an einen Administrator.
organization_orderContact = Kontakt
organization_address_city = Ort
ks_entry_subject = Geliefert an
organization_freeViewProfile = Profil
required = Erforderlich
active_partners = AKTIVSTE PARTNER
docs_selected = Ausgew\u00E4hlte Dokumente
logout = Abmelden
doc_name = Name des Dokuments
info_partner_saved_detail = Partner {0} gespeichert : {1}
keystore = KeyStore
switch_user = Nutzer wechseln
channel_asynchronous = Asynchronous
fax = Fax
ks_no_slot_index_selected = Bitte w\u00E4hlen Sie einen Schlitzindex aus
dashboard = Dashboard
entry_new = Neuer Eintrag
error_switching_user = Fehler beim Wechseln des Nutzers ({0})
warn_app_template_deleting=Die {0} kann nicht gel\u00F6scht werden.
channel_ftp_siteCommand = Site commands
role_min = Rolle
linked_certificates = Verkn\u00FCpfte Zertifikate
organization_name = Code
menu_security_order_issuers = Besteller
exception_file_upload_unknown_request = Unbekannter Anfrageparameter: {0}
phone = Telefon
menu_system = System
reset = Zur\u00FCcksetzen
style = Format
channel_add = Add a channel
channel_compressed_message = Compressed message
channel_WSStockEndpoint_stock_password = Passwort
ks_provide_friendly_name = Bitte stellen Sie einen benutzerlesbaren Namen ein
channel_arkhineo_depositCert_req = Deposit certificate
roadmap = Roadmap
contact_us = KONTAKT
channel_AliasEndpointConfiguration = Alias
confirm_file_delete = M\u00F6chten Sie das folgende Element wirklich entfernen: {0}
exception_portlet_cloning = Problem beim Klonieren des Portlets {0}
name_executable = Name der ausf\u00FChrbaren Datei
enabled = Aktiviert
channel_ftp_username = User name
exception_duplicate_role = Rollencode duplizieren: {0}
enabled_linked_document_import = Import verlinkter Dokumente aktivieren
theme = Thema
channel_ftp_serverAliveCountMax = SFTP serverAliveCountMax
error_saving_permission = Fehler beim Speichern der Berechtigung ({0})
start_date = Startdatum
channel_name = Channel name
editor = Bearbeiter
report_partners_completion_campaigns = ABSCHL\u00DCSSE PARTNER
ks_alias_not_found = Alias nicht gefunden: {0}. Gefundene Aliase getrennt durch ; : {1}
indexing = Indizierung
menu_instances = Umgebungen
display = Anzeigen
export_portal = Exporteur-Portal
warn_portlet_content_not_found = Portlet-Inhalt mit Kennung {0} nicht gefunden
entry_answers = Antworten
exception_partner_associated = {0} verbundene Partner
channel_as2to = AS2 to
info_instance_saved = {0} Umgebung gespeichert
organization_address_address = _address.address
channel_alias_target = Target
exception_unavailable_instance = Umgebung {0} ist vor\u00FCbergehend nicht verf\u00FCgbar
identification_validator = Die Kennung darf nur alphanumerische sowie das Zeichen '_' enthalten
task_to_complete = Zu erledigende Aufgabe
partner_saved = Gespeicherter Partner: {0}.
chat = Chat
exception_import_error_during_cloning = Fehler im Portalklon
instance_code_validator = Im Code ist die Regel der drei Ziffern oder der Gro\u00DFbuchstaben zu beachten
AllUsers = F\u00FCr die Benutzer aller Partner
channel_number_of_retry = Number of retry
error_creating_template_archive = Fehler beim Anlegen des Vorlagenarchivs ({0})
statistics = Berichte
ABANDONED_icon = fa fa-exclamation-circle
keyword_$contact = Kontakt
replace = Ersetzen
channel_AS2EndpointConfiguration = AS2
partner_send_mail = Kontoangaben senden
allow_user_managing_tab = Erlaubt Nutzern, diesen Reiter zu verwalten
channel_others = Others
notification = Benachrichtigung
template_edit = Vorlage bearbeiten
organization_orderPhone = Telefon
day = Tag
from_address = Adresse von
ks_uploaddate = Erstellungsdatum
channel_proxy_name = Proxy name
FATAL_color = #cc1e00
ks_remove_timestamp_server_integrity = Bedienung gespert: das Zertifikat wird noch vom Zeitstempelserver referenziert
ks_slotIndex = Schlitzindex
ks_label = Label
tasks_noselection = Keine Aufgabe ausgew\u00E4hlt
minutes = Minuten
error_saving_host = Fehler beim Speichern der Domain ({0})
format = Format
recent_business = Letzte Gesch\u00E4ftsvorf\u00E4lle
archive = Archiv
menu_domain = Domain
history = Historie
upload_invalid_size = Ung\u00FCltige Dateigr\u00F6\u00DFe
channel_ftp_serverAliveInterval = SFTP serverAliveInterval
home_message = Mit dem GCI Community Management beschleunigen Sie Ihre EDI/B2B-Einf\u00FChrungen und verringern gleichzeitig die Kosten
partner_new = Neuer Partner
reminder_content = Inhalt Erinnerung
FATAL_label = SCHWERWIEGEND
error_profile_name_mandatory = Die Profilbezeichnung ist ein Pflichtfeld
deploy_process = Ablauf ausl\u00F6sen
deployment=Einsatz
imenu_repository = Ablage
about_contact = Kontakt
ui_file_limit = Maximal zul\u00E4ssige Anzahl Dateien \u00FCberschritten
imenu_general = Allgemein
insert = Einf\u00FCgen
numeric = Numerisch
info_no_portlet_defined = Das Portlet wurde nicht auf diese Seite eingestellt
error_exporting_instance = Export fehlgeschlagen: {0}
campaign_name = Name
general = Allgemein
channel_ftp_separator = Path separator
channel_SmtpEndpointConfiguration = SMTP
test_send_mail = Automatischer Email-Versand
move_up = Nach oben
channel_report_timer = Delivery report timer
entry_new_answer = Option
channel_type_select = Channel type...
authentication = Authentifizierung
keyword_$company = Kunde
clear = L\u00F6schen
organization_freeViewConfiguration = _freeViewConfiguration
info_user_saved = Nutzer {0} gespeichert
channel_arkhineo_consultCert_req = Consult certificate
organization_userGroupAssociations = _userGroupAssociations
warn_creation_succes = Erstellung erfolgreich
upload_with_conflict_title = Archiv importieren
organization_logoSmall = _logoSmall
organization_orderFax = Fax
error_saving_organization = Fehler, das Element ist bereits vorhanden
move_down = Nach unten
user = Benutzer
account = Mein Konto
gcnSubscriberIdentification = Generix Billing-ID
channel_MDN = MDN
WARNING_label = WARNUNG
ks_password_pkcs11 = Passwort (PIN)
siret = Registrierung
importInstance = Import (*.json)
campaigns = Onboarding
organization_creation = _creation
STOPPED_color = #cc1e00
warn_partner_profile_deleted = Das Partnerprofil sollte nicht gel\u00F6scht werden
entry_add = Hinzuf\u00FCgen
profile = Profil
exception_gcn_subscriber_id_duplication = Generix Billing-Kennung duplizieren: {0}
reminder_delay = Erinnerung Verz\u00F6gerung
role_edit = Rolle Bearbeiten
DISABLED_label = DEAKTIVIERT
FAILED_icon = fa fa-exclamation-circle
menu_security_users = Backoffice-Nutzer
confirm_instance_enable = Alle Dienste dieser Umgebung werden aktiviert (Verarbeitung, Einarbeitung...).\\n\\nM\u00F6chten Sie diese Umgebung wirklich aktivieren?
tasks_campaign = Workflow-Aufgaben
ks_error_no_keystore_folder = Fehler - KeyStore-Ordner konnte nicht abgefragt werden
domain = Domain
channel_toggle_linked_chanel = Error while modifying channel. Existing alias to this channel:<br/>{0}
entry_type = Art
max_size_of_linked_document = Max. Gr\u00F6\u00DFe verkn\u00FCpfter Dateien
schema = Schema
strong = stark
organization_logoMedium = _logoMedium
managed_by = Verwaltet von
menu_security_groups = Gruppen
channel_message_body = Message body
preview_not_available = Vorschau nicht verf\u00FCgbar
ERROR_icon = fa fa-times-circle-o
organization_code = Kennung
search = Suchen
channel_ftp_isImplicit = isImplicit
menu_notification = Benachrichtigung
partner_parent = \u00DCbergeordnet
PENDING_color = #bebfbb
error_saving_file = Fehler beim Speichern der Datei ({0})
channel_ftp_disableSecureDataChannelDefaults = disable secure data channel defaults
completion = Abschluss
mail_content = Inhalt
keyword_$campaigncontactphone = Ansprechpartner Aktion, Telefon
warn_existing_resource = Diese Ressource ist bereits vorhanden
ks_uploaded = Hochgeladener KeyStore: {0}
confirm_task_result_reset = Die Ergebnisse dieser Task werden gel\u00F6scht. Forfahren?
company_add = Kunden hinzuf\u00FCgen
channel_ftp_receiveBufferSize = Downloading buffer size
import_cert_jks_pkcs12 = JKS/PKCS#12...
partner = Partner
confirmation_mail_message_html = Hallo {0},<br/><br/>Ihr Konto wurde erfolgreich angelegt. Bitte klicken Sie auf den folgenden Link, um Ihre Email-Adresse zu best\u00E4tigen: <a href=\"{1}\">Email-Best\u00E4tigung</a>.<br/></br>Alternativ k\u00F6nnen Sie die nachfolgende Adresse auch in die Adresszeile Ihres Browsers kopieren: {1}.<br/><br/>Bis bald!
channel_ftp_privateKeyUri = Private key file URI
error_removing_role = Fehler beim Entfernen der Rolle ({0})
menu_config = Konfiguration
ks_entry_signaturealgoname = Bezeichnung Signaturalgorithmus
channel_relation = Relationship
partner_import_already_existing_partners = Partner ist/sind bereits vorhanden
info_logo_uploaded = Logo geladen
language = Sprache
organization_comment = Beschreibung
styleClass = Formatklassen
OK_icon = fa fa-check-circle-o
error_uploding_template = Fehler beim Laden der Vorlage ({0})
channel_ftp_connectTimeout = Connect timeout (ms)
ks_entry_fingerprintsha1 = Fingerabdruck (SHA-1)
contact = Kontakt
add_right = Rechts hinzuf\u00FCgen
ks_hasRSApublickey = RSA
task_adv_search = Erweiterte Suche
WARNING_icon = fa fa-exclamation-circle
imenu_campaigns = Einf\u00FChrung
exception_import_instance_code_null = Der Umgebungscode ist null
decimal_separator = Dezimaltrennzeichen
warn_locked_folder = Gesperrter Ordner
docs_available = Verf\u00FCgbare Dokumente
entry_delete_question = Frage l\u00F6schen
error_sending_new_password = Fehler beim Versand des neues Passworts an {0} ({1})
generate_new_password = Neues Passwort erzeugen
authentication_mail_template = Mailvorlage
count = Anzahl
error_saving_host_not_unique = Domain mit diesem Namen bereits vorhanden
undefine = Definition zur\u00FCcksetzen
channel_signed_mdn = Signed MDN
error_no_user_found = Kein Nutzer gefunden mit Kennung: {0}
month = Monat
channel_as2_only_one_endpoint_per_organization = You can have only one AS2 channel
channel_ftp_keyPair = Java KeyPair
organization_id = _id

#Portlets Names
Calendar=CalendarDeprecated
CarrefourInvoiceEdition=CarrefourInvoiceDeprecated
CarrefourInvoice=CarrefourInvoice
DocumentBarChart=BarChart
DocumentBirt=Birt
DocumentCalendar=DocumentCalendarDeprecated
DocumentCounter=Counter
DocumentLineChart=LineChart
Documentation=DocumentationDeprecated
DocumentPieChart=PieChart
archiveimv3 = IMV3-Archiv
EDocuments=EdocumentsDeprecated
Factor=FactorDeprecated
FeedReader=FeedReaderDeprecated
Monitoring=MonitoringDeprecated
OrderLine=OrderLineDeprecated
Penalty=FranprixPenaltyDeprecated
PlanningSchedule=PlanningScheduleDeprecated
SafranInvoice=SafranInvoiceDeprecated
Survey=SurveyDeprecated
channel_indicator = Test indicator
sources = Quellen
back = Zur\u00FCck
title = Titel
content = Inhalt
timeout = Zeit\u00FCberschreitung
channel_ftp_knownHostsFile = known_hosts file
duration = Dauer
channel_admin_domain = Administration management domain
doc_upload_disabled = Sie k\u00F6nnen diese Aktion nicht ausf\u00FChren, weil die Task gesperrt ist (m\u00F6glicherweise wird gerade eine Kampagne durchgef\u00FChrt)
entry_question = Frage
smtp_ssl = SSL
alpha = Buchstaben
client = Kunde
company = Kunde
info_portal_saved = Portal gespeichert
new_message = Neue Nachricht
WARNING_color = #ffb752
clear_portlet_confirm = M\u00F6chten Sie den Inhalt dieses Portlets wirklich l\u00F6schen?
ks_entry_alias = Alias
length = L\u00E4nge
validator_email = Ung\u00FCltige Email.
home = Home
channel_ftp_securityProtocol = Security protocol
schedule = Zeitplan
channel_not_selected = No channel selected
print = Drucken
warn_delete_fail = Unterdr\u00FCcken fehlgeschlagen
ks_morethanoneentry = Ihr KeyStore enth\u00E4lt mehr als einen Eintrag. Dieser KeyStore kann nicht geladen werden.
prompt = Passwort eingeben
imenu_portal_o2c = Barzahlungsauftrag
select = Ausw\u00E4hlen
exception_instance_associated = {0} verbundene Umgebungen
ks_error_no_friendly_name_found = Benutzerlesbaren Name in den Parametern nicht gefunden. Name kann nicht ermittelt werden
error_removing_portlet_content = Fehler beim L\u00F6schen des Portlet-Inhalts ({0})
partner_imported = Importierter Partner: {0}.
shipment_date = Versanddatum
channel_ftp_ciphers = Ciphers
channel_dda3 = DDA3
channel_dda4 = DDA4
ks_entrydetailstab = Zertifikat
add_child_page = Unterseite hinzuf\u00FCgen
channel_dda1 = DDA1
channel_dda2 = DDA2
channel_delete_linked_chanel = Error while deleting channel. Existing alias to this channel:<br/>{0}
organization_fax = _fax
ks_password = Passwort
channel_ftp_charset = File encoding
PENDING_label = BERICHT WIRD ERSTELLT...
channel_port = Port
value_default = Standardwert
action = Aktion
info_file_saved = Datei gespeichert
text = Text
portal = Portal
DISABLED_icon = fa fa-minus-circle
tasks = Aufgaben
channel_ftp_stepWise = Stepwise traversing directories
ks_config_require_instance = Es wird eine KeyStore-Instanz ben\u00F6tigt
io = IO
partner_user_add = Kontakt hinzuf\u00FCgen
templates = Vorlagen
permission_edit = Rechte bearbeiten f\u00FCr
exception_code_duplication = Code duplizieren: {0}
DISABLED_color = #d4d4d4
messaging = Messaging
STOPPING_label = H\u00C4LT AN
field = Feld
info_company_removed = Kunde {0} entfernt
campaign_date_creation = Creation Date
messages = Nachrichten
doc_cancel = Abbrechen
begin = Beginnen
ks_upload_error = Fehler beim Hochladen des KeyStores: {0}
status = Status
template = Vorlage
organization_orderEmail = Email
channel_options = Options
alphanumeric_underscore = Nur alphanumerische Zeichen und Unterstrich erlaubt
error_importing_instance = Fehler w\u00E4hrend des Imports: {0}
channel_uaid = Uaid
organization_web = _web
channel_terminal_id = Terminal id
notify = Benachrichtigen
ks_unexpected_multiple_keys = KeyStore enth\u00E4lt unerwartet viele Schl\u00FCssel
file = Datei
library = Bibliothek
folder_out = Ausgabeordner
portlet = Portlet
warn_creation_fail = Erstellung fehlgeschlagen
exception_role_has_users = Rolle {0} hat {1} verbundene Nutzer
channel_authentification_http = HTTP authentification
linked_document_import = Anhang importieren
organization_children = _children
url = URL
keyword_$campaigncontactname = Ansprechpartner Aktion, Name
warn_instance_code_already_used = Dieser Code wird bereits verwendet
channel_alernative_relation = Alternative relationship
organization_registration = Registrierung
role_add = hinzuf\u00FCgen
channel_private_domain = Private management domain
channel_encoding_message = Encoding message
doc_remove_error = Fehler beim Entfernen des Dokuments
doc_uploaded_success = Hochgeladenes Dokument: {0}/{1}
exception_import_export_null = Der Export betr\u00E4gt null
is_locked = ist gesperrt
exception_user_more_companies = Nutzer {0} Partnergruppe {1} hat mehr als einen verbundenen Kunden ({2})
instance = Umgebung
template_new = Vorlage erstellen
doc_rename_error = Das Dokument existiert bereits
menu_monitoring = Aktivit\u00E4t
subject = Thema
NONE_icon = fa fa-minus-circle
day_of_month = Monatstag
error_saving_user = Fehler beim Speichern des Nutzers ({0})
ks_use_start_date = Nutzungsbeginn
channel_ftp_useList = Use FTP LIST
channel_retry = Retry
download = Herunterladen
ks_provide_library = Bitte stellen Sie eine Bibliothek ein
clear_page_confirm = M\u00F6chten Sie den Inhalt dieser Seite wirklich l\u00F6schen?
find = Suchen
info_role_saved = Rolle {0} gespeichert
line_comment = Zeilenkommentar
host = Host
menu_search = Suchen
boolean_select = --Ausw\u00E4hlen--
exception_import_instance_null = Umgebung in der Exportdatei ist null
workflow = Ablauf
error_changing_layout = Fehler beim \u00C4ndern des Seitenlayouts ({0})
warn_profile_already_exists = Das Profil ist bereits vorhanden
channel_ftp_ftp_variant = FTP mode
task = Aufgabe
channel_WSStockEndpoint_stock_retrieval = Endpoint supply data retrieval
channel_fax = Fax number
company_edit = Kunden bearbeiten {1} ({0})
channel_ftp_hostname = Server hostname
emitter = Sender
info_no_portlet_content_defined = Das Portlet wurde auf diese Seite ohne Inhalt eingestellt
error_select_correct_user = Bitte einen korrekten Nutzer ausw\u00E4hlen
no = Nein
code = Code
ui_invalid_file = Ung\u00FCltiges Dateiformat
user_edit = Nutzer bearbeiten
organization_modification = _modification
delete = L\u00F6schen
move_bottom = Ganz nach unten
channel_ftp_passiveMode = Passive mode
hour = Stunde
ks_use_end_date = Nutzungsende
error_save_user_no_partner = Partnerauswahl erforderlich
ok = OK
channel_ftp_jschLoggingLevel = JSCH log level
ks_entry_fingerprintmd5 = Fingerabdruck (MD5)
account_creation = Kontoerstellung
ks_keystoredetailstab = Angaben KeyStore
imenu_repository_dictionary = Wirtschaftsw\u00F6rterbuch
exception_unknown_layout_type = Unbekannter Layouttyp
new_folder = Neuer Ordner
show_triggers = Trigger anzeigen
rigth = Rechts
ks_usage_mismatch = Die Verwendung {0} muss passen zur \u00FCbergeordneten Verwendung {1}
error_saving_portal = Fehler beim Speichern des Portals ({0})
info_partner_saved = Partner {0} gespeichert
channel_signed_message = Signed message
writer = Ersteller
entry_text = Text
report_no_data = KEINE DATEN
about_short = \u00DCber
channel_ArkhineoEndpointConfiguration = Arkhineo
about = \u00DCber TradeXpress Evolution
information_system = Informationssystem
edit_mail = Mail bearbeiten
error_removing_row = Fehler beim Entfernen der Reihe ({0})
exception_failing_ACE_instanciation = ACE instantiieren aus Typ fehlgeschlagen: {0}
number_reviewed_messages = Anzahl eingegangener Nachrichten
channel_ftp_streamDownload = Stream download
deny = Ablehnen
edit = Bearbeiten
edit_record = Bearbeiten
entry_checkbox = Kontrollk\u00E4stchen
channel_generation_qualifier = Generation qualifier
error_editing_default_host = Der Standardhost ist nicht bearbeitbar
execute = Ausf\u00FChren
accept = \u00DCbernehmen
exception_more_than_one_instance = Der dem Kunden {1} zugeordnete Anwender {0} hat mehr als eine Umgebung eingerichtet f\u00FCr Domain {2} ({3})
info_host_removed = Domain {0} entfernt
channel_ftp_ftpClientKeyStoreId = Client keystore
WARN_color = #ffb752
STOPPING_color = #cc1e00
exception_gcn_id_duplication = GCN-Kennung duplizieren: {0}
end_date = Enddatum
channel_ftp_flatten = Flatten file path
task_edit = Aufgabe bearbeiten
contact_mode = Medien
view_all = Alle Anzeigen
menu_security_partners = Partner
import_cert_pkcs11 = PKCS#11...
property_not_integer = muss ganzzahlig sein.
ks_type = Art
ks_use_period = Nutzungszeitraum
email = Email
creation_mail_message_text = Hallo {0},\n\nnachfolgend finden Sie die Informationen zur Verbindung mit der {1}-Website ({2}): \n\n- Login: {3}\n- Passwort: {4}\n\nBis bald!
variables = Variablen
warn_select_role = Bitte w\u00E4hlen Sie eine Rolle aus
channel_coded_message = Encrypted message
doc_upload_duplicate = Duplizieren: {0}
started = Gestartet
channel_ftp_chmod = Set chmod
number_connexion = Anzahl Verbindungen
trf_required_template_uri = Es wird die URI der Belegumwandlungsvorlage ben\u00F6tigt
confirm = Best\u00E4tigen
trf_required_document_type = Eine Konfiguration zur Belegumwandlung erfordert die Belegart
gcn_subscriber_active = Generix kunde
keyword_$url = Anwendungs-URL
ks_entry_version = Version
contacts = Kontakte
country = Land
partner_edit = Partner bearbeiten {1} ({0})
organization_freeDouble02 = _freeDouble02
organization_freeDouble01 = _freeDouble01
channel_type_mandatory = You have to choose a channel type before clicking on add button.
doc_edit = Dokument bearbeiten
channel_receipt_report = Receipt report
exception_duplicate_login = Login duplizieren
UNKNOWN_label = UNBEKANNT
imenu_integration_recent_mapping = Letzte Mappings
channel_ftp_reconnectDelay = Reconnection delay (ms)
details = Details
channel_content_ident = Content identification
confirmation_mail_subject = Best\u00E4tigung Kontoerstellung.
empty_page = Leere Seite
channel_arkhineo_clientId_req = Client id
error_file_not_found = Datei nicht gefunden {0}
query = Filter
user_logged = Bereits angemeldet
exception_user_not_specified = Nutzer nicht angegeben
no_data = Keine Daten
modification = \u00C4nderungsdatum
date_read = Lesedatum
channels = Kan\u00E4le
entry_remove = Entfernen
partner_field_configuration = Feld Partner einrichten
campaign = Aktion
error_exporting_partner = Fehler beim Exportieren der Partner ({0})
preview = Vorschau
partner_address = Adresse
domain_create = Domain erstellen
keystores = KeyStores
sooner_shipment_date = Fr\u00FChestm\u00F6gliches Datum
channel_ftp_strictHostKeyChecking = Strict host key checking
managed_by_order_issuer = Verwaltet von OI
error_editing_portlet = Fehler beim Bearbeiten des Portlets ({0})
purge = L\u00F6schen
task_create = Aufgabe erstellen
imenu_portal_p2p = Einkauf zur Zahlung
organization_collaborativeId = GCN-ID (gid)
warn_locked_file = Gesperrte Datei
gcnIdentification = GCN-ID (gid)
company_code_validator = Im Code ist die Regel der 'drei Gro\u00DFbuchstaben' zu beachten
keyword_$campaigncontactemail = Ansprechpartner Aktion, Email
layout = Layout
confirm_instance_disable = Alle Dienste dieser Umgebung werden deaktiviert (Verarbeitung, Einarbeitung...).\\n\\nM\u00F6chten Sie diese Umgebung wirklich deaktivieren?
exception_json_export_error = Fehler beim JSon-Export: {0}
channel_GcnEndpointConfiguration = GCN
comment = Kommentar
channel_add_type = Adding channel of type
exception_task_change_parent = Fehler im changeParent-Algorithmus
exception_access_denied = Zugriff verweigert
COMPLETED_icon = fa fa-check-circle-o
none = Kein
doc_upload = Hochladen
repository = Ablage
type = Art
user_new = Nutzer erstellen
seconds = Sekunden
client_order = Kundenauftrag
enable = Aktivieren
permissions = Rechte
remindpassword = An Passwort erinnern
imenu_general_instance = Umgebung
sort_order_ascending = Aufsteigend
channel_ftp_ignoreFileNotFoundOrPermissionError = Ignore FileNotFound or Permission Error
STOPPED_label = ANGEHALTEN
channel_save_error_null = Error while saving channel (channel to be saved == null).
partner_comment = Kommentar
later_shipment_date = Am letzten Versanddatum
imenu_portal = Collaborative
exception_task_import_parents = Problem bei Import-tasksParent f\u00FCr Name {0}
error_removing_host = Fehler beim Entfernen der Domain ({0})
ks_config_require_usage = Es wird die KeyStore-Verwendung ben\u00F6tigt
exception_task_import = Problem bei Import-Tasks f\u00FCr Name {0}
ks_entry_issuer = Herausgeber
error_removing_company = Fehler beim Entfernen des Kunden ({0})
ks_certificationpath = Vertrauenskette
NONE_label = KEIN
ks_error_multiple_parent_for_friendly_name = Undefinierter Zustand: f\u00FCr den benutzerlesbaren Namen wurden mehrere Stammbezeichnungen gefunden {0}
info_file_removed = Datei entfernt
permissions_dialog_campaign_title = Partner der Aktion hinzuf\u00FCgen
error_importing_partner = Fehler beim Importieren der Partner ({0})
imenu_report = Aktivit\u00E4t
weak = schwach
channel_WSStockEndpointConfiguration = WS/Supply
channel_delivery_report = Delivery report
channel_ftp_preferredAuthentications = Preferred authentications
invalid_file_size = Ung\u00FCltige Dateigr\u00F6\u00DFe
copy = Kopierer
CAMPAIGN = EINARBEITUNG
channel_desc = Description
warn_host_not_removed = {0} wurde NICHT entfernt ({1} zugeh\u00F6rige Umgebung(en):CODE= {2})
filter = Filter
channel_synchronous = Synchronous
help = Hilfe
task_types = Aufgabenarten
description_short = Kurzbeschreibung
menu_dashboard = Dashboard
organization_description = _description
rename = Umbenennen
iframe_error_message = Anzeige des Dokuments mit diesen X-Frame-Einstellungen nicht m\u00F6glich
error_removing_file = Fehler beim Entfernen der Datei ({0})
doc_uploaded_update = aktualisiertes Dokument
date = Datum
exception_admin_url_portal = Um Zugriff auf die Portal-URL zu erhalten, geben Sie bitten einen anderen Nutzernamen an oder wenden Sie sich an Ihren Administrator
warn_user_missing = Nutzer fehlt (siehe Parameter UID)
entry_edit_question = Frage bearbeiten
error_saving_portal_portlet_missing = Portlet-Konfiguration fehlt
partner_identification = Kennung
upload_file = Datei hochladen
channel_ftp_port = Port
menu_process_reports = Vorgangsberichte
page_noselection = Keine Seite ausgew\u00E4hlt
create = Erstellen
organization_address_streetName = Adresse
campaign_general = General
user_company = Anwender des Kunden
STARTING_label = ANFANG
task_last_completed = Letzte abgeschlossene Aufgabe
channel_duplicate_ok = Channel duplicated
move_rigth = Nach rechts
channel_WSStockEndpoint_stock_login = Login
channel_surname = Surname
add_certificate = Zertifikat hinzuf\u00FCgen
document_children_policy = Grunds\u00E4tze zu Dokumenten f\u00FCr Kinder
report_completed = Abgeschlossen
STARTING_color = #bebfbb
send = Senden
exception_role_has_pages = Rolle {0} hat {1} verbundene Seite(n)
COMPLETED_color = #87b87f
week = Woche
configuration = Konfiguration
instances = Umgebungen
exception_sending_mail_partner = Fehler beim Mailversand an Partner: {0}
site_optimized_for_ie9 = Die Website wurde f\u00FCr Internet Explorer 9 optimiert.
error = Fehler
smtp_mailHost = Host
delete_select_single = M\u00F6chten Sie das ausgew\u00E4hlte Element wirklich dauerhaft l\u00F6schen?
security = Sicherheit
STARTING_icon = fa fa-times-circle-o
channel_type = Type
ks_entry_notbefore = G\u00FCltig ab
campaign_date_end = Stopped Date
value = Wert
COMPLETED_label = ABGESCHLOSSEN
PENDING_icon = fa fa-cog fa-spin
info_company_created = Kunde {0} angelegt
import_portal = Import-Portal
backoffice = Back Office
channel_ftp_account = Account
invoice = Rechnungsnummer
ks_no_key_found = Der KeyStore enth\u00E4lt keinen Schl\u00FCssel
Cascade=Purge child documents
Detach=Untergeordnete Dokumente abtrennen
Ignore=Ignore parent documents
doc_purge_children_policy=Linked documents
extensions=Extensions
file_name=Dateiname :
last_modification=Last modification
extension_label=Recording n\u00B0
export_extension_file=Export file
export_extension_all_files=Export files
extension_name=Extension Name
displayed_extension_name=Displayed extension name
display_extension=Display extension

portlet_uploadpdfclient=UploadPdfClient
process_msg = Behandle die Nachricht
organization_registerName = Firmenname
organization_shareCapital = Legales Kapital
processing = Status
organization_legalStructure = Rechtsstruktur
unprocesses_message = unverarbeitete Nachricht(en)

ACCEPTED_WITH_AMENDMENT = Best\u00E4tigt mit \u00C4nderungen
INVOICED = Fakturiert
NONE = Ungelesen
SENT_PARTIALLY = Teilweise versendet
ACQUITTED = Beglichen
SENT = Versendet

READ = Gelesen
CANCEL = Storniert
REFUSED = Abgelehnt
PENDING = Entwurf

DOC=KPI
UPDATED = Aktualisiert
self_register_login = Registrieren
ACCEPTED = Best\u00E4tigt
DUPLICATE = Dupliziert
RESOLVED = Gel\u00F6st
organization_address_country_displayCountry = Land
UNKNOWN = Unbekannt
to_validate = Best\u00E4tigen
TO_CORRECT = Zu korrigieren
IN_DISPUTE = Strittig
CLOSED = Geschlossen
APPROVED = Genehmigt
PARTIALLY_SHIPPED = Teilweise gesendet
ANSWERED = Beantwortet
BLOCKED = Geblockt
TIMEOUT = Abgelaufen
java_lang_String = Text
REFUSED_STYLE_STAGE = Warnung
Unknown = UNBEKANNT!
bql_filter_title = Filter
WARNING = Warnung
TO_VALIDATE = Validierung ausstehend
SYNTAX_ERR = Syntaxfehler
DELIVERED = Geliefert
CORRECT = Elektronisch
organization_address_addressLine = Adresse
TO_REMOVE = Zu l\u00F6schen
UNDEFINED = Nicht elektronisch
REFUSED_MANUALLY = Manuell abgelehnt
OK = OK
ERROR = Fehler
partner_address_1 = Adresse 1
partner_address_2 = Adresse 2
INTEGRATED = Integriert
customer_partner_address_city = Ort
partner_city = Ort
clients_type_CUSTOMER = Kunden
partner_country = Land
SHIPPED = Gesendet
FORCED = Erzwungen
customer_partner_name = Name
UNKNOWN_STYLE_STAGE = Warnung
FATAL = Systemfehler
REMOVED = Gel\u00F6scht
ARCHIVED = Archiviert
user_lastname = Nachname
customer_partner_create = ${client} hinzuf\u00FCgen
menu_user_information = Mein Konto
menu_company_information = Meine Unternehmensinformationen
user_creation_date = Erstellungsdatum
customer_clientUsers = Meine Benutzer
customer_partner_edit = ${client}
customer_partner_export = ${client}datei exportieren
customer_partner_edit_button = ${client}-Update
customer_partner_add_user = Nutzer hinzuf\u00FCgen
menu_user_parameters = Meine Parameter
user_number_connexion = Verbindung (en)
customer_partner_delete_yes = Ja, ich l\u00F6sche es
user_phone = Telefon
customer_partner_import_button = Datei importieren
customer_partner_add_user_dialog_header = F\u00FCgen Sie dem ${client} einen Benutzer hinzu
user_login_validator = Die Benutzer-ID muss eine E-Mail-Adresse sein.
menu_company_information_client = Meine ${client}informationen
user_firstname_placeholder = Ihr Vorname
customer_partner_import_header = Importieren Sie eine ${client}datei (*.xls, *.xlsx)
customer_partner_create_button = ${client} hinzuf\u00FCgen
partner_user_search_placeholder = Suchen Sie den Benutzer nach Nr, ${client} -Name, Benutzer, Vorname oder Nachname
partner_company = Kunde
customer_partner_userNumber = Anzahl der Nutzer
clientPartners = Meine ${clients}
customer_clientPartners = Meine ${clients}
customer_partner_delete_no = Nein, ich werde dar\u00FCber nachdenken
partner_postal_code = Postleitzahl
customer_partner_number = ${client} nr
profile_title = Mein Konto
add_user_mail_button = Nutzer hinzuf\u00FCgen + Email Benachrichtigung
customer_partner_import = Importieren Sie eine ${client}datei
user_mobile = Mobiltelefon
user_login = Nutzer
customer_partner_name_for_user = ${client} Name
partner_user_create = Nutzer hinzuf\u00FCgen
customer_partner_delete = ${client} entfernen
user_last_authentication = Letzte Verbindung
user_firstname = Vorname
add_user_button = Nutzer hinzuf\u00FCgen
user_lastname_placeholder = Ihr Nachname
customer_partner_search_placeholder = Suchen Sie nach einem ${client} nach Nummer, Name oder Stadt
menu_security_user = Meine Sicherheit
partner_client_name = ${client} Name
company_number = ${client} nr
date_to = um
return_to = Zur\u00FCckkommen zu
introduction_placeholder = Einf\u00FChrungstext Bsp.:\n  Die verf\u00FCgbare Dokumentation ist kategorisiert. Klicken Sie einfach auf eine Kategorie, um die Dokumente zu entdecken, die wir Ihnen zur Verf\u00FCgung stellen. Wenn ein Dokument fehlt, k\u00F6nnen Sie es \u00FCber die Kontaktseite melden.
invalid_file_accent_char =   Die verf\u00FCgbare Dokumentation ist kategorisiert. Klicken Sie einfach auf eine Kategorie, um die Dokumente zu entdecken, die wir Ihnen zur Verf\u00FCgung stellen. Wenn ein Dokument fehlt, k\u00F6nnen Sie es \u00FCber die Kontaktseite melden.
library_document_introduction = Die verf\u00FCgbare Dokumentation ist kategorisiert. Klicken Sie einfach auf eine Kategorie, um die Dokumente zu entdecken, die wir Ihnen zur Verf\u00FCgung stellen.
category = Kategorie
update_user_parameters = Aktualisiere meine Parameter
partner_user_send_new_pass_header = Passwort f\u00FCr Benutzer zur\u00FCcksetzen
confirmOverrideFilter = Dieser Filter existiert bereits. M\u00F6chten Sie es ersetzen?
user_message_delete = Der Benutzer wird dauerhaft gel\u00F6scht.
partner_user_roles_save = Benutzerrolle \u00E4ndern
confirmationTitle = Best\u00E4tigung
partner_user_delete = Benutzer l\u00F6schen
partner_user_roles = Benutzer-Rolle
partner_user_send_new_pass = Passwort zur\u00FCcksetzen
comment_upload_file = Bitte f\u00FCgen Sie Ihrem Anhang einen Kommentar-Link hinzu
partner_user_add_role = Weisen Sie eine Rolle zu
DocChart=DocChart

fileName = Dateiname
export_list = Liste exportieren
IMPORT_CORRECTION = Korrekturimport
DELIVERED_STYLE = status_green
ARCHIVED_STYLE = status_green
BEING_SENT_CLIENT_STYLE = status_orange
SENT_ICON = fa fa-paper-plane-o
PARTIALLY_ACCEPTED_WITH_AMENDMENTS = Teilweise best\u00E4tigt mit \u00C4nderungen
UNDEFINED_STYLE = Standard
REFERENTIAL_OK = Stammdaten ok
PARTIALLY_ACCEPTED = Teilweise best\u00E4tigt
BLOCKED_STYLE = status_orange
IN_VALIDATION = Validierung l\u00E4uft
REFERENTIAL_KO = Stammdaten fehlgeschlagen
METADATA_KO_STYLE = status_red
UNARCHIVABLE_STYLE = status_red
BEING_SENT_CLIENT = Sendung an Kunden l\u00E4uft
SUBMITTED = \u00DCberwiesen
NO_ROUTE = Route nicht vorhanden
UNKNOWN_ICON = fa fa-remove
ERROR_STYLE = status_red
UPDATED_STYLE = Info
IN_DELIVERY_STYLE = status_orderlist_yellow
INTEGRATED_STYLE = status_green
AWAITING_VALIDATION_STYLE = status_orange
REFUSED_STYLE = status_red
IN_PREPARATION_STYLE = status_orderlist_yellow
CLOSED_STYLE = Standard
Development = Entwicklung
TO_CORRECT_STYLE = status_yellow
REFERENTIAL_KO_STYLE = status_red
IN_PREPARATION = Vorbereitung l\u00E4uft
CANCEL_ICON = fa fa-times
Production = Produktion
PARTIALLY_ACCEPTED_STYLE = status_partially_accepted
UNKNOWN_STYLE = status_yellow
Acceptance = Rezept
CONTROL_OK_STYLE = status_green
SENT_PARTIALLY_STYLE = status_yellow
WARNING_STYLE = status_yellow
CONTROL_TOTAL_KO = Betragskontrollen fehlgeschlagen
ACCEPTED_WITH_AMENDMENT_STYLE = Info
ERROR_ICON = fa fa-remove
REFUSED_ICON = fa fa-exclamation-triangle
SYSTEMATIC = Systematische Generierung
FATAL_STYLE = status_red
DEMAT_ERR = Digitalisierungsfehler
TO_REMOVE_STYLE = status_yellow
PENDING_STYLE = status_yellow
ACCEPTED_STYLE = Info
ANSWERED_STYLE = Info
UNARCHIVABLE = Nicht archivierbar
IN_DELIVERY = Wird ausgeliefert
END_PROCESS = Verarbeitung abgeschlossen
PAID_STYLE = status_green
RESOLVED_STYLE = status_green
CORRECT_STYLE_STAGE = Erfolg
TO_VALIDATE_STYLE = status_yellow
FORCED_STYLE = Standard
SIGNATURE_KO_STYLE = status_red
SUBMITTED_STYLE = status_green
IMPORT_CORRECTION_STYLE = Info
ERR_XLEG = Gesetzl: Pr\u00FCfungsfehler
IN_SUBMISSION = Auszahlung l\u00E4uft
SMTP_ERROR_STYLE = status_red
REFUSED_MANUALLY_STYLE = Standard
METADATA_KO = Metadaten fehlgeschlagen
IN_VALIDATION_STYLE = status_orderlist_blue
PARTIALLY_ACCEPTED_WITH_AMENDMENTS_STYLE = status_deep_blue
REFERENTIAL_OK_STYLE = status_green
SIGNATURE_OK_STYLE = status_green
INVOICED_STYLE = status_green
CONTROL_TOTAL_KO_STYLE = status_red
OK_STYLE = status_green
VALIDATED = Validiert
NO_ROUTE_STYLE = status_yellow
REMOVED_STYLE = Standard
ERROR_STYLE_STAGE = Gefahr
APPROVED_STYLE = Info
AWAITING_VALIDATION = Validierung ausstehend
PAID = Bezahlt
DEMAT_ERR_STYLE = status_red
CONTROL_OK = Kontrollen OK
SMTP_ERROR = SMTP-Fehler
UNDEFINED_STYLE_STAGE = Standard
TO_PAY = Zahlbar
TIMEOUT_ICON = fa fa-remove fa-fw
NONE_STYLE = status_yellow
IN_DISPUTE_STYLE = status_yellow
TIMEOUT_STYLE = status_red
READ_STYLE = Info
SYNTAX_ERR_STYLE = status_red
SIGNATURE_KO = Signatur fehlgeschlagen
SHIPPED_STYLE = status_green
END_PROCESS_STYLE = status_green
SIGNATURE_OK = Signatur OK
NO_GENERATION_IF_EXISTS = Wird nicht generiert, wenn die Datei existiert
READ_ICON = fa fa-eye
SENT_STYLE = Info
ACQUITTED_STYLE = status_green
REPLACE_IF_EXISTS = Generierung mit Ersatz
DUPLICATE_STYLE = status_yellow
ACCEPTED_WITH_AMENDMENT_ICON = fa fa-check-circle-o
IN_SUBMISSION_STYLE = status_yellow
TO_PAY_STYLE = status_green
VALIDATED_STYLE = status_orderlist_green
CANCEL_STYLE = Standard
RESENT_STYLE = status_green
ERR_XLEG_STYLE = status_red
PARTIALLY_SHIPPED_STYLE = status_yellow
RESENT = Zur\u00FCckgeschickt
Preproduction = Vorproduktion
selection_or_enter = Wert ausw\u00E4hlen oder eingeben
partner_user_send_pass_yes = Ja, ich m\u00F6chte es senden
portlet_invoice_refused_manually = RECHNUNG: Manuell ablehnen
users_search_placeholder = Benutzer nach Benutzername, Vorname oder Nachname suchen
portlet_asn_ship = ASN: Versenden
ANSWERED_ICON = fa fa-undo fa-fw fa-rotate-90
client_type_EDI = Partner
instance_type_SPECIFIC = Spezifisch
organization_autoGenerationOfSSCC = SSCC generiert
error_removing_client = Fehler beim L\u00F6schen des Kunden ({0}).
R = Empf\u00E4nger
S = Sender
agreement_file_not_found = {0} Vereinbarungsdatei f\u00FCr {1} Sprache nicht gefunden
perimeter_edit = Bereich bearbeiten f\u00FCr
SMARTPDF = SUPPLIER PORTALS
portlet_referentiel_taxes = Stammdaten/Steuern
check_site_conditions = Ich akzeptiere die Nutzungsbedingungen der Website
correct = Korrigieren
Noname = KEIN NAME!
portlet_invoice_join = RECHNUNG: Zusammenf\u00FChren
deploy = Einsetzen
portlet_files = DATEIEN
cart_checkout = BEST\u00C4TIGEN
a_day_past = weniger als einen Tag
filterSuccessfullySaved = Der Filter wurde gespeichert
users_email = Benutzer/E-Mail
contextual_validation_user_scope_partner_id = Die ID-Nummern f\u00FCr den Partner {0} und den Bereich {1} m\u00FCssen f\u00FCr den Benutzer {2} unterschiedlich sein.
organization_end = Enddatum
portlet_deadpool = Deadpool
cart_view = WARENKORB ANZEIGEN
root = Wurzelverzeichnis
clients_type_EDI = Partner
bank_account_bic = BIC
menu_process_execution = Prozess: Ausf\u00FChrungen
from_address_validator = Die E-Mail-Adresse des Absenders ist ung\u00FCltig. Es kann nur eine Adresse angegeben werden.
organization_address_country_iSO3Country = ISO-3-Code des Landes
demat_partner_dialog_title = \u00C4nderungsverlauf
angular_url = Angular-URL
document_type = Dokumententyp
related_process = Nach verkn\u00FCpften Prozessen suchen
clear_query = L\u00F6schen
info_client_saved = Kunde {0} gespeichert
date_last_authentication_no_date = Datum der letzten Anmeldung: Informationen nicht verf\u00FCgbar
instance_type_INVOICE = Rechnung
portlet_invoice_forced = RECHNUNG: Erzwingen
angular_template = Angular-Vorlage
error_password_link_generation = Bei der Generierung des Links zur Zur\u00FCcksetzung des Passworts ist ein Fehler aufgetreten.
cart_totalAmount = Insgesamt:
rte_input_file_must_be_unique = Die Eingabedatei muss eindeutig sein
error_exporting_client = Fehler beim Exportieren von Kunden ({0}).
organization_address_country_country = ISO-2-Code des Landes
aws_batch = Batch AWS
environment_error_max_characters_allowed = Maximal 64\u00A0Zeichen sind erlaubt
error_export_not_empty_portlet = Sie k\u00F6nnen nicht exportieren: Das Portlet ist nicht leer.
info_client_removed = Kunde {0} gel\u00F6scht
cart_numberOfArticles = Artikel(n) im Warenkorb
order_history_title = BESTELLVERLAUF NR.:
rte_status_not_deployed = Das RTE-Skript ist nicht bereitgestellt
error_importing_page = Fehler beim Importieren der Seite
identical_new_passoword = Das neue Passwort ist identisch mit dem alten Passwort
portlet_invoice_remove = RECHNUNG: L\u00F6schen
logistic_gs1_error_format_message = Der einheitliche nationale Lieferantencode (GS1) muss aus 7 bis 10 Ziffern bestehen.
action_line = Aktion auf der Linie
notification_new_contact = E-Mail-Benachrichtigung neuer Kontakt
placeholder_database_name = Name der Instanzdatenbank
contact_recipients = Empf\u00E4nger der Kontakt-E-Mail
portlet_referentiel_produits_other = Stammdaten/Produkte/Sonstiges
portlet_documentation = DOKUMENTATION
not_valid = Nicht g\u00FCltig
logistic_gs1_company_prefix = GS1-Unternehmenscode (CNUF)
error_export_empty_page = Sie k\u00F6nnen keine leere Seite exportieren;
no_records_found_loreal_order = Es wurden keine passenden Artikel gefunden. <br/> Bitte \u00E4ndern Sie Ihre Suche.
rte_status_deployed = Das TEN-Skript wurde erfolgreich bereitgestellt
TO_VALIDATE_ICON = fa fa fa-pause fa-fw
GCN = COLLABORATIVE NETWORK
info_import_processes = Die Prozesse wurden importiert
document_statuses_added = {0} neue Status wurde(n) hinzugef\u00FCgt
logistic_serial_reference_required = Die Sequenznummer ist ein Pflichtfeld, da die automatische Generierung von SSCCs aktiviert ist.
rte_base_not_supported = RTE-Sammlungen sind (vorerst) vom Testbereich ausgeschlossen.
client_type_SPECIFIC = Partner
user_password_dialog_header = Passwort hinzuf\u00FCgen
portlet_order = Bestellung
self_register_placeholder_2_placeholder = Eindeutiges Anmeldefeld der Selbstregistrierungsseite
general_edi_service = Generix-EDI-Service
total_carts = Gesamtbetrag der Warenk\u00F6rbe:
placeholder_host_name = Hostname oder IP-Adresse des Systems, auf dem die Instanz l\u00E4uft
menu_process_trigger = Prozess: Trigger
add_new_status = Dokumentenstatus hinzuf\u00FCgen
logistic_gs1_company_not_provided = Bitte geben Sie den einheitlichen nationalen Lieferantencode ein.
contextual_validation_user_scope_user_cpy = Benutzer {0} geh\u00F6rt nicht zu {1}.
exception_duplicate_perimeter = Duplizierter Bereich: {0}.
document_statuses_updated = {0} Status wurde(n) aktualisiert
menu_process_deployment = Prozess: Bereitstellung
ks_error_could_not_extract_certificate = Das in der Datei enthaltene(n) Zertifikat(e) kann/k\u00F6nnen nicht extrahiert werden.
client_number = Nr. ${client}
portlet_referentiel_global_allowances_charges = Stammdaten/Globale Rabatte und Zuschl\u00E4ge
to_prepare = Vorbereitung l\u00E4uft
banking = Bankwesen
rte_status_deployed_but_changed = Das RTE-Skript wurde seit der Bereitstellung ge\u00E4ndert
error_saving_perimeter = Fehler beim Speichern des Bereichs ({0}).
error_saving = Fehler beim Speichern ({0})
portlet_invoice_actions_10 = RECHNUNG: Aktion\u00A010
general_invoices_service = Generix-Rechnungsservice
agreement_default_required = Die Bedingungen der Servicevereinbarung m\u00FCssen mit der Standardsprache verkn\u00FCpft sein. {0} (Registerkarte \u201EServicevereinbarung\u201C)
import_cert_pem_cer_crt = PEM/CER/CRT
host_name = Domain
estimated_delivery_date = Voraussichtliches Lieferdatum:
SENT_PARTIALLY_ICON = fa fa-paper-plane
EDOCUMENT = INVOICING SERVICES
info_category = Zum ordnen der Kategorien muss die unterste Kategorie per Drag\u00A0&\u00A0Drop nach oben verschoben werden.
error_password_generation = Bei der Generierung des Passworts ist ein Fehler aufgetreten.
self_register_placeholder_1 = Selbstregistrierung Platzhalter\u00A01
self_register_placeholder_2 = Selbstregistrierung Platzhalter\u00A02
Category.name = Kategorie
clients_type_INVOICE = Partner
organization_bankAccount_iban = IBAN
bank_account_name_required = Der Name ist obligatorisch
document_status = Dokumentenstatus
java_lang_Integer = Ganzzahl
organization_bankAccount_bic = BIC
perimeters = Bereiche
logistic_missing_mandatories_message = Bitte geben Sie alle erforderlichen Informationen ein, da die automatische Generierung von SSCC aktiviert ist.
portlet_freetext = FreeText
ignored = Ignoriert
user_email = E-Mail-Adresse
logistic_gs1_company_required = Der GS1-Code ist ein Pflichtfeld, da die automatische Generierung von SSCC aktiviert ist.
bql_filter_details = BQL-Abfrage zur Filterung der Rechnungs-Portlet
portlet_order_actions_10 = BESTELLUNG: Aktion 10
bank_account_currency = W\u00E4hrung
portlet_order_confirm_with_modification = BESTELLUNG: Best\u00E4tigen mit \u00C4nderung
agreement_instance_required = Servicevereinbarungsinstanz ist erforderlich
error_invalid_date = {0}: Der eingegebene Wert ist kein Datum: {1}
warm_change_into_empty_when_other_exists = Diese Anordnung kann nicht in eine leere Anordnung ge\u00E4ndert werden, solange andere Anordnungen vorhanden sind
portlet_invoice_export_list = RECHNUNG: Liste exportieren
agreement_lang_required = Servicevereinbarungssprache ist erforderlich
logistic_serial_reference = Sequenznummer
portlet_indexdata_export = INDEXDATA: Exportieren
cookie = Cookie
no_processed = Nicht verarbeitet
warm_add_empty_when_other_exits = Sie k\u00F6nnen keine leere Anordnung hinzuf\u00FCgen, wenn bereits eine Anordnung existiert
instance_type = Portaltyp
portlet_invoice_actions_3 = RECHNUNG: Aktion\u00A03
portlet_invoice_actions_4 = RECHNUNG: Aktion\u00A04
portlet_invoice_actions_1 = RECHNUNG: Aktion\u00A01
portlet_invoice_actions_2 = RECHNUNG: Aktion\u00A02
portlet_invoice_actions_7 = RECHNUNG: Aktion\u00A07
portlet_invoice_actions_8 = RECHNUNG: Aktion\u00A08
portlet_invoice_actions_5 = RECHNUNG: Aktion\u00A05
triggername_ACTION_10 = Aktion 10
portlet_invoice_actions_6 = RECHNUNG: Aktion\u00A06
menu = Men\u00FC-Rechte
portlet_invoice_actions_9 = RECHNUNG: Aktion\u00A09
portlet_referentiel_produits_remise_charge = Stammdaten/Produkte/Rabatt und Zuschlag
return = Zur\u00FCck
edit_library = Eine Bibliothek bearbeiten
url_tracking = URL-Tracking f\u00FCr jeden angemeldeten Benutzer aktivieren
my_bank_accounts = Meine Bankkonten
partner_user_message_send_new_pass_msg = Der Benutzer erh\u00E4lt eine E-Mail mit einem Link, \u00FCber den er sein Passwort \u00E4ndern kann.
library_created_success = Die Dokumentation wurde erstellt
portlet_orderresponse_import = ORDER RESPONSE: Importieren
java_lang_Class = Klasse
contextual_validation_partner_role = Die Rolle muss existieren
clientUsers = Meine Benutzer
processed = Verarbeitet
document_status_updated = Der Status des Dokuments {0} wurde aktualisiert
History = RoquetteHistory
rte_test_properties_placeholder = Eigenschaften:
in_delivery = Wird ausgeliefert
rte_forbidden_value = init.tst: Die Eigenschaft {0} ist nicht korrekt angegeben
regex = Regex
portlet_invoice_add = RECHNUNG: Hinzuf\u00FCgen
com_byzaneo_xtrade_api_DocumentStage = Status
uploaded_since = Hochgeladen von
indexClassName = Indextyp
error_localizations_import_bad_structure = Die Struktur der Datei entspricht nicht dem erwarteten Format
customer_partner_message_delete = Der ${client} wird endg\u00FCltig gel\u00F6scht.
error_export_portlet_rte_collection = Sie k\u00F6nnen das Portlet \u201ECollection\u201C nicht exportieren.
database_name = Name der Datenbank
to_edit = Bearbeiten
portlet_invoice_export = RECHNUNG: Exportieren
channel_auth_userDnPattern = Benutzer-DN-Muster
info_import_portal = Das Portal wurde importiert
progress = In Bearbeitung\u00A0...
edit_status = Einen Status f\u00FCr die Dokumente bearbeiten
scope_customer = Eingeschr\u00E4nkter Bereich
portlet_referentiel_produits = Stammdaten/Produkte
eDocument = E-Dokument
active_carts = aktiver Warenkorb\u00A0/aktive Warenk\u00F6rbe
portlet_asn_import = ASN: Importieren
doc_status_code = Statuscode
general_customers_service = Generix-Kundenservice
category_field_empty = Kategorie ist ein Pflichtfeld.
info_user_role_duplicated = Die Rolle {0} wurde dupliziert
no_processes_were_found = Der RTE wird in keinem Prozess aufgerufen
organization_bankAccount_partner = Partner
info_perimeter_saved = Bereich {0} gespeichert
com_byzaneo_xtrade_api_DocumentStatus = Status
rte_status_unknown = Zu bestimmen
status_import_file = Import einer Datei mit Dokumentstatus (.xls, .xlsx)
import_cert_pkcs7 = PKCS#7
save_register = Ich best\u00E4tige meine Registrierung
disagree = Ablehnen
error_saving_client = Fehler beim Speichern des Kunden ({0}).
RECIPIENT = Empf\u00E4nger
contact_subject = Betreff der Kontakt-E-Mail
user_email_placeholder = E-Mail-Adresse f\u00FCr die Anmeldung
logistic_serial_reference_and_cnuf_error_format_message = Die L\u00E4nge des CNUF und der Sequenznummer muss 16 Zeichen betragen.
java_math_BigDecimal = Dezimalzahl
portlet_faq = FAQ
agree = Akzeptieren
ACCEPTED_ICON = fa fa-check
Contract = RoquetteContract
organization_extension = Erweiterungszeichen
add_description = Beschreibung hinzuf\u00FCgen
triggername_ACTION_2 = Aktion 2
triggername_ACTION_1 = Aktion 1
triggername_ACTION_4 = Aktion 4
triggername_ACTION_3 = Aktion 3
triggername_ACTION_6 = Aktion 6
triggername_ACTION_5 = Aktion 5
triggername_ACTION_8 = Aktion 8
triggername_ACTION_7 = Aktion 7
triggername_ACTION_9 = Aktion 9
menu_rights_clients = Kunden
info_addressSearch = Es ist nicht m\u00F6glich, nach L\u00E4nderfeldern zu suchen
imenu.portal = Kollaborativ
in_validation = Validierung l\u00E4uft
portlet_library = Bibliothek
contextual_validation_user_scope_role = Benutzer {0} hat nicht die Rolle {1}
error_importing_Languages = Beim Importieren der Portal-Lokalisierungsdatei ist ein Fehler aufgetreten
bank_account_iban_error_exist = Leider ist der ausgew\u00E4hlten W\u00E4hrung bereits eine IBAN zugeordnet. Bitte entfernen Sie die IBAN, bevor Sie eine neue hinzuf\u00FCgen
rte_collection = Sammlung
invalid_password = Das Passwort ist ung\u00FCltig. Bitte geben Sie ein neues Passwort ein.
validated = Validiert
channel_AuthenticationEndpointConfiguration = Authentifizierung
scope_partner = Erweiterter Bereich
java_lang_Boolean = Boolean
jsf_template = Jsf-Vorlage
exception_import_page_null = Die Exportseite ist null.
portlet_referentiel_produits_general_description = Stammdaten/Produkte/Allgemein/Beschreibung
agreement = Service-Vereinbarung
autoGenerationOfSSCC = SSCC generiert
bank_account_bic_error_required = Der BIC-Code ist obligatorisch
logistic_sscc = SSCC
users_number_exceeded = Sie \u00FCberschreiten die Anzahl der Benutzer f\u00FCr einen ${Client}. Bitte l\u00F6schen Sie inaktive Benutzer oder wenden Sie sich an den Support.
client_type_SUPPLIER = Lieferant
requested_delivery_date = Gew\u00FCnschtes Lieferdatum:
contextual_validation_user_scope_user = Der Benutzer {0} existiert nicht
rte_test_results_header = RTE-Testergebnisse
user_email_address = E-Mail
rte_test_fail_message = RTE-Test fehlgeschlagen
locale_not_defined = Die Sprache Ihres Partnerdatensatzes ist nicht festgelegt, bitte geben Sie eine Sprache an
portlet_invoice_view_history = RECHNUNG: Pr\u00FCfen
java_util_Date = Datum
organization_gs1 = GS1-Unternehmenscode
clients_type_SPECIFIC = Partner
order_number = Bestell-Nr.
info_perimeter_removed = Bereich {0} gel\u00F6scht
warn_deleting_own_user = Der Benutzer {0} kann nicht gel\u00F6scht werden, da es Ihr eigener Benutzer ist.
portlet_pack = Paket
organization_start = Startdatum
company_society = Unternehmen
portal_general = Allgemein
contextual_validation_user_scope_partner = Partner {0} geh\u00F6rt nicht zu {1}.
logistic_sscc_auto = Automatische SSCC
TO_REMOVE_ICON = fa fa-archive
customer_partner_connectionCode = Eindeutige Anmelde-Nr.
self_register_placeholder_1_placeholder = Feld Nr. ${client} auf der Selbstregistrierungsseite
info_parameters_saved = Benutzereinstellungen gespeichert
connection_code = Eindeutige Anmelde-Nr.
portlet_taxes_allowances = Rabatte und parafiskalische Steuern
organization_role = Rolle
organization_serialReference = Sequenznummer
banking_partner = Bankpartner
exception_perimeter_has_users = Dem Bereich {0} sind {1} Benutzer zugeordnet.
instance_type_EDI = EDI
add_sso_role_mapping = Rollenzuordnung hinzuf\u00FCgen
logistic_extension_required = Das Erweiterungszeichen ist erforderlich, da die automatische Generierung von SSCC aktiviert ist.
error_saving_company = Fehler beim Speichern des Kunden.
self_register = Selbstregistrierung
error_invalid_row_length = [{0},{1}] Anzahl der erwarteten ({2}) und gefundenen ({3}) fehlerhaften Felder.
localisation = Portal-Standort
error_invalid_number = {0} : Die Eingabe ist keine Zahl: {1}
bank_accounts = Bankkonten
quantityValue_validator = Der Wert der Menge ist ung\u00FCltig
info_partner_role_saved = Partnerrolle gespeichert
exception_duplicate_email = Diese E-Mail-Adresse wurde bereits erstellt
freetext_details = Freie Felder\u00A0/ Details
error_input_content_not_valid = Der eingegebene Inhalt ist ung\u00FCltig: {0}
CUSTOMERS = KUNDENPORTALE
channel_auth_userSearchBase = Benutzersuchbasis
error_export_empty_portlet = Sie k\u00F6nnen kein leeres Portal exportieren;
bank_account_delete_confirm = Sind Sie sicher, dass Sie die IBAN f\u00FCr die W\u00E4hrung l\u00F6schen m\u00F6chten?
arn = ARN der IAM-Rolle
xPath = xPath
client_type_CUSTOMER = Kunde
PARTIALLY_SHIPPED_ICON = fa fa-paper-plane
sso_role_mapping = Rollenzuordnung
canNotSaveFilter = Sie k\u00F6nnen diesen Filter nicht speichern, weil der Speicherort voll ist
portlet_collection = Sammlung
run_rte = Ausf\u00FChren
factor = Factor
general_supplier_service = Generix-Lieferantenservices
quicksight = QuickSight
portlet_switch_user = Identit\u00E4ts\u00FCbernahme
logistic = Logistik
confirmScopeSelection = Sie werden Ihre Auswahl des Partnerbereichs verlieren
client_type_INVOICE = Partner
javax_xml_datatype_XMLGregorianCalendar = XML-Datum
error_exporting_page = Fehler beim Exportieren der Seite.
contact_validator = Die E-Mail-Adresse des Kontakts ist ung\u00FCltig. Mithilfe eines Kommas (,) als Trennzeichen k\u00F6nnen mehrere Adressen eingegeben werden.
menu_basket = Warenkorb
placeholder_tcp = TCP-Port, auf dem die Instanz lauscht
portlet_orderresponse_print = ORDER RESPONSE: Drucken
document_status_removed = Der Status des Dokuments {0} wurde entfernt
bank_account_iban_error_notValid = Die IBAN ist ung\u00FCltig
field_missing = Das Feld \"{0}\" fehlt.
portlet_referentiel_produits_general_net_price = Stammdaten/Produkte/Allgemein/Nettopreis
cron_expression = Cron-Ausdruck
no_message_display = Diese Nachricht nicht mehr anzeigen
add_library = Bibliothek hinzuf\u00FCgen
permission_missing = Sie haben nicht die erforderlichen Berechtigungen, um auf diese Funktion zuzugreifen
bank_account_bic_error_notValid = Der BIC-Code muss 8 bis 11 Zeichen enthalten
invalid_file_special_char = Die Datei darf keine Sonderzeichen enthalten
organization_bankAccount_date = Erstellungsdatum
document_status_added = Der Status des Dokuments {0} wurde hinzugef\u00FCgt
portlet_invoice_open = RECHNUNG: \u00D6ffnen
duplicate_mapping = Diese Zuordnung existiert bereits.
document_status_exists = Der Status des Dokuments {0} existiert bereits
portlet_carousel = Karussell
warn_kpi_invalid = Der KPI-Dienst kann nicht gestartet werden. Bitte versuchen Sie es sp\u00E4ter erneut oder wenden Sie sich an Ihren Administrator.
exception_role_has_sso = Die Rolle {0} hat SSO-Rollen-Zuordnungen.
organization_bankAccount_currency = W\u00E4hrung
portlet_invoice_diagnostic = RECHNUNG: Diagnostizieren
integrationnotification = Bericht \u00FCber die Prozessintegration abrufen
portlet_invoice_import = RECHNUNG: Importieren
error_removing_perimeter = Fehler beim L\u00F6schen des Bereichs ({0}).
instance_type_CUSTOMER = Kunde
generate_reset_link_expired = Der Link zum Zur\u00FCcksetzen des Passworts ist abgelaufen.
imenu_messaging = Nachrichtendienst
one_hour_past = weniger als eine Stunde
control_ean = L\u00E4ngenpr\u00FCfung des EAN-Code-Feldes
exception_export_null = Kein Export in der Datei gefunden
quick_search_loreal_order = Schnellsuche nach Marke, Artikel, Filialcode, EAN
default_test_rte = Standardtest
logistic_sscc_fieldset = SSCC-Nummer
import_role_header = Import einer Rollendatei (*.xls, *.xlsx)
info_perimeter_duplicated = Der Bereich {0} wurde dupliziert
ks_error_during_file_reading = Beim Lesen der Datei ist ein Fehler aufgetreten.
generate_reset_link_invalid = Ung\u00FCltiger Link zum Zur\u00FCcksetzen des Passworts. Bitte wenden Sie sich an Ihren Administrator.
error_duplicate_configuration = Die Konfiguration existiert bereits
doc_status_style = Stilstatus
exception_exchange_associated = {0} erlaubte verkn\u00FCpfte \u00DCbertragung(en)
self_register_ok = Die Registrierung war erfolgreich!<br/>Ihnen wurde per E-Mail ein Link zur Zur\u00FCcksetzung des Passwort gesendet.
clients_type_SUPPLIER = Lieferanten
partner_user = Benutzer
instance_type_SUPPLIER = Lieferant
classificationPlan = Ablageplan
tcp_port = TCP-Port
logistic_missing_sscc_message = Bitte geben Sie alle erforderlichen Informationen ein
portlet_referentiel_carrier = Stammdaten/Spediteur
user_password_confirm = Passwort best\u00E4tigen
author = Autor
self_register_ko = Bei der Registrierung Ihres Kontos ist ein Problem aufgetreten. Bitte versuchen Sie es sp\u00E4ter erneut.
warn_portlet_localization_lang_not_supported = Die Sprache {0} wird von der Umgebung nicht unterst\u00FCtzt
perimeter = Bereich
NONE_ICON = fa fa-envelope-o
site_conditions_link = Nutzungsbedingungen der Website
logistic_extension = Erweiterungszeichen
subtype = Subtyp
individual_tests_rte = Individueller Test
channel_auth_groupSearchBase = Gruppensuchbasis
control = Kontrollieren
partner_with_code_missing = Die Nummer ${Client} und/oder die Registrierungsnummer existieren nicht
bql = BQL
bank_account_iban = IBAN
channel_auth_groupSearchFilter = Gruppensuchfilter
library_edited_succes = Die Dokumentation wurde ge\u00E4ndert
info_portlet_localization_import = Der Import des Lokalisierungsportals ist abgeschlossen.{0} Nachricht(en) hinzugef\u00FCgt und {1} bearbeitet
menu_rights_users = Benutzer
invalid_file_csv_type = Das Dateiformat muss CSV sein
rte_property_unknown = init.tst: Die Eigenschaft {0} ist unbekannt.
RTE = EDI-DIENSTLEISTUNGEN
database_kpi = KPI-Datenbank
bank_accounts_dlg_header = Bankkonto hinzuf\u00FCgen
portlet_contact_us = Kontaktieren Sie uns
exception_perimeter_has_partners = Dem Bereich {0} ist/sind {1} Partner zugeordnet.
portlet_invoice_print = RECHNUNG: Drucken
user_empty_client = W\u00E4hlen Sie einen ${client}\u00A0...
ks_error_no_certificate_found = Kein Zertifikat in der Datei gefunden.
by_date = der {0}
bank_account_deleting_error = Fehler beim L\u00F6schen des Bankkontos {0}
channel_auth_userSearchFilter = Benutzersuchfilter
bank_account_iban_error_required = Die IBAN ist obligatorisch
save_success = Die gesamte Bibliothek wurde gespeichert
edocument = E-Dokument
warn_import_pt_collection = Wenn das Portal \u201ECollection\u201C-Portlets verwendet, ist eine Konfiguration dieser Portlets erforderlich
customer_partner_show_users = Mit dem Kunden verbundene Benutzer
portlet_orderresponse_export = ORDER RESPONSE: Exportieren
validity_date = Ablaufdatum:
action_global = Globale Aktion
organization_bankAccount_user = Erstellt von
portlet_order_mark_as_unread = BESTELLUNG: Als ungelesen markieren
portlet_invoice_view_attachment = RECHNUNG: Anhang anzeigen
logistic_serial_reference_error_format_message = Die Sequenznummer muss aus 6 bis 9 Ziffern bestehen.
notifications = Benachrichtigungen
portlet_order_actions_9 = BESTELLUNG: Aktion 9
portlet_order_actions_8 = BESTELLUNG: Aktion 8
menu_rights_roles = Rollen
advSearch = Erweiterte Suche
warn_localizations_import_portlet_not_found = Portlet {0} wurde in der Portaldefinition nicht gefunden
agreement_version_required = Servicevereinbarungsversion ist erforderlich
SENDER = Sender
portlet_order_actions_1 = BESTELLUNG: Aktion 1
portlet_order_actions_3 = BESTELLUNG: Aktion 3
portlet_order_actions_2 = BESTELLUNG: Aktion 2
portlet_order_actions_5 = BESTELLUNG: Aktion 5
portlet_order_confirm = BESTELLUNG: Best\u00E4tigen
portlet_order_actions_4 = BESTELLUNG: Aktion 4
portlet_order_actions_7 = BESTELLUNG: Aktion 7
portlet_order_actions_6 = BESTELLUNG: Aktion 6
exception_task_properties_not_found = Der Import der Aufgabe {0} ist nicht m\u00F6glich, ein Versionsupgrade ist erforderlich
edit_user_button = Benutzer aktualisieren
self_register_bottom_msg = Nach Abschluss der Registrierung erhalten Sie eine E-Mail mit Ihrem tempor\u00E4ren Passwort. Dieses Passwort muss bei der ersten Anmeldung ge\u00E4ndert werden.
PENDING_ICON = fa fa-eur
library_deleted_success = Die Dokumentation wurde gel\u00F6scht
rte_no_init_test_file = Es wurde keine standardm\u00E4\u00DFige init.tst-Datei gefunden
rte_test_success_message = RTE-Test erfolgreich durchgef\u00FChrt
warn_partner_missing = Der Partner wurde nicht gefunden (bitte den Parameter \u201Epcode\u201C \u00FCberpr\u00FCfen).
partner_user_send_pass_no = Nein, vorerst nicht