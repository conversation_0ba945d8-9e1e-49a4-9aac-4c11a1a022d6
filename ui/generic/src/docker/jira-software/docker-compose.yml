jira:
  image: cptactionhank/atlassian-jira-software:7.8.1
  labels:
    traefik.frontend.value: 'jira.chassagne-project.generixgroup.com'
    traefik.frontend.passHostHeader: 'true'
    traefik.frontend.rule: 'Host'
    traefik.port: '8080'
  volumes:
  - /data/jira/data:/var/atlassian/jira
  links:
  - jira_postgres:postgres
  log_opt:
    max-size: '50m'
  restart: 'no'
  cpu_shares: 512
  mem_limit: 3758096384
jira_postgres:
  image: postgres:9.5
  labels:
    traefik.enable: 'false'
  environment:
    POSTGRES_USER: 'jira'
    POSTGRES_PASSWORD: 'jira'
    POSTGRES_DB: 'jiradb'
  volumes:
  - /data/jira/data/postgres:/var/lib/postgresql/data
  log_opt:
    max-size: '50m'
  restart: 'no'
  cpu_shares: 512
  mem_limit: 536870912

