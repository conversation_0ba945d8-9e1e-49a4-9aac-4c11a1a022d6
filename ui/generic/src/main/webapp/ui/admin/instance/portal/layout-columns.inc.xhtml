<p:outputPanel xmlns="http://www.w3.org/1999/xhtml" 
	xmlns:f="http://xmlns.jcp.org/jsf/core" xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets" 
	xmlns:p="http://primefaces.org/ui"
	xmlns:b="http://byzaneo.com/ui"
	xmlns:gnx="http://xmlns.jcp.org/jsf/composite/components/gnx"
	id="ppgColumnsLayoutPnl" styleClass="psPpgColumnsLayoutPnl"
	rendered="#{layout!=null and    layout.getClass().getSimpleName()=='ColumnsLayout'}">
	
	<style type="text/css">
	.ui-datalist-data {
		width: 90%;
	}
	</style> 
	
	<!-- Iterate through all layouts -->
		<!-- COLUMNS -->
			<p:dataList id="columns" value="#{layout.columns}" var="col" varStatus="colStatus" type="definition" itemStyleClass="#{col.styleClass}">
				<!-- ADDING -->
				<p:outputPanel styleClass="center" style="margin-bottom:2px;"> 
					<p:commandLink value="#{labels.add} #{labels.portlet.toLowerCase()}" onclick="addPortlet([{name:'layoutIndex', value:#{layoutStatus.index}}, {name:'col', value:#{colStatus.index}},{name:'row', value:-1},{name:'above', value:'false'}]);return false;" style="font-weight:normal;color:#aaa;"/>
				</p:outputPanel>
				<ui:include src="layout-columns-portlets.inc.xhtml">
					<ui:param name="column"   value="#{col}" /> 
					<ui:param name="header"   value="false" /> 
					<ui:param name="colidx"   value="#{colStatus.index}" /> 
					<ui:param name="colfirst" value="#{colStatus.first}" /> 
					<ui:param name="collast"  value="#{colStatus.last}" /> 
					<ui:param name="layoutIndex" value="#{layoutStatus.index}">
				</ui:param> 
				</ui:include>
			</p:dataList>
				<p:commandButton id="changeLayout" icon="fa fa-th-large" title="#{labels.layout}" onclick="PF('wPageLayoutDlg').show()" actionListener="#{gnxPortalISHandler.setIndexOfLayoutToEdit(layoutStatus.index)}" 
						styleClass="mls"  style="float: right;" process="@this"/>
				
				<p:commandButton icon="fa fa-trash-o" title="#{comlbls.delete}" styleClass="mls" 
								  update="@(.psPtlPagePnl)" style="float: right;"
								disabled="#{not gnxPortalISHandler.canBeRemoved(currentLayout)}" 
								actionListener="#{gnxPortalISHandler.onRemoveLayoutByIndex(layoutStatus.index)}"/>
				
	<p:remoteCommand name="addPortlet" 					
		actionListener="#{gnxPortalISHandler.onColumnsAddPortlet}"			
		immediate="true" process="@this" update="@(.psPpgColumnsLayoutPnl)" />
	<p:remoteCommand name="movePortlet" 					
		actionListener="#{gnxPortalISHandler.onColumnsMovePortlet}" 
		immediate="true" process="@this" update="@(.psPpgColumnsLayoutPnl)" />
	<p:remoteCommand name="deletePortlet" 					
		actionListener="#{gnxPortalISHandler.onColumnsRemovePortlet}" 
		immediate="true" process="@this" update="@(.psPpgColumnsLayoutPnl)" />
</p:outputPanel>