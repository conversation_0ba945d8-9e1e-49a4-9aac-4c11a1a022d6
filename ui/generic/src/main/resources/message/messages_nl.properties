javax.faces.validator.RegexValidator.NOT_MATCHED_detail = Ongeldige waarde
javax.faces.validator.RegexValidator.PATTERN_NOT_SET = De waarde is niet geldig
javax.faces.component.UIInput.REQUIRED = {0} : Er is een waarde vereist !
primefaces.password.INVALID_MATCH = {0} : Valideringsfout
javax.faces.validator.DoubleRangeValidator.TYPE = De waarde is niet van het correcte type.
javax.faces.validator.RegexValidator.NOT_MATCHED = De waarde is niet geldig
javax.faces.validator.DoubleRangeValidator.NOT_IN_RANGE = De waarde moet liggen tussen de verwachte waarden {0} en {1}.
javax.faces.component.UISelectOne.INVALID = Deze waarde is niet geldig