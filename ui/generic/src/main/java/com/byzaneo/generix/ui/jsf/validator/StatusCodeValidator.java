package com.byzaneo.generix.ui.jsf.validator;

import static com.byzaneo.commons.ui.util.JSFHelper.getLocale;

import java.util.regex.Pattern;

import javax.faces.application.FacesMessage;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.validator.*;

import org.apache.commons.lang3.StringUtils;

import com.byzaneo.commons.ui.util.MessageHelper;

@FacesValidator("statusCodeValidator")
public class StatusCodeValidator implements Validator {

  private static final String REGEX = "[a-zA-Z0-9_]{1,}";

  @Override
  public void validate(FacesContext context, UIComponent component, Object value) throws ValidatorException {
    final String content = (String) value;
    if (StringUtils.isEmpty(content))
      throw new ValidatorException(new FacesMessage(FacesMessage.SEVERITY_ERROR, "The status code can not be empty", ""));

    Pattern p = Pattern.compile(REGEX);
    boolean b = p.matcher(content)
        .matches();
    if (!b) {
      String messageDErreurAAfficher = MessageHelper.getMessage("msglbls.error_invalid_status_code",
          "The status code must contains only chars, digits and _",
          getLocale());
      throw new ValidatorException(new FacesMessage(FacesMessage.SEVERITY_ERROR, messageDErreurAAfficher, ""));
    }
  }

}
