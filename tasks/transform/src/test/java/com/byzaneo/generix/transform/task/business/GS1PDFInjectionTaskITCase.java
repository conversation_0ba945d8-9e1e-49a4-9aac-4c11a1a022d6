package com.byzaneo.generix.transform.task.business;

import com.byzaneo.commons.bean.FileType;
import com.byzaneo.commons.bean.PropertyType;
import com.byzaneo.commons.test.SystemPropertyContextLoader;
import com.byzaneo.generix.edocument.util.XQuery;
import com.byzaneo.generix.service.KeyStoreService;
import com.byzaneo.generix.service.SecurityService;
import com.byzaneo.generix.signature.service.SignatureService;
import com.byzaneo.generix.signature.task.CertificateVerifyingTaskManager;
import com.byzaneo.generix.util.AESEncryptionHelper;
import com.byzaneo.security.bean.Company;
import com.byzaneo.security.bean.Group;
import com.byzaneo.security.bean.Partner;
import com.byzaneo.xtrade.api.ArchiveStatus;
import com.byzaneo.xtrade.api.DocumentStage;
import com.byzaneo.xtrade.api.DocumentStatus;
import com.byzaneo.xtrade.api.DocumentType;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.DocumentFile;
import com.byzaneo.xtrade.service.DocumentService;
import com.byzaneo.xtrade.service.DocumentStatusService;
import com.byzaneo.xtrade.util.DocumentHelper;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.byzaneo.commons.bean.PropertyType.*;
import static com.byzaneo.generix.service.KeyStoreService.*;
import static com.byzaneo.xtrade.util.DocumentHelper.searchFiles;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(locations = { "classpath:/gnx-transform-test.beans.xml" }, loader = SystemPropertyContextLoader.class)
public class GS1PDFInjectionTaskITCase {
  @Autowired
  @Qualifier(CertificateVerifyingTaskManager.COMPONENT_NAME)
  private transient CertificateVerifyingTaskManager certificateVerifyingTaskManager;

  @Autowired
  @Qualifier(SecurityService.SERVICE_NAME)
  private transient SecurityService securityService;

  @Autowired
  @Qualifier(DocumentService.SERVICE_NAME)
  private DocumentService documentService;

  @Autowired
  @Qualifier(SignatureService.SERVICE_NAME)
  private transient SignatureService signatureService;

  @Autowired
  @Qualifier(DocumentStatusService.SERVICE_NAME)
  private DocumentStatusService statusService;

  private GS1PDFInjectionTask gs1pdfInjectionTask;

  private ExtractGS1PDFTask gs1pdfExtractTask;

  private File library;

  private static final String FRIENDLY_NAME = "Certificate HSM";

  private static final String ALIAS = "LABEL_RSA2048";

  private static final String NO_URI = "no_uri";

  private static final String KEYSTORE_PASSWORD = "1234";

  private static final String PDF_NORM = "NAMED_PDF";

  private Company company;

  private Partner from;

  private Partner to;

  private Document certificate;

  private Document keyStoreFolder;

  private Document keyStoreDoc;

  private Document docToSign;

  private boolean envInitialized;

  @BeforeAll
  public static void beforeAll(@Autowired DocumentStatusService statusService) throws Exception {
    DocumentHelper.populateDocumentStatusEntityTable(statusService);
  }

  @BeforeEach
  public void before() throws Exception {
    if (!envInitialized) {
      initgs1pdfInjectionTask();
      initgs1pdfExtractTask();
      initCompanyAndPartners();
      envInitialized = true;
    }
  }

  @AfterEach
  public void after() throws Exception {
    documentService.removeDocuments(Stream.of(docToSign, certificate, keyStoreDoc, keyStoreFolder)
        .collect(Collectors.toList()), true);
  }

  @Test
  @Transactional
  public void test_signature_from_is_company_partner_dont_use_company_private_key() throws Exception {
    createKeyStoreFolder(from);
    createKeyStoreDoc();
    createCertificateAndAssociatedDocFile();
    initDocumentToSignAndAttachedFile(false);

    Map<String, Group> documentsGroups = gs1pdfInjectionTask.getDocumentsGroups(Arrays.asList(docToSign), company, false);
    Map<String, Document> certificates = gs1pdfInjectionTask.getBestCertificateFromGroup(documentsGroups);

    List<DocumentFile> pdfDofs = searchFiles(Collections.singletonList(docToSign), "type = PDF");
    List<DocumentFile> xmlDofs = searchFiles(Collections.singletonList(docToSign), "type = XML");
    List<DocumentFile> xcblDofs = searchFiles(Collections.singletonList(docToSign), XQuery.hasOfficialXcblIndex());

    DocumentFile xmlPdfGs1DocFile = new DocumentFile(
        gs1pdfInjectionTask.getOutputFile(xmlDofs.get(0)
            .getFile()),
        FileType.XML,
        "GS1PDFInjectionTask",
        "500000",
        docToSign,
        "XML/PDF GS1 France Test");
    docToSign.addFile(xmlPdfGs1DocFile);
    Assertions.assertTrue(
        gs1pdfInjectionTask.processDocumentFiles(docToSign, certificates.get(docToSign.getFrom()), pdfDofs.get(0), xmlDofs.get(0),
            xcblDofs.get(0), company,
            xmlPdfGs1DocFile));
    // Verification
    assertEquals(1, gs1pdfExtractTask.verifyGS1Signature(Collections.singletonList(xmlPdfGs1DocFile)));
    assertEquals(DocumentStage.CORRECT, docToSign.getStage());
  }

  @Test
  @Transactional
  public void test_signature_from_is_company_partner_use_company_private_key() throws Exception {
    createKeyStoreFolder(company);
    createKeyStoreDoc();
    createCertificateAndAssociatedDocFile();
    initDocumentToSignAndAttachedFile(false);

    Map<String, Group> documentsGroups = gs1pdfInjectionTask.getDocumentsGroups(Arrays.asList(docToSign), company, true);
    Map<String, Document> certificates = gs1pdfInjectionTask.getBestCertificateFromGroup(documentsGroups);

    List<DocumentFile> pdfDofs = searchFiles(Collections.singletonList(docToSign), "type = PDF");
    List<DocumentFile> xmlDofs = searchFiles(Collections.singletonList(docToSign), "type = XML");
    List<DocumentFile> xcblDofs = searchFiles(Collections.singletonList(docToSign), XQuery.hasOfficialXcblIndex());

    DocumentFile xmlPdfGs1DocFile = new DocumentFile(
        gs1pdfInjectionTask.getOutputFile(xmlDofs.get(0)
            .getFile()),
        FileType.XML,
        "GS1PDFInjectionTask",
        "500000",
        docToSign,
        "XML/PDF GS1 France Test");
    docToSign.addFile(xmlPdfGs1DocFile);
    Assertions.assertTrue(
        gs1pdfInjectionTask.processDocumentFiles(docToSign, certificates.get(docToSign.getFrom()), pdfDofs.get(0), xmlDofs.get(0),
            xcblDofs.get(0), company,
            xmlPdfGs1DocFile));
    // Verification
    assertEquals(1, gs1pdfExtractTask.verifyGS1Signature(Collections.singletonList(xmlPdfGs1DocFile)));
    assertEquals(DocumentStage.CORRECT, docToSign.getStage());
  }

  @Test
  @Transactional
  public void test_signature_from_is_company_use_company_private_key() throws Exception {
    createKeyStoreFolder(company);
    createKeyStoreDoc();
    createCertificateAndAssociatedDocFile();
    initDocumentToSignAndAttachedFile(true);

    Map<String, Group> documentsGroups = gs1pdfInjectionTask.getDocumentsGroups(Arrays.asList(docToSign), company, true);
    Map<String, Document> certificates = gs1pdfInjectionTask.getBestCertificateFromGroup(documentsGroups);

    List<DocumentFile> pdfDofs = searchFiles(Collections.singletonList(docToSign), "type = PDF");
    List<DocumentFile> xmlDofs = searchFiles(Collections.singletonList(docToSign), "type = XML");
    List<DocumentFile> xcblDofs = searchFiles(Collections.singletonList(docToSign), XQuery.hasOfficialXcblIndex());

    DocumentFile xmlPdfGs1DocFile = new DocumentFile(
        gs1pdfInjectionTask.getOutputFile(xmlDofs.get(0)
            .getFile()),
        FileType.XML,
        "GS1PDFInjectionTask",
        "500000",
        docToSign,
        "XML/PDF GS1 France Test");
    docToSign.addFile(xmlPdfGs1DocFile);
    Assertions.assertTrue(
        gs1pdfInjectionTask.processDocumentFiles(docToSign, certificates.get(docToSign.getFrom()), pdfDofs.get(0), xmlDofs.get(0),
            xcblDofs.get(0), company,
            xmlPdfGs1DocFile));
    // Verification
    assertEquals(1, gs1pdfExtractTask.verifyGS1Signature(Collections.singletonList(xmlPdfGs1DocFile)));
    assertEquals(DocumentStage.CORRECT, docToSign.getStage());
  }

  private void initgs1pdfInjectionTask() {
    this.gs1pdfInjectionTask = new GS1PDFInjectionTask();
    gs1pdfInjectionTask.setSignatureService(signatureService);
    gs1pdfInjectionTask.setCertificateVerifyingTaskManager(certificateVerifyingTaskManager);
    gs1pdfInjectionTask.setSecurityService(securityService);
    // when launching the test locally you should use the library below
    // library = new File("src/test/resources/signature-gs1/lib/softhsm2-x64.dll");
    library = new File("/usr/local/lib/softhsm/libsofthsm2.so");
  }

  private void initgs1pdfExtractTask() {
    gs1pdfExtractTask = new ExtractGS1PDFTask();
    gs1pdfExtractTask.setCertificateVerifyingTaskManager(certificateVerifyingTaskManager);
  }

  private void initDocumentToSignAndAttachedFile(boolean fromIsCompany)
      throws IOException {

    File pdfFile = new File("src/test/resources/signature-gs1/AvoirEARLIMBERT.pdf");
    File pdfBackupFile = new File(
        "src/test/resources/signature-gs1/backup/AvoirEARLIMBERT.pdf");
    File xmlFile = new File(
        "src/test/resources/signature-gs1/AvoirEARLIMBERT.xml");
    File xmlBackupFile = new File(
        "src/test/resources/signature-gs1/backup/AvoirEARLIMBERT.xml");
    File xcblFile = new File("src/test/resources/signature-gs1/AvoirEARLIMBERT-xcbl.xml");
    File xcblBackupFile = new File("src/test/resources/signature-gs1/backup/AvoirEARLIMBERT-xcbl.xml");
    File xmlgs1File = new File("src/test/resources/signature-gs1/AvoirEARLIMBERT-xml-gs1.xml");
    File xmlgs1FileBackup = new File(
        "src/test/resources/signature-gs1/backup/AvoirEARLIMBERT-xml-gs1.xml");

    Files.copy(pdfBackupFile.toPath(), pdfFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
    Files.copy(xmlBackupFile.toPath(), xmlFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
    Files.copy(xcblBackupFile.toPath(), xcblFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
    Files.copy(xmlgs1FileBackup.toPath(), xmlgs1File.toPath(), StandardCopyOption.REPLACE_EXISTING);

    docToSign = new Document("doc_to_sign", "IDG",
        PDF_NORM, fromIsCompany ? company.getCode() : from.getCode(), to.getCode(), DocumentStatus.PENDING);
    docToSign.setArchiveStatus(ArchiveStatus.UNDEFINED);
    docToSign.setStage(DocumentStage.CORRECT);

    DocumentFile pdfDocFile = new DocumentFile();
    pdfDocFile.setFile(pdfFile);
    pdfDocFile.setType(FileType.PDF);
    docToSign.addFile(pdfDocFile);

    DocumentFile xmlDocFile = new DocumentFile();
    xmlDocFile.setFile(xmlFile);
    xmlDocFile.setType(FileType.XML);
    docToSign.addFile(xmlDocFile);

    DocumentFile xcblDocFile = new DocumentFile();
    xcblDocFile.setFile(xcblFile);
    xcblDocFile.setType(FileType.XCBL);
    xcblDocFile.setDescription("official_index");
    docToSign.addFile(xcblDocFile);

    DocumentFile xmlgs1DocFile = new DocumentFile();
    xmlgs1DocFile.setFile(xmlgs1File);
    xmlgs1DocFile.setType(FileType.XML);
    xmlgs1DocFile.setComment("XML/GS1");
    docToSign.addFile(xmlgs1DocFile);

    documentService.saveDocument(docToSign);
  }

  private void createCertificateAndAssociatedDocFile() {
    certificate = new Document();
    certificate.setParent(keyStoreDoc);
    keyStoreDoc.addChild(certificate);
    certificate.setLinCount(1);
    certificate.setReference(FRIENDLY_NAME);
    certificate.setOwners(KEYSTORE_OWNERS);
    certificate.setType(KeyStoreType.PKCS11.name());
    certificate.setStatusWithEnumValue(DocumentStatus.NONE);

    // some specific values for PKCS11
    certificate.addModel(PKCS11_KEYSTORE_FRIENDLY_NAME, PropertyType.String, null, FRIENDLY_NAME);
    certificate.addModel(PKCS11_SLOT_INDEX, PropertyType.String, null, "0");
    certificate.addModel(PKCS11_LIBRARY, PropertyType.String, null, library.getAbsolutePath());
    certificate.addModel(PKCS11_CERTIFICATE_PASSWORD, String, null, AESEncryptionHelper.encrypt(KEYSTORE_PASSWORD));

    certificate.addModel(KEYSTORE_ENTRY_ALIAS, PropertyType.String, null, ALIAS);
    certificate.addModel(KEYSTORE_USAGE, PropertyType.Enum, null, UsageType.SIGNATURE);
    certificate.addModel(KEYSTORE_ENTRY_SUBJECT_COMMON_NAME, PropertyType.String, null, "CN_HSM");

    Calendar cal = Calendar.getInstance();
    Date today = cal.getTime();
    cal.add(Calendar.YEAR, 1);
    Date nextYear = cal.getTime();

    certificate.addModel(KEYSTORE_ENTRY_NOT_BEFORE, Date, KEYSTORE_DATE_FORMAT, today);
    certificate.addModel(KEYSTORE_ENTRY_NOT_AFTER, Date, KEYSTORE_DATE_FORMAT, nextYear);
    certificate.addModel(KEYSTORE_ENTRY_WITH_PRIVATE_KEY, Boolean, null, true);
    certificate.addModel(KEYSTORE_ENTRY_RSA_PUBLIC_KEY, Boolean, null, true);
    certificate.addModel(KEYSTORE_ENTRY_PUBLIC_KEY_MODULUS_LENGTH, String, null, "2048");

    com.byzaneo.xtrade.bean.DocumentFile keystoreDocFile = new DocumentFile();
    keystoreDocFile.setActionName(AESEncryptionHelper.encrypt(KEYSTORE_PASSWORD));
    keystoreDocFile.setUri(NO_URI);
    certificate.addFile(keystoreDocFile);
    documentService.saveDocument(certificate);
    documentService.saveDocument(keyStoreFolder);
    documentService.saveDocument(keyStoreDoc);
  }

  private void createKeyStoreDoc() {
    keyStoreDoc = new Document();
    keyStoreDoc.setReference(FRIENDLY_NAME);
    keyStoreDoc.setOwners(KEYSTORE_OWNERS);
    keyStoreDoc.setType(CERTIFICATE_FAMILY_PARENT_TYPE);
    keyStoreDoc.addModel(KEYSTORE_USAGE, Enum, null, UsageType.SIGNATURE);
    keyStoreDoc.setParent(keyStoreFolder);
    keyStoreFolder.addChild(keyStoreDoc);
    documentService.saveDocument(keyStoreFolder);
    documentService.saveDocument(keyStoreDoc);
  }

  private void createKeyStoreFolder(Group keyStoreOwner) {
    keyStoreFolder = new Document();
    keyStoreFolder.setReference(KeyStoreService.DOCUMENT_SSL_ROOT_REFERENCE);
    keyStoreFolder.setStage(DocumentStage.UNDEFINED);
    keyStoreFolder.setStatusWithEnumValue(DocumentStatus.NONE);
    keyStoreFolder.setArchiveStatus(ArchiveStatus.UNDEFINED);
    keyStoreFolder.setType(DocumentType.FOLDER);
    keyStoreFolder.setOwners(keyStoreOwner.getId());
  }

  private void initCompanyAndPartners() {
    company = new Company("Generix group");
    company.setFullname("Generix group");
    company.setCode("IDG");

    from = new Partner("FROM-3014531200102");
    from.setCode("3014531200102");
    from.setParent(company);

    to = new Partner("TO-3026990099739");
    to.setCode("3026990099739");
    to.setParent(company);

    company.setChildren(Stream.of(from, to)
        .collect(Collectors.toSet()));

    company = securityService.saveGroup(company);
    from = securityService.saveGroup(from);
    to = securityService.saveGroup(to);
  }

}
