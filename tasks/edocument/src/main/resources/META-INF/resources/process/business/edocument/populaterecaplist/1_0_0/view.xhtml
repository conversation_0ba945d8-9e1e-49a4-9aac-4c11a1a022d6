<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:cc="http://xmlns.jcp.org/jsf/composite"
	xmlns:p="http://primefaces.org/ui"
	xmlns:tsk="http://xmlns.jcp.org/jsf/composite/components/task"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets">

	<!-- INTERFACE -->
	<cc:interface name="populateRecapListEdit" />

	<!-- IMPLEMENATION -->
	<cc:implementation>
		<ui:param name="taskViewModel"
			value="#{cc.attrs.type.getViewModel(cc.attrs.mode)}" />
        <p:outputPanel styleClass="form-group">
            <p:outputLabel styleClass="col-sm-3 control-label"  value="#{tsksiglbls.disabled}" />
            <p:outputPanel styleClass="col-sm-8">
                <p:selectBooleanCheckbox value="#{cc.attrs.value.disabled}" disabled="true" styleClass="form-control" />
            </p:outputPanel>
		</p:outputPanel>

             
		<tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
			mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
			multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
			locales="#{cc.attrs.locales}"
			readonly="true"
			defaultLocale="#{cc.attrs.defaultLocale}"
			labelProvider="#{cc.attrs.labelProvider}"
			property="#{taskViewModel.getGroup('default')['recapListSavingRule']}" />

		<p:outputPanel styleClass="form-group">
			<h:outputText styleClass=" col-sm-3"/>
			<h:outputText value="#{edctsklbls.warning_from_to}" styleClass="alert alert-warning col-sm-8"/>
		</p:outputPanel>

		<tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
				   mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
				   multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
				   locales="#{cc.attrs.locales}"
				   readonly="true"
				   defaultLocale="#{cc.attrs.defaultLocale}"
				   labelProvider="#{cc.attrs.labelProvider}"
				   property="#{taskViewModel.getGroup('default')['from']}" />

		<tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
				   mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
				   multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
				   locales="#{cc.attrs.locales}"
				   readonly="true"
				   defaultLocale="#{cc.attrs.defaultLocale}"
				   labelProvider="#{cc.attrs.labelProvider}"
				   property="#{taskViewModel.getGroup('default')['to']}" />
	</cc:implementation>
</ui:component>
