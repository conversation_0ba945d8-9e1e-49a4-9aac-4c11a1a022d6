<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml" 
    xmlns:f="http://xmlns.jcp.org/jsf/core" xmlns:h="http://xmlns.jcp.org/jsf/html" 
    xmlns:cc="http://xmlns.jcp.org/jsf/composite" xmlns:p="http://primefaces.org/ui"
    xmlns:tsk="http://xmlns.jcp.org/jsf/composite/components/task"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets">

    <!-- INTERFACE -->
    <cc:interface name="productsEdit">
        <cc:attribute name="value" required="true" />
        <cc:attribute name="type" required="true" />
        <cc:attribute name="mode" default="edit" />
    </cc:interface>

    <!-- IMPLEMENATION -->
    <cc:implementation>
        <p:outputPanel styleClass="psProductsEditPnl">
        </p:outputPanel>
    </cc:implementation>
</ui:component>