<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:gnxf="http://generix.com/ui/functions">

	<ui:param name="cell" value="#{step.getColumnValue(doc, property)}"/>
    <p:column field="#{property.name}"
              sortBy="#{cell.value}" sortable="#{property.sortable}"
              style="#{property.style}" styleClass="idx-column #{step.getColumnStyleClass(property)} center"
              rendered="#{property.rendered}">
        <f:facet name="header">
            <h:outputText value="#{property.styleClass eq 'iconColumn' ? '' : gnxHandler.label(property.label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}"
                          title="#{gnxHandler.label(property.label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}"/>
        </f:facet>
            <h:outputText rendered="#{property.name ne 'specialIndicator' and property.styleClass ne 'iconColumn'}"
                          value="#{cell.value}"
                          title="#{cell.value}"
                          converter="#{cell.converter}"
                          styleClass="#{cell.style}"/>
            <h:graphicImage value="#{step.getImageUrl(doc)}" styleClass="img-#{article.gencode}" style="height: 50px;"
            	onmousemove="zoomInWithImage(event, '#{doc.imageURL}')" onmouseout="zoomOut()" rendered="#{property.styleClass eq 'iconColumn'}"/>
            <h:graphicImage value="#{cell.value}" rendered="#{property.name eq 'specialIndicator'}" style="height: 50px;"/>        
    </p:column>
</ui:composition>
