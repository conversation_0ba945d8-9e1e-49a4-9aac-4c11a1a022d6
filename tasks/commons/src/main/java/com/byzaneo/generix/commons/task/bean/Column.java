package com.byzaneo.generix.commons.task.bean;

import static java.lang.String.format;

/**
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Apr 10, 2013
 * @since 2.3 GNX-394
 * @deprecated since 2.5.3 use {@link com.byzaneo.commons.bean.PropertyDescriptor} instead
 */
@Deprecated
public class Column {
  private String key;

  private String header;

  private String footer;

  private String style;

  private String styleClass;

  private boolean resizable;

  private boolean exportable = true;

  private boolean rendered = true;

  public Column() {
    super();
  }

  public Column(String key, String header, boolean rendered) {
    this();
    this.key = key;
    this.header = header;
    this.rendered = rendered;
  }

  public String getKey() {
    return key;
  }

  public void setKey(String key) {
    this.key = key;
  }

  public String getHeader() {
    return header;
  }

  public void setHeader(String header) {
    this.header = header;
  }

  public String getFooter() {
    return footer;
  }

  public void setFooter(String footer) {
    this.footer = footer;
  }

  public String getStyle() {
    return style;
  }

  public void setStyle(String style) {
    this.style = style;
  }

  public String getStyleClass() {
    return styleClass;
  }

  public void setStyleClass(String styleClass) {
    this.styleClass = styleClass;
  }

  public boolean isResizable() {
    return resizable;
  }

  public void setResizable(boolean resizable) {
    this.resizable = resizable;
  }

  public boolean isExportable() {
    return exportable;
  }

  public void setExportable(boolean exportable) {
    this.exportable = exportable;
  }

  public boolean isRendered() {
    return rendered;
  }

  public void setRendered(boolean rendered) {
    this.rendered = rendered;
  }

  @Override
  public String toString() {
    return format("Column@%s [%s]", key, rendered);
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + ((key == null) ? 0 : key.hashCode());
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj)
      return true;
    if (obj == null)
      return false;
    if (getClass() != obj.getClass())
      return false;
    Column other = (Column) obj;
    if (key == null) {
      if (other.key != null)
        return false;
    }
    else if (!key.equals(other.key))
      return false;
    return true;
  }

}
