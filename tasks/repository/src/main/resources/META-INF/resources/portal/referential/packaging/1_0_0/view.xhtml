<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml" xmlns:f="http://xmlns.jcp.org/jsf/core"
              xmlns:h="http://xmlns.jcp.org/jsf/html"
              xmlns:cc="http://xmlns.jcp.org/jsf/composite" xmlns:p="http://primefaces.org/ui"
              xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
              xmlns:gnx="http://webui.generix.com/jsf">

    <cc:interface name="packagingView" />

    <cc:implementation>
        #{cc.attrs.value.setLanguage(gnxSessionHandler.locale.toString())}
        <ui:param name="taskBean" value="#{cc.attrs.value}"/>
        <ui:param name="taskPropertiesPackage" value="#{cc.attrs.value.getDescriptorPackage().getProperties()}"/>
        <ui:param name="taskPropertiesPallet" value="#{cc.attrs.value.getDescriptorPallet().getProperties()}"/>
        <p:outputPanel id="packViewPnl" styleClass="packPsDocumentPnl portletContentMargin">
            <!-- * LIST *   -->
            <p:outputPanel styleClass="psTaskList" rendered="#{cc.attrs.value.indexablePackage == null}">
            <h:outputText value="${gnxrefpacklbls.header_table_package}" style="font-weight:bold;font-size:large;"/>

                <p:dataTable id="PackPackTable"
                             value="#{cc.attrs.value.dataModelPackage}"
                             rows="15"
                             lazy="true"
                             var="doc"
                             selection="#{cc.attrs.value.multiSelectedPackage}"
                             scrollable="#{not cc.attrs.value.responsive}"
                             paginator="true"
                             paginatorPosition="bottom"
                             paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             currentPageReportTemplate="{startRecord}-{endRecord}/{totalRecords}"
                             rowsPerPageTemplate="15,50,100,200,500"
                             rowKey="#{doc.id}"
                             styleClass="datatable-hide-filters"
                             resizableColumns="true"
                             widgetVar="wResultTable"
                             emptyMessage="#{labels.no_records_found}"
                             sortBy="#{cc.attrs.value.sortByPackage}"
                             sortField="#{cc.attrs.value.sortByPackage}"
                             sortOrder="#{cc.attrs.value.sortAscPackage ? 'ascending' : 'descending'}">

                    <f:facet name="header">
                        <p:outputPanel styleClass="left">
                            <p:commandButton icon="fa fa-plus" value="#{labels.add}"
                                             action="#{cc.attrs.value.onAddNewPackage()}"
                                             process="@this" update="@(.packPsDocumentPnl)"
                                             rendered="#{not cc.attrs.value.isPartnerUser()}"
                                             style="margin-bottom:5px;"/>
                        </p:outputPanel>
                    </f:facet>

                    <gnx:pcolumn property="#{taskPropertiesPackage.get(0)}"/>
                    <gnx:pcolumn property="#{taskPropertiesPackage.get(1)}"/>
                    <gnx:pcolumn property="#{taskPropertiesPackage.get(2)}"/>
                    <gnx:pcolumn property="#{taskPropertiesPackage.get(3)}"/>
                    <gnx:pcolumn property="#{taskPropertiesPackage.get(4)}"/>
                    <gnx:pcolumn property="#{taskPropertiesPackage.get(5)}"/>
                    <gnx:pcolumn property="#{taskPropertiesPackage.get(6)}"/>
                    <gnx:pcolumn property="#{taskPropertiesPackage.get(7)}"/>
                    <gnx:pcolumn property="#{taskPropertiesPackage.get(8)}"/>

                    <p:column headerText="#{labels.actions}" width="100px;" rendered="#{not cc.attrs.value.isPartnerUser()}">
                        <p:commandButton process="@this"
                                         ignoreAutoUpdate="true"
                                         update="@(.packPsDocumentPnl)"
                                         title="#{labels.button_edit}"
                                         icon="ui-icon ui-icon-pencil"
                                         actionListener="#{cc.attrs.value.setOldPackageCode(doc)}">
                            <f:setPropertyActionListener value="#{doc}"
                                                         target="#{cc.attrs.value.indexablePackage}"/>
                        </p:commandButton>
                        <p:commandButton actionListener="#{cc.attrs.value.onRemovePackage(doc)}" ignoreAutoUpdate="true"
                                         update="@(.packPsDocumentPnl)" icon="ui-icon ui-icon-trash"
                                         title="#{labels.delete}">
                            <p:confirm header="#{comlbls.confirm}" message="#{gnxxcblreflbls.msg_confirmation_delete}"
                                       icon="ui-icon-alert"/>
                        </p:commandButton>
                    </p:column>
                </p:dataTable>
            </p:outputPanel>

            <ui:include src="edit-package.xhtml"/>
		</p:outputPanel>
		<p:outputPanel id="pallViewPnl" styleClass="pallPsDocumentPnl">
			<p:spacer height="10" />

			<p:outputPanel styleClass="psTaskList" rendered="#{cc.attrs.value.indexablePallet == null}">
				<h:outputText value="${gnxrefpacklbls.header_table_pallet}" style="font-weight:bold;font-size:large;"/>

                <p:dataTable id="PackPallTable"
                             value="#{cc.attrs.value.dataModelPallet}"
                             rows="15"
                             lazy="true"
                             var="doc"
                             selection="#{cc.attrs.value.multiSelectedPallet}"
                             scrollable="#{not cc.attrs.value.responsive}"
                             paginator="true"
                             paginatorPosition="bottom"
                             paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             currentPageReportTemplate="{startRecord}-{endRecord}/{totalRecords}"
                             rowsPerPageTemplate="15,50,100,200,500"
                             rowKey="#{doc.id}"
                             styleClass="datatable-hide-filters"
                             resizableColumns="true"
                             widgetVar="wResultTable"
                             emptyMessage="#{labels.no_records_found}"
                             sortBy="#{cc.attrs.value.sortByPallet}"
                             sortField="#{cc.attrs.value.sortByPallet}"
                             sortOrder="#{cc.attrs.value.sortAscPallet ? 'ascending' : 'descending'}">

                    <f:facet name="header">
                        <p:outputPanel styleClass="left">
                            <p:commandButton icon="fa fa-plus" value="#{labels.add}"
                                             action="#{cc.attrs.value.onAddNewPallet()}"
                                             process="@this" update="@(.pallPsDocumentPnl)"
                                             rendered="#{not cc.attrs.value.isPartnerUser()}"
                                             style="margin-bottom:5px;"/>
                        </p:outputPanel>
                    </f:facet>

                    <gnx:pcolumn property="#{taskPropertiesPallet.get(0)}"/>
                    <gnx:pcolumn property="#{taskPropertiesPallet.get(1)}"/>
                    <gnx:pcolumn property="#{taskPropertiesPallet.get(2)}"/>
                    <gnx:pcolumn property="#{taskPropertiesPallet.get(3)}"/>

                    <p:column headerText="#{labels.actions}" width="100px;" rendered="#{not cc.attrs.value.isPartnerUser()}">
                        <p:commandButton process="@this"
                                         ignoreAutoUpdate="true"
                                         update="@(.pallPsDocumentPnl)"
                                         title="#{labels.button_edit}"
                                         icon="ui-icon ui-icon-pencil"
                                         actionListener="#{cc.attrs.value.setOldPalletCode(doc)}">
                            <f:setPropertyActionListener value="#{doc}"
                                                         target="#{cc.attrs.value.indexablePallet}"/>
                        </p:commandButton>
                        <p:commandButton actionListener="#{cc.attrs.value.onRemovePallet(doc)}" ignoreAutoUpdate="true"
                                         update="@(.pallPsDocumentPnl)" icon="ui-icon ui-icon-trash"
                                         title="#{labels.delete}">
                            <p:confirm header="#{comlbls.confirm}" message="#{gnxxcblreflbls.msg_confirmation_delete}"
                                       icon="ui-icon-alert"/>
                        </p:commandButton>
                    </p:column>
                </p:dataTable>
            </p:outputPanel>

            <ui:include src="edit-pallet.xhtml"/>

            <p:confirmDialog global="true">
                <p:commandButton value="#{labels.yes}"
                                 type="button"
                                 styleClass="ui-confirmdialog-yes"
                                 icon="ui-icon-check"/>
                <p:commandButton value="#{labels.no}"
                                 type="button"
                                 styleClass="ui-confirmdialog-no"
                                 icon="ui-icon-close"/>
            </p:confirmDialog>
        </p:outputPanel>
    </cc:implementation>
</ui:component>
