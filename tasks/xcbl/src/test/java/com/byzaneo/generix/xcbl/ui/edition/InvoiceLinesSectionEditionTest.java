package com.byzaneo.generix.xcbl.ui.edition;

import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.buildInvoiceItemDetail;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.getCalculationNet;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.setCalculationGross;
import static com.byzaneo.generix.edocument.util.InvoiceTaxXcblHelper.createInvoiceTaxtype;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.updateInvoicePrices;
import static com.byzaneo.generix.edocument.util.UnitOfMeasurement.UOM_EA;
import static com.byzaneo.generix.xcbl.portal.task.util.InvoiceEditionXcblHelper.initNewInvoice;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getInvoiceTotalFromInvoiceSummary;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getTaxableValueFromInvoiceSummary;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getTotalTaxAmountFromInvoiceSummary;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.toComplexBigDecimalType;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.BasisCodeType.MONETARY_AMOUNT;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.BasisCodeType.PERCENT;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.IndicatorCodeType.LINE_ITEM_ALLOWANCE;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.IndicatorCodeType.LINE_ITEM_CHARGE;
import static java.math.BigDecimal.valueOf;
import static java.math.RoundingMode.HALF_EVEN;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.byzaneo.generix.edocument.util.InvoiceAllowOrChargeXcblHelper;
import com.byzaneo.generix.xcbl.ui.edition.invoice.InvoiceLinesSectionEdition;
import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.InvoiceAllowOrChargeType;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceItemDetailType;
import java.math.BigDecimal;
import java.util.Locale;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class InvoiceLinesSectionEditionTest {

  Invoice invoice;

  InvoiceLinesSectionEdition section;

  @BeforeEach
  public void before() {
    this.invoice = new Invoice();
    initNewInvoice(this.invoice, null, Locale.FRANCE);
    this.section = new InvoiceLinesSectionEdition();
    this.section.document(this.invoice);
    this.section.context(new DocumentEditionContext());
  }

  @Test
  public void onSaveItemTest() {
    // -- ADD PRODUCT --
    this.section.setItem(createInvoiceItemDetail(valueOf(2), valueOf(12.50), valueOf(19.60), valueOf(30), valueOf(10)));
    this.section.getItems()
        .add(this.section.getItem());
    // Product
    this.section.compute(this.section.getItem());
    assertEquals(valueOf(18.750000).setScale(6, HALF_EVEN), getCalculationNet(this.section.getItem()).orElse(BigDecimal.ZERO));
    assertEquals(valueOf(37.50).setScale(2, HALF_EVEN), this.section.getItem()
        .getInvoicePricingDetail()
        .getTax()
        .get(0)
        .getTaxableAmount());
    assertEquals(valueOf(7.35).setScale(2, HALF_EVEN), this.section.getItem()
        .getInvoicePricingDetail()
        .getTax()
        .get(0)
        .getTaxAmount()
        .getValue());
    assertEquals(valueOf(37.50).setScale(2, HALF_EVEN), this.section.getItem()
        .getInvoicePricingDetail()
        .getLineItemSubTotal()
        .getMonetaryAmount()
        .getValue());
    assertEquals(valueOf(44.85).setScale(2, HALF_EVEN), this.section.getItem()
        .getInvoicePricingDetail()
        .getLineItemTotal()
        .getMonetaryAmount()
        .getValue());
    // Invoice Total
    updateInvoicePrices(this.section.getDocument(), this.section.getItems(), null, 2);
    assertEquals(valueOf(37.50).setScale(2, HALF_EVEN), getTaxableValueFromInvoiceSummary(this.section.<Invoice> getDocument()));
    assertEquals(valueOf(7.35).setScale(2, HALF_EVEN), getTotalTaxAmountFromInvoiceSummary(this.section.<Invoice> getDocument()));
    assertEquals(valueOf(37.50).setScale(2, HALF_EVEN), this.section.<Invoice> getDocument()
        .getInvoiceSummary()
        .getInvoiceTotals()
        .getInvoiceSubTotal()
        .getMonetaryAmount()
        .getValue());
    assertEquals(valueOf(44.85).setScale(2, HALF_EVEN), getInvoiceTotalFromInvoiceSummary(this.section.<Invoice> getDocument()));
    // -- ADD PRODUCT --
    this.section.setItem(createInvoiceItemDetail(valueOf(4), valueOf(15), valueOf(5.5), valueOf(0), valueOf(0)));
    this.section.getItems()
        .add(this.section.getItem());
    // Product
    this.section.compute(this.section.getItem());
    assertEquals(valueOf(15.000000).setScale(6, HALF_EVEN), getCalculationNet(this.section.getItem()).orElse(BigDecimal.ZERO));
    assertEquals(valueOf(60.00).setScale(2, HALF_EVEN), this.section.getItem()
        .getInvoicePricingDetail()
        .getTax()
        .get(0)
        .getTaxableAmount());
    assertEquals(valueOf(3.30).setScale(2, HALF_EVEN), this.section.getItem()
        .getInvoicePricingDetail()
        .getTax()
        .get(0)
        .getTaxAmount()
        .getValue());
    assertEquals(valueOf(60.00).setScale(2, HALF_EVEN), this.section.getItem()
        .getInvoicePricingDetail()
        .getLineItemSubTotal()
        .getMonetaryAmount()
        .getValue());
    assertEquals(valueOf(63.30).setScale(2, HALF_EVEN), this.section.getItem()
        .getInvoicePricingDetail()
        .getLineItemTotal()
        .getMonetaryAmount()
        .getValue());
    // Invoice Total
    updateInvoicePrices(this.section.getDocument(), this.section.getItems(), null, 2);
    assertEquals(valueOf(97.50).setScale(2, HALF_EVEN), getTaxableValueFromInvoiceSummary(this.section.<Invoice> getDocument()));
    assertEquals(valueOf(10.65).setScale(2, HALF_EVEN), getTotalTaxAmountFromInvoiceSummary(this.section.<Invoice> getDocument()));
    assertEquals(valueOf(97.5).setScale(2, HALF_EVEN), this.section.<Invoice> getDocument()
        .getInvoiceSummary()
        .getInvoiceTotals()
        .getInvoiceSubTotal()
        .getMonetaryAmount()
        .getValue());
    assertEquals(valueOf(108.15).setScale(2, HALF_EVEN), getInvoiceTotalFromInvoiceSummary(this.section.<Invoice> getDocument()));

  }

  private InvoiceItemDetailType createInvoiceItemDetail(BigDecimal qty, BigDecimal grossPrice, BigDecimal tax, BigDecimal allowance,
      BigDecimal charge) {
    InvoiceItemDetailType detail = buildInvoiceItemDetail();
    // -- PRODUCT --
    // Quantity
    detail.getInvoiceBaseItemDetail()
        .getInvoicedQuantity()
        .getQuantityValue()
        .setValue(qty);
    // UOM
    detail.getInvoiceBaseItemDetail()
        .getInvoicedQuantity()
        .getUnitOfMeasurement()
        .setUOMCoded(UOM_EA.getCode());
    // Gross Price
    setCalculationGross(detail, grossPrice, 6, HALF_EVEN);
    // Tax
    detail.getInvoicePricingDetail()
        .getTax()
        .add(createInvoiceTaxtype());
    detail.getInvoicePricingDetail()
        .getTax()
        .get(0)
        .getTaxPercent()
        .setValue(tax);
    // -- ALLOWANCE OR CHARGE --
    InvoiceAllowOrChargeType allow = InvoiceAllowOrChargeXcblHelper.buildInvoiceAllowOrCharge();
    InvoiceAllowOrChargeType chrge = InvoiceAllowOrChargeXcblHelper.buildInvoiceAllowOrCharge();
    // Allowance
    allow.setIndicatorCoded(LINE_ITEM_ALLOWANCE);
    allow.setBasisCoded(PERCENT);
    allow.getTypeOfAllowanceOrCharge()
        .getPercentageAllowanceOrCharge()
        .getPercent()
        .setValue(allowance);
    // Charge
    chrge.setIndicatorCoded(LINE_ITEM_CHARGE);
    chrge.setBasisCoded(MONETARY_AMOUNT);
    chrge.getTypeOfAllowanceOrCharge()
        .getMonetaryValue()
        .setMonetaryAmount(toComplexBigDecimalType(chrge.getTypeOfAllowanceOrCharge()
            .getMonetaryValue()
            .getMonetaryAmount(), charge));

    detail.getInvoicePricingDetail()
        .getItemAllowancesOrCharges()
        .getAllowOrCharge()
        .add(allow);
    detail.getInvoicePricingDetail()
        .getItemAllowancesOrCharges()
        .getAllowOrCharge()
        .add(chrge);
    return detail;
  }
}
