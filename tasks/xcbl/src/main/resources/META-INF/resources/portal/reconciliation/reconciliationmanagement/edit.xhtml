<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml"
              xmlns:cc="http://xmlns.jcp.org/jsf/composite"
              xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
              xmlns:g="http://xmlns.jcp.org/jsf/composite/components/gnx">

    <cc:interface name="reconciliationEdit">
        <cc:attribute name="value" type="com.byzaneo.generix.xcbl.portal.task.ReconciliationManagementTask" required="true"/>
    </cc:interface>

    <cc:implementation>
        #{cc.attrs.value.setLanguage(cc.attrs.locale)}
        <g:editBqlInstructions value="#{cc.attrs.value}" locales="#{cc.attrs.locales}" localeSelected="#{cc.attrs.locale}" defaultLocale="#{cc.attrs.defaultLocale}"/>
    </cc:implementation>
</ui:component>