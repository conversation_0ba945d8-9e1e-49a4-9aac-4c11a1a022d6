package com.byzaneo.generix.xcbl.portal.task;

import com.byzaneo.angular.bean.BqlFilter;
import com.byzaneo.angular.service.FilterService;
import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.dao.mongo.PageRequest;
import com.byzaneo.commons.ui.util.MessageHelper;
import com.byzaneo.faces.event.CompleteEvent;
import com.byzaneo.generix.bean.Instance;
import com.byzaneo.generix.edocument.bean.*;
import com.byzaneo.generix.edocument.listener.FieldEventEntry;
import com.byzaneo.generix.edocument.service.EDocumentService.EDocument;
import com.byzaneo.generix.edocument.util.UnitOfMeasurement;
import com.byzaneo.generix.service.TransformService.BirtOutoutFileType;
import com.byzaneo.generix.ui.ApplicationHandler.EDateFormat;
import com.byzaneo.generix.ui.*;
import com.byzaneo.generix.ui.event.data.UploadData;
import com.byzaneo.generix.ui.instance.*;
import com.byzaneo.generix.util.OrganizationHelper;
import com.byzaneo.generix.xtrade.task.*;
import com.byzaneo.generix.xtrade.util.BirtFile;
import com.byzaneo.portal.bean.Portal;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.QueryBuilder;
import com.byzaneo.security.bean.*;
import com.byzaneo.security.service.OrderingPartyService;
import com.byzaneo.task.api.TaskType;
import com.byzaneo.task.bean.TaskDefinition;
import com.byzaneo.task.dao.TaskDefinitionDAO;
import com.byzaneo.task.util.TaskHelper;
import com.byzaneo.xtrade.api.Indexable;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.ipm.bean.Deployment;
import com.byzaneo.xtrade.ipm.service.ProjectService;
import com.byzaneo.xtrade.service.DocumentService;
import com.byzaneo.xtrade.ui.*;
import com.byzaneo.xtrade.util.DocumentHelper;
import com.byzaneo.xtrade.xcbl.api.XcblDocument;
import com.byzaneo.xtrade.xcbl.bean.InvoiceIndex;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.mongodb.DBObject;
import javassist.util.proxy.Proxy;
import org.apache.commons.lang3.*;
import org.primefaces.context.RequestContext;
import org.primefaces.event.*;
import org.primefaces.model.*;
import org.slf4j.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.data.domain.*;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;

import javax.faces.model.SelectItem;
import javax.persistence.*;
import java.io.*;
import java.text.*;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.*;

import static com.byzaneo.commons.bean.FileType.CSV;
import static com.byzaneo.commons.ui.util.EmptyPersistentDataModel.emptyDataModel;
import static com.byzaneo.commons.ui.util.JSFHelper.getLabel;
import static com.byzaneo.commons.ui.util.JSFHelper.getManagedBean;
import static com.byzaneo.commons.ui.util.MessageHelper.error;
import static com.byzaneo.commons.ui.util.MessageHelper.getMessage;
import static com.byzaneo.commons.ui.util.MessageHelper.warn;
import static com.byzaneo.generix.ui.FileUploadDialogHandler.openFileUploadDialog;
import static com.byzaneo.generix.util.OrganizationHelper.resolveUserOrganizations;
import static com.byzaneo.generix.xtrade.util.IndexableTaskHelper.getFontAwesomeIcon;
import static com.byzaneo.portal.util.PortalHelper.findFirstPageByPortletContentId;
import static com.byzaneo.portal.util.PortalHelper.findPage;
import static com.byzaneo.query.builder.Clauses.ilike;
import static com.byzaneo.query.builder.QueryBuilder.createBuilder;
import static com.byzaneo.task.util.TaskHelper.error;
import static com.byzaneo.task.util.TaskHelper.getDefinition;
import static com.byzaneo.xtrade.process.Variable.DOCUMENTS;
import static com.byzaneo.xtrade.process.Variable.WRITE_ASYNC;
import static java.lang.String.valueOf;
import static java.util.Collections.emptyList;
import static java.util.Collections.singletonList;
import static java.util.Comparator.comparing;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.lang3.ArrayUtils.contains;
import static org.apache.commons.lang3.LocaleUtils.toLocale;
import static org.apache.commons.lang3.StringUtils.EMPTY;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.primefaces.context.RequestContext.getCurrentInstance;

/**
 * Allows to display any XCBL documents.
 *
 * @param <T> type of XcblDocument handled by this task.
 * <AUTHOR> Aboulaye <<EMAIL>>
 * @company Generix group
 * @date Nov 12, 2014
 * @since 3.0 GNX-1631
 */

public abstract class AbstractXcblDocumentTask<T extends XcblDocument> extends AbstractIndexableDocumentTask<T> {
  private static final long serialVersionUID = -460859390454156507L;

  protected static final Logger log = LoggerFactory.getLogger(AbstractIndexableTask.class);

  private static final String STANDARD_DIALOG = "standardUploadDialog";

  private static final String GENERIX_CUSTOMER_DIALOG = "generixCustomerUploadDialog";

  // DAO
  @Autowired
  @Qualifier(TaskDefinitionDAO.DAO_NAME)
  protected transient TaskDefinitionDAO taskDefinitionDAO;

  @Autowired
  @Qualifier(FilterService.BEAN_NAME)
  protected transient FilterService filterService;

  // PROPERTIES
  protected transient PortalRequestParameterHandler portalRequestParameterHandler;

  // action
  protected transient PortalSessionHandler portalSessionHandler;

  protected transient PortalISHandler portalISHandler;

  protected transient TaskDefinition taskDefinition;

  protected transient Long actionId;

  protected transient TaskType actionType;

  protected transient Logs logs;

  // restitution
  protected transient List<String> availableStatus;

  protected transient List<UploadedFile> uploadedFiles = new ArrayList<>();

  protected transient boolean allowOrChrgLineEdit;

  protected transient static Map<String, String> allUOMS;

  // display files
  private transient DocumentFile addedDocumentFileToDelete;

  protected transient List<DocumentFile> files;

  // history
  protected transient List<FieldEventEntry> fieldHistory;

  // - VIEW -

  protected transient DocumentFileViewer download;

  @Autowired
  @Qualifier(OrderingPartyService.SERVICE_NAME)
  protected transient OrderingPartyService orderingPartyService;
  
  @Autowired
  @Qualifier(ProjectService.SERVICE_NAME)
  protected transient ProjectService projectService;

  // row expansion
  // we need these maps to be synchronized because the tables are lazy loaded and there are 2 threads accessing this
  // when isRowExpanded is called
  private transient Map<String, Boolean> rowExpansionMap = Collections.synchronizedMap(new HashMap<>());

  private transient Map<String, DatabaseDocumentFileDataModel> rowExpansionDataModels = Collections.synchronizedMap(new HashMap<>());

  private transient BeanDescriptor documentFileDescriptor;

  protected boolean orderingParty;

  /*
   * -- LIFE CYCLE --
   */

  @PrePersist
  @PreUpdate
  public void prePersist() {
    indexProperties(descriptor, false);
  }

  @PreRemove
  public void preRemove() {

    if (!doCascadeDeleteChildren()) {
      if (this instanceof Proxy) {
        taskDefinition = TaskHelper.getDefinition(this);
      }
      if (taskDefinition != null) {
        taskDefinition = taskDefinitionDAO.findById(taskDefinition.getId());
        List<BqlFilter> filters = this.filterService.getByPortletId(taskDefinition.getId());
        if(filters != null && !filters.isEmpty()) {
          this.filterService.deleteFilters(filters);
        }
        List<TaskDefinition> children = taskDefinition.getChildren();
        if (children != null) {
          children.stream()
              .filter(Objects::nonNull)
              .forEach(child -> child.setParent(null));
          children.clear();
        }
      }
    }
  }

  @Override
  protected String viewerBaseName(T indexable) {
    return ofNullable(EDocument.valueOf(indexable))
        .map(edoc -> edoc.getNumber(indexable))
        .orElse(super.viewerBaseName(indexable));
  }

  /**
   * return true if the children of this task have to be cascade removed if this task is removed (ex FAQ and FAQToc), false otherwise
   *
   * @return - default is false, override if needed
   */
  protected boolean doCascadeDeleteChildren() {
    return false;
  }

  protected void indexProperties(BeanDescriptor descriptor, boolean global) {
    if (!indexProperties) {
      log.info("Properties indexing disabled");
      return;
    }

    log.info("Start Creating indexes");
    int counter = 0;
    long start = System.currentTimeMillis();
    List<String> propNames = new ArrayList<>();
    for (PropertyDescriptor prop : descriptor.getProperties()) {
      if (prop.getRendered() && !prop.getSortable()) {
        prop.setSortable(true);
        String propName = prop.getName()
            .contains("#")
                ? prop.getName()
                    .substring(0, prop.getName()
                        .lastIndexOf("#"))
                : prop.getName();
        if (!propNames.contains(propName)) {
          try {
            eDocumentService.addFieldIndex(this.getIndexableType(), prop.getFieldAlias() != null ? prop.getFieldAlias() : propName, global ? prop.getLabel() : propName, Direction.ASC);
            propNames.add(propName);
            counter++;
          }
          catch (IndexOutOfBoundsException ex) {
            log.error(ex.getMessage());
          }
        }
      }
    }

    // display a user-friendly info message.
    String message = getUserFriendlyInfoMessage(counter, System.currentTimeMillis() - start);
    log.info(message);

  }

  protected String getUserFriendlyInfoMessage(int counter, long duration) {
    // index creation messages are not localized (just for logging).
    // No index added.
    // Pattern: [{0} been created, in {1} ms.]
    // [1 index has been created, in %d ms.]: "1 index has"
    // [2 indexes have been created, in %d ms.]: "{2} indexes have"
    if (counter == 0) {
      return "No index added.";
    }
    MessageFormat messageFormat = new MessageFormat("[{0} been created, in {1} ms.]");
    double[] indexLimits = { 1, 2 };
    String[] indexStrings = {
        "1 index has",
        "{2} indexes have"
    };
    ChoiceFormat choiceFormat = new ChoiceFormat(indexLimits, indexStrings);
    Format[] formats = { choiceFormat, null, NumberFormat.getInstance() };
    messageFormat.setFormats(formats);
    Object[] messageArguments = { counter, duration, counter };
    return messageFormat.format(messageArguments);
  }

  /**
   * Get status values of bean
   *
   * @param bean
   * @param label
   * @return map which contains values status
   */
  public Map<Object, String> getStatusValues(Class<T> bean, String label) {
    List<String> availablesStatuses = isOriginalStatusProperty(label) ? getAvailableStatus(bean) : Collections.emptyList();
    return getStatusesAsMapOfCodeObject(availablesStatuses);
  }

  @Override
  public String getValueStyleClass(T indexable, PropertyDescriptor property, String environmentCode) {
    return isOriginalStatusProperty(property)
        ? getFontAwesomeIcon(indexable.getStatus()
            .getStatusCode())
        : super.getValueStyleClass(indexable, property, this.getInstance()
            .getCode());
  }

  /* -- HISTORY -- */

  public void onStatusHistory(Indexable indexable) {
    if (indexable == null)
      return;
    try {
      this.setSelected(indexable);
      this.fieldHistory = new ArrayList<>(this.eDocumentService.searchStatusEvents(indexable));
      if (isEmpty(this.fieldHistory) && indexable instanceof XcblDocument) {
        final XcblDocument xdoc = (XcblDocument) indexable;
        MessageHelper.warn("gnxxcblinvlbls.history_none", EDocument.valueOf(xdoc)
            .getNumber(xdoc));
        getCurrentInstance().addCallbackParam("history", false);
        this.selected = null;
      }
      else {
        getCurrentInstance().addCallbackParam("history", true);
      }
    }
    catch (Exception e) {
      error(e, "labels.exception_message", getRootCauseMessage(e));
      getCurrentInstance().addCallbackParam("history", false);
      this.selected = null;
    }
  }

  public List<FieldEventEntry> getFieldHistory() {
    return fieldHistory;
  }

  public void setFieldHistory(List<FieldEventEntry> fieldHistory) {
    this.fieldHistory = fieldHistory;
  }

  /* -- IMPORT -- */

  public void onUploadFile(final String title, final String allowTypes, final String filterTypes) {
    FileUploadDialogConfiguration configuration = new FileUploadDialogConfiguration()
        .setTitle(title)
        .setAllowTypes(allowTypes)
        .setSizeLimit(FILE_MAX_SIZE)
        .setFilterTypes(filterTypes);
    openFileUploadDialog(configuration);
  }

  public void onUploadFile(final String title, final String allowTypes, final String filterTypes, int fileLimit) {
    FileUploadDialogConfiguration configuration = new FileUploadDialogConfiguration()
        .setTitle(title)
        .setAllowTypes(allowTypes)
        .setSizeLimit(FILE_MAX_SIZE)
        .setFileLimit(fileLimit)
        .setSingle(fileLimit == 1)
        .setEnableComment(false)
        .setFilterTypes(filterTypes);
    openFileUploadDialog(configuration);
  }

  public void onUploadFile(String title, String allowTypes, String filterTypes, int fileLimit, boolean enableComment) {
    FileUploadDialogConfiguration configuration = new FileUploadDialogConfiguration()
        .setTitle(title)
        .setAllowTypes(allowTypes)
        .setSizeLimit(FILE_MAX_SIZE)
        .setFileLimit(fileLimit)
        .setSingle(fileLimit == 1)
        .setEnableComment(enableComment)
        .setFilterTypes(filterTypes);
    openFileUploadDialog(configuration);
  }

  public Object onImportDialogReturn(SelectEvent event) {
    UploadData uploadData = (UploadData) event.getObject();
    importFiles(uploadData.getUploadedFiles());
    return null;
  }

  private void importFiles(List<UploadedFile> files) {
    if (files.isEmpty()) {
      return;
    }

    List<InputStream> list = new ArrayList<>();
    InputStream is = null;
    try {
      for (UploadedFile file : files) {

        if (checkFileCompliance(file)) {
          // File ok
          // when uploading a CSV file from a Windows system WITHOUT Microsoft Excel installed,
          // the CSV file content-type is set to application/octet-stream which is an archive type.
          // If the file extension is .csv, no need to decompress the file
          if ((isNotBlank(file.getFileName()) && !file.getFileName()
              .endsWith(CSV.getExtension())) && contains(FileType.ARCHIVE.getMimes(), file.getContentType())) {
            // Decompress the contents...
            is = new ZipInputStream(file.getInputstream());
            ((ZipInputStream) is).getNextEntry();
          }
          else {
            is = file.getInputstream();
          }
          list.add(is);
        }
      }

      if (!list.isEmpty()) {
        logs = this.importDocuments(list);
      }

    }
    catch (IOException e) {
      log.error("Exception during import", e);
    }
    finally {
      try {
        if (!list.isEmpty()) {
          list.stream()
              .close();
        }
        if (is != null) {
          is.close();
        }
      }
      catch (Exception e) {
        log.error("Exception during closing file in import", e);
      }
    }

  }

  public Collection<String> onAutocompleteStart(final CompleteEvent event) {

    String fieldName = event.getFieldName()
        .replaceAll("\"", "");
    List<String> returnVar = this.eDocumentService.aggregate(
        getIndexableType(),
        DBObject.class,
        createBuilder(resolveBaseQuery())
            .and(ilike(event.getFieldName()
                .replaceAll("\"", ""), event.getFieldValue()))
            .query(),
        createGroupPipeline(fieldName, 10).toArray(new AggregationOperation[0]))
        .stream()
        .map(dbo -> dbo.get("_id"))
        .map(Object::toString)
        .collect(toList());
    return returnVar;
  }

  /**
   * Check the uploaded file If the file is a zip file with more than one entries, an error occurs
   *
   * @param file
   * @return
   * @throws IOException
   */
  protected boolean checkFileCompliance(UploadedFile file) throws IOException {

    boolean result = false;

    // Check if file is empty
    if (file.getSize() == 0) {
      error(this, null, "gnxxcblcomlbls.import_csv_file_empty");
    }
    else if (isNotBlank(file.getFileName()) && file.getFileName()
        .endsWith(CSV.getExtension())) {
          // when uploading a CSV file from a Windows system WITHOUT Microsoft Excel installed,
          // the CSV file content-type is set to application/octet-stream which is an archive type.
          // If the file extension is .csv, no need to decompress the file
          return true;
        }
    else if (contains(FileType.ARCHIVE.getMimes(), file.getContentType())) {
      // Decompress the contents...
      try (final ZipInputStream zis = new ZipInputStream(file.getInputstream())) {
        zis.getNextEntry();
        if (zis.getNextEntry() != null) {
          error(this, null, "gnxxcblcomlbls.import_csv_zip_contains_more", file.getFileName());
        }
        else {
          // ZipInputStream must be instanciated again, because calling nextEntry twice moved the pointer of the inputstream
          try (final ZipInputStream zis2 = new ZipInputStream(file.getInputstream())) {
            ZipEntry entry = zis2.getNextEntry();
            if (entry == null) {
              error(this, null, "gnxxcblcomlbls.import_csv_zip_empty", file.getFileName());
            }
            else if (!entry.getName()
                .endsWith(CSV.getExtension())) {
                  error(this, null, "gnxxcblcomlbls.import_csv_zip_without_csv", file.getFileName());
                }
            else {
              result = true;
            }
          }
        }
      }
    }
    else {
      result = true;
    }
    return result;
  }

  /* --- ACTIONS --- */
  public abstract Logs importDocuments(List<InputStream> is);

  public String getIdFromIndexable(Indexable indexable) {
    Serializable entityId = indexable.getEntityId();
    if (entityId == null) {
      Document doc = documentService.getDocument(indexable);
      return doc == null ? null
          : doc.getId()
              .toString();
    }
    return entityId.toString();
  }

  private String currentPageParam;

  public String getCurrentPageParam(Portal portal) {
    if (currentPageParam == null) {
      com.byzaneo.portal.bean.Page currentPage = findFirstPageByPortletContentId(portal, valueOf(getDefinition(this).getId()));
      currentPageParam = currentPage.getId();
    }
    return currentPageParam;
  }

  public String getPreviousTaskUrl(Portal portal) {
    com.byzaneo.portal.bean.Page returnPage = findPage(portal, getPortalRequestParameterHandler().getReturnPage());
    if (returnPage == null) {
      warn("No Page defined with id : %s", getPortalRequestParameterHandler().getReturnPage()); // I18N
      return null;
    }
    return PortalISHandler.PORTAL_URL + "?page=" + returnPage.getId();
  }

  /**
   * Check if the document has changed based on the optimistic lock. Update the current document with the base.
   *
   * @return true if the document hasn't changed.
   */
  protected boolean checkRevisionDocument() {
    Document baseDocument = documentService.getDocument(Document.class, Long.valueOf(getPortalRequestParameterHandler().getDocumentId()),
        true);
    boolean notChanged = Objects.equals(document.getRevision(), baseDocument.getRevision());
    document = baseDocument;
    return notChanged;
  }

  /**
   * upoload files from standard primefaces'dialog when exception throws, hide the popup and show the error message
   *
   * @param event
   * <AUTHOR>
   */
  public void uploadFromStandardDialog(FileUploadEvent event) {
    try {
      uploadedFiles.add(event.getFile());
    }
    catch (Exception e) {
      error(e, "labels.exception_message", getRootCauseMessage(e));
      getCurrentInstance().execute("PF('" + STANDARD_DIALOG + "').hide()");
    }

  }

  public void uploadFromGenerixCustomerDialog(FileUploadEvent event) {
    try {
      uploadedFile = event.getFile();
      // check if the uploaded file is PDF type
      uploadedFilePDF = Arrays.stream(FileType.PDF.getMimes())
          .anyMatch(mime -> mime.equals(uploadedFile.getContentType()));
    }
    catch (Exception e) {
      error(e, "labels.exception_message", getRootCauseMessage(e));
      getCurrentInstance().execute("PF('" + GENERIX_CUSTOMER_DIALOG + "').hide()");
    }
  }

  /**
   * This api allows to import the {@code uploadedFiles} that is already populated
   */
  public void uploadFiles() {
    try {
      if (uploadedFiles.size() > 0) {
        importFiles(uploadedFiles);
      }
    }
    catch (Exception e) {
      error(e, "labels.exception_message", getRootCauseMessage(e));
    }
    finally {
      // reinit uploadFiles after imported beacuse it will be reused for next import
      uploadedFiles = new ArrayList<>();
    }
  }

  public void uploadFile() {
    try {
      if (uploadedFile != null) {
        importFiles(Arrays.asList(uploadedFile));
      }
    }
    catch (Exception e) {
      error(e, "labels.exception_message", getRootCauseMessage(e));
    }
    finally {
      // reinit uploadFiles after imported beacuse it will be reused for next import
      uploadedFile = null;
    }
  }

  public void onDownloadList(Instance instance) {
    if (dataModel != null && this.dataModel.getRowCount() == 0) {
      TaskHelper.warn(this, "gnxxcblorderlbls.error_export_no_records");
      return;
    }
    // CountgetAsynchronousProcessLimit
    int itemsCount = Long.valueOf(documentService.countIndexable(getIndexableType(), resolveSearchQuery()))
        .intValue();
    // Max
    int max = itemsCount > getListExportRange().getMax() ? getListExportRange().getMax() : itemsCount;
    int asynchronousLimit = (int) getAsynchronousProcessLimit();
    // Sort
    String sortField = getCurrentModelSort();
    Direction sortOrder = getCurrentModelOrder();
    // Process
    this.download = new DocumentFileViewer(getListExportFileName(), FileType.EXCEL,
        startDownloadProcess(FileType.EXCEL, instance, max, asynchronousLimit, getListExportFileName(), sortField, sortOrder));
    if (this.download != null) {
      getCurrentInstance().addCallbackParam("download", this.download != null && download.getFile() != null);
    }
    else {
      TaskHelper.error(this, null, "edctsklbls.unable_to_generate_file");
    }
  }

  /*
   * -- ACCESSORS --
   */
  protected PortalSessionHandler getPortalSessionHandler() {
    if (portalSessionHandler == null) {
      portalSessionHandler = getManagedBean(PortalSessionHandler.class, PortalSessionHandler.MANAGED_BEAN_NAME);
    }
    return portalSessionHandler;
  }

  protected PortalISHandler getPortalISHandler() {
    if (portalISHandler == null) {
      portalISHandler = getManagedBean(PortalISHandler.class, PortalISHandler.MANAGED_BEAN_NAME);
    }
    return portalISHandler;
  }

  protected PortalRequestParameterHandler getPortalRequestParameterHandler() {
    if (portalRequestParameterHandler == null) {
      portalRequestParameterHandler = getManagedBean(PortalRequestParameterHandler.class, PortalRequestParameterHandler.MANAGED_BEAN_NAME);
    }
    return portalRequestParameterHandler;
  }

  public Long getActionId() {
    TaskDefinition parentTaskDefinition = getParentDefinition();
    actionId = parentTaskDefinition != null ? parentTaskDefinition.getId() : null;
    return actionId;
  }

  public void setActionId(Long actionId) {
    this.actionId = actionId;
    if (actionId != null) {
      TaskDefinition actionTsk = taskService.getTaskDefinition(actionId);
      setParentDefinition(actionTsk);
      actionType = taskService.getTaskType(actionTsk.getType());
    }
    else {
      setParentDefinition(null);
      actionType = null;
    }
  }

  public String getActionName() {
    if (actionType == null) {

      TaskDefinition parentDefinition = getParentDefinition();
      if (parentDefinition != null) {
        this.actionType = taskService.getTaskType(parentDefinition.getType());
      }
    }
    return actionType != null ? actionType.getName() : null;
  }

  // -- RESTITUTION --
  public boolean enableAction() {
    return (getActionName() != null && isPartnerUser()) || isSupportFileType(FileType.PDF) || isSupportFileType(FileType.EXCEL);
  }

  public TaskDefinition getParentDefinition() {
    if (this instanceof Proxy) {
      taskDefinition = TaskHelper.getDefinition(this);
    }
    return taskDefinition != null ? taskDefinition.getParent() : null;
  }

  public void setParentDefinition(TaskDefinition parentTaskDefinition) {
    if (taskDefinition != null) {
      taskDefinition.setParent(parentTaskDefinition != null ? parentTaskDefinition : null);
    }
  }

  public Logs getLogs() {
    return logs;
  }

  public void setLogs(Logs logs) {
    this.logs = logs;
  }

  protected List<String> getAvailableStatus(Class<T> clazz, User user, Locale locale, boolean fromRest) {
    final OrganizationHelper.UserOrganizations userOrgs = resolveUserOrganizations(user);
    String companyCode = userOrgs.getCompanyCode();
    if (availableStatus == null && eDocumentService != null && (fromRest || !currentSession().isBackOfficeUser())) {
      if(!fromRest) {
        resolveOrganizationCodes();
      }
      List<String> status = eDocumentService.searchDocumentStatus(clazz, companyCode);
      availableStatus = status.stream()
          .sorted(comparing(l -> label(l, locale))) // Sort on translate value
          .collect(toList());
    }
    return availableStatus;
  }

  public User getAuthenticatedUser() {
    com.byzaneo.security.ui.handler.SessionHandler session = getManagedBean(com.byzaneo.security.ui.handler.SessionHandler.class,
        com.byzaneo.security.ui.handler.SessionHandler.MANAGED_BEAN_NAME);
    return session.getUser();
  }

  @Override
  protected List<String> getAvailableStatus(Class<T> clazz) {
    return getAvailableStatus(clazz, currentSession().getUser(), getSessionUserLocale(), false);
  }


  // - OPTIMIZATIONS -

  /**
   * Allows to enable count on lazy data loading. Count is not efficient on large data collectionss. Could be parameterized (later) for
   * configuration.
   * 
   * @return true if the count is enabled during lazy data loading.
   */
  public boolean isCountEnabled() {
    return true;
  }

  /*
   * -- LAZY DATA MODEL --
   */

  /**
   * @see com.byzaneo.generix.xtrade.task.AbstractIndexableTask#resolveDataModel()
   */
  @Override
  protected LazyDataModel<? extends Indexable> resolveDataModel() {
    return isSafeboxSearch()
        ? new ArchiveDataModel(resolveSearchQuery())
        : new XcblDocumentDataModel(resolveSearchQuery());
  }

  protected class XcblDocumentDataModel extends AbstractIndexableDataModel<T> {
    private static final long serialVersionUID = -6729324800659701990L;

    private transient String sortField;

    private transient Direction sortOrder;

    protected XcblDocumentDataModel(Query query) {
      super(documentService, query, getSearch(), getIndexableType(), isCountEnabled());
      this.addFilterProps();
    }

    private void addFilterProps() {
      if (globalFilterDescriptor != null) {
        for (PropertyDescriptor prop : globalFilterDescriptor) {
          descriptor.addProperty(prop);
        }
      }
    }

    @Override
    protected void addFilterClauses(QueryBuilder qb, Map<String, Object> filters) {
      if (!isAdvSearch()) {
        super.addFilterClauses(qb, filters);
      }
    }

    @Override
    public Page<T> load(PageRequest pageable, Map<String, Object> filters) {
      setSort(pageable);

      Page<T> results = super.load(pageable, filters);
      postLazyResponse(pageable, filters, results);

      RequestContext.getCurrentInstance()
          .execute("window.postFilter && postFilter(" +
              listExportRange.includes((int) results.getTotalElements()) + ");");

      // TODO due to a bug (I think) in primefaces, on upload the expanded rows are not kept.. so we need this to keep coherence
      rowExpansionMap.clear();
      rowExpansionDataModels.clear();
      return results;
    }

    public String getSortField() {
      return this.sortField;
    }

    public Query getQuery() {
      return this.query;
    }

    public Direction getSortOrder() {
      return this.sortOrder;
    }

    private void setSort(PageRequest pageable) {
      ofNullable(pageable).map(org.springframework.data.domain.PageRequest::getSort)
          .filter(s -> s.isSorted())
          .map(Sort::toString)
          .map(s -> s.split(":"))
          .ifPresent(arr -> {
            this.sortField = arr[0].trim();
            this.sortOrder = Direction.fromString(arr[1].trim());
          });
    }
  }

  protected class ArchiveDataModel extends AbstractIndexableDataModel<ArchivedInvoice> {
    private static final long serialVersionUID = -6360072567692601645L;

    protected ArchiveDataModel(Query query) {
      super(documentService, query, getSearch(), ArchivedInvoice.class);
    }

    @Override
    public Page<ArchivedInvoice> load(PageRequest pageable, Map<String, Object> filters) {
      Page<ArchivedInvoice> archives = eDocumentService.searchArchives(ArchivedInvoice.class, query, pageable);
      postLazyResponseArchive(pageable, filters, archives);
      return archives;
    }
  }

  protected String getCurrentModelSort() {
    return of(dataModel).filter(XcblDocumentDataModel.class::isInstance)
        .map(XcblDocumentDataModel.class::cast)
        .map(XcblDocumentDataModel::getSortField)
        .orElse(StringUtils.EMPTY);
  }

  protected Direction getCurrentModelOrder() {
    return of(dataModel).filter(XcblDocumentDataModel.class::isInstance)
        .map(XcblDocumentDataModel.class::cast)
        .map(XcblDocumentDataModel::getSortOrder)
        .orElse(Direction.ASC);
  }

  protected Indexable[] loadIndexables(Query query, String sortField, Direction sortOrder, int itemCount) {

    if (itemCount == 0) {
      return new Indexable[0];
    }
    org.springframework.data.domain.PageRequest pageable = isNotBlank(sortField)
        ? org.springframework.data.domain.PageRequest.of(0, itemCount, sortOrder, sortField)
        : org.springframework.data.domain.PageRequest.of(0, itemCount);
    List<Object> invoices = new ArrayList<>(this.documentService.searchIndexables(InvoiceIndex.class, query, pageable)
        .getContent());
    Indexable[] indexables = invoices.toArray(new Indexable[invoices.size()]);
    return indexables;

  }

  protected Indexable[] loadIndexables(Class<T> indexableClass, Query query, String sortField, Direction sortOrder, int itemCount) {

    if (itemCount == 0) {
      return new Indexable[0];
    }
    org.springframework.data.domain.PageRequest pageable = isNotBlank(sortField)
        ? org.springframework.data.domain.PageRequest.of(0, itemCount, sortOrder, sortField)
        : org.springframework.data.domain.PageRequest.of(0, itemCount);
    List<Object> indexable = new ArrayList<>(this.documentService.searchIndexables(indexableClass, query, pageable)
        .getContent());
    Indexable[] indexables = indexable.toArray(new Indexable[indexable.size()]);
    return indexables;

  }

  public Indexable[] loadIndexables(Query query, String sortField, Direction sortOrder) {
    int itemCount = (int) documentService.countIndexable(InvoiceIndex.class, query);
    return loadIndexables(query, sortField, sortOrder, itemCount);
  }

  /**
   * Méthode à surcharger pour traitement custom après réponse. The default implementation does nothing.
   *
   * @param pageable
   * @param filters
   */
  protected void postLazyResponse(Pageable pageable, Map<String, Object> filters, Page<T> documents) {
    // No-op
  }

  /**
   * Méthode à surcharger pour traitement custom après réponse. The default implementation does nothing.
   *
   * @param pageable
   * @param filters
   */
  protected void postLazyResponseArchive(Pageable pageable, Map<String, Object> filters, Page<ArchivedInvoice> documents) {
    // No-op
  }

  public String getListExportFileName() {
    return createExportFileName(true, null);
  }

  public boolean isAllowOrChrgLineEdit() {
    return allowOrChrgLineEdit;
  }

  public void setAllowOrChrgLineEdit(boolean allowOrChrgLineEdit) {
    this.allowOrChrgLineEdit = allowOrChrgLineEdit;
  }

  public boolean isOtherUomCode(UnitOfMeasurementType type) {
    setAllUOMS();
    return type.getUOMCoded()
        .equalsIgnoreCase(
            allUOMS.get(UnitOfMeasurement.UOM_Other.getCode()));
  }

  /**
   *
   */
  private void setAllUOMS() {
    if (allUOMS == null) {
      allUOMS = UnitOfMeasurement.initMap(selectedLanguage);
    }
  }

  public List<SelectItem> getBasisCodeItems() {
    return Arrays.asList(new SelectItem(BasisCodeType.PERCENT,
        getMessage(LABEL_XCBL_COMMON + ".PERCENT", "Percent", selectedLanguage)),
        new SelectItem(BasisCodeType.MONETARY_AMOUNT,
            getMessage(LABEL_XCBL_COMMON + ".MONETARY_AMOUNT", "Monetary amount",
                selectedLanguage)));
  }

  protected void clearTypeOfAllowanceOrCharge(Iterable<AllowOrChargeType> items) {
    for (AllowOrChargeType item : items) {
      if (item.getBasisCoded() == BasisCodeType.PERCENT) {
        item.getTypeOfAllowanceOrCharge()
            .setMonetaryValue(null);
      }
      else if (item.getBasisCoded() == BasisCodeType.MONETARY_AMOUNT) {
        item.getTypeOfAllowanceOrCharge()
            .setPercentageAllowanceOrCharge(null);
      }
      // avoid to generate tax in xcbl when no tax is provided
      if (!item.getTax()
          .isEmpty() &&
          item.getTax()
              .get(0)
              .getTaxPercent()
              .getValue() == null) {
        item.getTax()
            .clear();
      }
    }
  }

  protected void clearInvoiceTypeOfAllowanceOrCharge(Iterable<InvoiceAllowOrChargeType> items) {
    for (InvoiceAllowOrChargeType item : items) {
      if (item.getBasisCoded() == BasisCodeType.PERCENT) {
        item.getTypeOfAllowanceOrCharge()
            .setMonetaryValue(null);
      }
      else if (item.getBasisCoded() == BasisCodeType.MONETARY_AMOUNT) {
        item.getTypeOfAllowanceOrCharge()
            .setPercentageAllowanceOrCharge(null);
      }
      // avoid to generate tax in xcbl when no tax is provided
      if (!item.getTax()
          .isEmpty() &&
          item.getTax()
              .get(0)
              .getTaxPercent()
              .getValue() == null) {
        item.getTax()
            .clear();
      }
    }
  }

  public String getTodayAsStringUsingUserPattern() {
    return new SimpleDateFormat(
        EDateFormat
            .findPatternBy(getAuthenticatedUser(),
                SessionHandler.DEFAULT_DATE_PATTERN)
            .getPattern()).format(new Date());
  }

  @Override
  public Optional<String> getUserFriendlyBirtFileName() {
    return getUserFriendlyBirtFileName(getBirtFile().getFileType()
        .getExtension());
  }

  protected Optional<String> getUserFriendlyBirtFileName(String extension) {
    String typeDocument = getUserFriendlyMessageType();
    Locale locale = toLocale(this.getLanguage());
    String xcblNumber = getUserFriendlyXcblMessageNumber().orElse("NOTFOUND");

    // At least one information missing, don't block export process , older
    // mechanism is applied
    if (StringUtils.isBlank(typeDocument) || Objects.isNull(locale) || StringUtils.isBlank(xcblNumber)) {
      return Optional.empty();
    }
    return Optional.of(new StringBuilder().append(typeDocument)
        .append("_")
        .append(xcblNumber)
        .append("_")
        .append(locale.toString())
        .append(extension)
        .toString());
  }

  /**
   * Obtains the party details for the given ID from the partner/company or companies partners. In order to obtain the company partners
   * {@link AbstractXcblDocumentTask#getOrganizationByCode(String code, String parentCode)} must be implemented
   * 
   * @param ident
   * @return
   */
  public Group getPartyInfo(String ident) {
    if (isBlank(ident)) {
      return null;
    }
    resolveOrganizationCodes();
    if (ident.equals(partnerCode)) {
      return partner;
    }

    if (ident.equals(companyCode)) {
      return company;
    }
    return getOrganizationByCode(ident, companyCode);
  }

  public Group getOrganizationByCode(String code, String parentCode) {
    return null;
  }

  @Override
  public void setBirtFile(File file, FileType fileType) {
    setBirtFile(new BirtFile(file, fileType));
  }

  @Override
  public void setFiles(List<DocumentFile> files) {
    this.files = files;
  }

  public boolean canDeleteAddedFile(DocumentFile documentFile) {
    // by default don't allow to delete files
    return false;
  }

  public void onSelectAddedDocumentFileToDelete(DocumentFile addedDocumentFileToDelete) {
    this.addedDocumentFileToDelete = addedDocumentFileToDelete;
  }

  public void onConfirmDeleteAddedFile() {
    Validate.notNull(addedDocumentFileToDelete);
    Validate.validState(canDeleteAddedFile(addedDocumentFileToDelete));
    eDocumentService.deleteDocumentFile(addedDocumentFileToDelete);
    files = files.stream()
        .filter(dof -> dof.getId() != addedDocumentFileToDelete.getId())
        .map(dof -> documentService.getDocumentFile(dof.getId()))// reload doc files
        .collect(Collectors.toList());
  }

  private BeanDescriptor getDocumentFileDescriptor() {
    if (documentFileDescriptor == null)
      documentFileDescriptor = beanService.fromXml(DocumentHelper.getDocumentFileDescriptor());
    return documentFileDescriptor;
  }

  /**
   * Process the export by specifying the type of export file: archive or xls.
   *
   * @param exportArchive true if the download file type must be a zip archive
   * @param itemCount number of invoices to export
   * @param fileName the name of the download file
   * @param instance instance
   * @param fileType type of the download file
   * @param type type of files added to the archive
   */
  @SuppressWarnings("unchecked")
  public void export(boolean exportArchive, int itemCount, int asynchronousLimit, String fileName, Instance instance, FileType fileType,
      BirtOutoutFileType type) {
    this.exportArchive = exportArchive;
    // Sort
    String sortField = ((XcblDocumentDataModel) this.dataModel).getSortField();
    Direction sortOrder = ((XcblDocumentDataModel) this.dataModel).getSortOrder();
    // Process
    this.download = new DocumentFileViewer(fileName, fileType,
        startDownloadProcess(type.getFileType(), instance, itemCount, asynchronousLimit, fileName, sortField, sortOrder));
  }

  public void onDownloads(Instance instance, BirtOutoutFileType type, Class<T> clazz) {
    if (!isItemSelected() && !this.allIndexesSelected) {
      TaskHelper.warn(this, "gnxxcblcomlbls.error_document_select_line");
      return;
    }

    try {
      // Base Name of the files contained in the archive
      String label = getLabel(getFamilyLabel(), getLabelKey(), getDefaultLabel(), currentSession().getUser()
          .getLocale());
      baseFileName = ofNullable(label).map(String::toLowerCase)
          .orElse(EMPTY);
      // Number of invoices to export
      int itemCount = this.allIndexesSelected ? (int) documentService.countIndexable(clazz, resolveSearchQuery())
          : this.selected.length;
      int asynchronousLimit = (int) getAsynchronousProcessLimit();
      // Process the export
      export(true, itemCount, asynchronousLimit, createExportFileName(false, type.getFileType()
          .toString()
          .toLowerCase()), instance, FileType.ARCHIVE, type);
      // If download is synchronous
      if (itemCount < asynchronousLimit) {
        markDocumentsAsRead();
        getCurrentInstance()
            .addCallbackParam("download", this.download != null && this.download.getFile() != null);
      }
    }
    catch (Exception e) {
      MessageHelper.error(e, "Error downloading selected documents: %s", getRootCauseMessage(e));
      this.selected = null;
    }
    finally {
      this.documentsToUpdate = null;
    }
  }

  public boolean isOrderingParty() {
    return orderingParty;
  }

  public void setOrderingParty(boolean orderingParty) {
    this.orderingParty = orderingParty;
  }

  // ROW EXPANSION

  public void onRowToggle(ToggleEvent toggleEvent) {
    synchronized (rowExpansionMap) {
      Indexable rowExpansionSelected = (Indexable) toggleEvent.getData();
      this.rowExpansionMap.put(rowExpansionSelected.getId(), toggleEvent.getVisibility() == Visibility.VISIBLE);
      if (toggleEvent.getVisibility() == Visibility.HIDDEN) {
        rowExpansionDataModels.remove(rowExpansionSelected.getId());
      }
    }
  }

  public boolean isRowExpanded(Indexable indexable) {
    if (indexable == null || indexable.getId() == null) {
      return false;
    }
    boolean isRowExpanded = ofNullable(this.rowExpansionMap.get(indexable.getId())).orElse(false);
    // return isRowExpanded && !attachedFilesOfASN(indexable).isEmpty();
    return isRowExpanded;
  }

  public LazyDataModel<? extends Persistent<?>> getRowExpansionDataModel(Indexable indexable) {
    try {
      if (indexable == null) {
        log.error("Failed to get data model: indexable is null");
        return emptyDataModel();
      }
      synchronized (rowExpansionMap) {
        if (!isRowExpanded(indexable)) {
          return emptyDataModel();
        }
      }
      // we need to keep all the opened models in order to be able to select a file from a previously expended row
      if (!rowExpansionDataModels.containsKey(indexable.getId()))
        rowExpansionDataModels.put(indexable.getId(), new DatabaseDocumentFileDataModel());
      DatabaseDocumentFileDataModel model = rowExpansionDataModels.get(indexable.getId());
      // also we need to specify the expanded indexable row on the model so the database loading is correct
      model.setRowExpansionSelected(indexable);
      return model;
    }
    catch (Exception e) {
      error(e, "xtdtsklbls.error_data_model", e.getMessage());
      log.error("Failed to get data model", e);
      return emptyDataModel();
    }
  }

  public class DatabaseDocumentFileDataModel extends AbstractPersistentDataModel<Persistent<?>> {
    private static final long serialVersionUID = -5537605125865835842L;

    private transient List<Persistent<?>> data = new ArrayList<>(0);

    private transient Indexable rowExpansionSelected;

    public DatabaseDocumentFileDataModel() {
      super(documentService, new Query(), getDocumentFileDescriptor(),
          getRowExpansionType());
    }

    public void setRowExpansionSelected(Indexable indexable) {
      this.rowExpansionSelected = indexable;
    }

    @Override
    public Page<Persistent<?>> load(PageRequest pageable, Map<String, Object> filters) {
      data = resolveRowExpansionData(documentService, rowExpansionSelected);
      return new PageImpl<>(data);
    }

    @Override
    public Persistent<?> getRowData(final String rowKey) {
      return data.stream()
          .filter(p -> p != null && p.getId()
              .toString()
              .equals(rowKey))
          .findAny()
          .orElse(null);
    }
  }

  protected <R extends Persistent<?>> List<R> resolveRowExpansionData(DocumentService documentService, Indexable rowExpansionSelected2) {
    return emptyList();
  }

  protected <R extends Persistent<?>> Class<R> getRowExpansionType() {
    return null;
  }

  public void onPostValidationProcess(Document document, String validationProcessId, String postValidation, String docType,String companyCode) {
    if (validationProcessId == null)
      return;
    // only partners can run the process
    this.resolveOrganizationCodes();
    if (companyCode == null) {
      error(this, null, "error_only_partner_can_process");
      return;
    }

    // gets the deployed process
    final Deployment deployment = this.projectService.getDeployment(validationProcessId);
    if (deployment == null) {
      error(this, null, "error_process_not_deployed");
      return;
    }
    documentService.detachDocuments(Arrays.asList(document));

    // processes document
    Arrays.asList(document)
        .stream()
        .map(ufile ->
        {
          String uFileName = ufile.getFirstFile()
              .getName();
          try {
            return new ProcessReportExecution(uFileName,
                this.projectService.startAsync(deployment,
                    new HashMap<String, Object>() {
                      {
                        put(DOCUMENTS.toString(), singletonList(ufile));
                        put(WRITE_ASYNC.toString(), false);
                        /*
                         * even if the process is asynchronous we need to generate the reports synchronous to be able to obtain the report
                         * status of the process
                         */
                      }
                    }));
          }
          catch (Exception e) {
            return new ProcessReportExecution(uFileName, e);
          }
        })
        .collect(toList());
  }
  
  public void onPostValidationProcess(Document document, String validationProcessId, String postValidation, String docType) {
    this.onPostValidationProcess(document, validationProcessId, postValidation, docType, this.companyCode);
  }

  @Override
  public List<DocumentFile> getFiles() {
    return files;
  }

  @Override
  public DocumentFileViewer getDownload() {
    return download;
  }
}
