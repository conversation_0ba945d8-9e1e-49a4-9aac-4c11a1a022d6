package com.byzaneo.generix.signature.ui;

import static com.byzaneo.commons.ui.util.JSFHelper.getSpringBean;
import static java.util.Locale.ENGLISH;
import static java.util.stream.Collectors.toMap;
import static org.thymeleaf.util.ListUtils.toList;

import java.io.Serializable;
import java.util.*;
import java.util.AbstractMap.SimpleEntry;
import java.util.stream.Collectors;

import javax.faces.bean.*;

import com.byzaneo.commons.ui.util.JSFHelper;
import com.byzaneo.generix.signature.task.*;
import com.byzaneo.generix.signature.task.SignatureTaskClassic.*;
import com.byzaneo.generix.signature.task.contextfilter.ContextDocumentFilterPolicy;
import com.byzaneo.generix.xtrade.util.IndexableTaskHelper;
import com.byzaneo.xtrade.api.DocumentStage;
import com.byzaneo.xtrade.service.DocumentStatusService;
import org.apache.commons.collections4.MapUtils;

/**
 * UI handler for Signature Task
 */
@SuppressWarnings("serial")
@ManagedBean(name = SignatureTaskHandler.MANAGED_BEAN_NAME)
@ViewScoped
public class SignatureTaskHandler implements Serializable {

  public static final String MANAGED_BEAN_NAME = "gnxSignatureTaskHandler";

  private transient Map<String, String> documentStatusDescription;

  private transient Map<String, String> documentStageDescription;

  public Map<String, String> getLabellizedEnumDescription(String enumName, String familyVar) {
    if ("SignatureFormat".equals(enumName)) {
      return getLabellizedEnumDescription(SignatureFormat.values(), familyVar);
    }
    else if ("SignatureHashingAlgorithm".equals(enumName)) {
      return getLabellizedEnumDescription(SignatureHashingAlgorithm.values(), familyVar);
    }
    else if ("SignaturePackaging".equals(enumName)) {
      return getLabellizedEnumDescription(SignaturePackaging.values(), familyVar);
    }
    else if ("ContextDocumentFilterPolicy".equals(enumName)) {
      return getLabellizedEnumDescription(ContextDocumentFilterPolicy.values(), familyVar);
    }
    return new HashMap<>();
  }

  public <T extends Enum<?> & Labellized> Map<String, String> getLabellizedEnumDescription(T[] values, String familyVar) {
    Map<String, String> ret = new HashMap<>();
    if (values == null) {
      return ret;
    }

    for (T labellizedEnum : values) {
      String uiLabel = JSFHelper.getLabel(familyVar, labellizedEnum.getLabel(), labellizedEnum.toString(), JSFHelper.getLocale());
      ret.put(uiLabel, labellizedEnum.name());
    }

    return ret;
  }

  public Map<String, String> getDocumentStageDescription(String instanceCode) {
    if (Objects.isNull(this.documentStageDescription)) {
      // get all the document stages from enum translated to user selected language
      this.documentStageDescription = IndexableTaskHelper.toStageItems(Arrays.stream(DocumentStage.values())
              .map(DocumentStage::toString)
              .collect(
                  Collectors.toList()), JSFHelper.getLocale())
          .entrySet()
          .stream()
          .collect(Collectors.toMap(e -> (String) e.getKey(), Map.Entry::getValue));
      // invert the map because we want the key to be the label and the value to be the enum value
      // example: Refused(label) -> REFUSED(enum value), not REFUSED(enum value) -> Refused(label)
      // and sort it alphabetically
      this.documentStageDescription = MapUtils.invertMap(documentStageDescription)
          .entrySet()
          .stream()
          .sorted(
              Map.Entry.comparingByValue())
          .collect(Collectors.toMap(o -> o.getKey(), o -> o.getValue(), (e1, e2) -> e1, TreeMap::new));
    }

    return documentStageDescription;
  }

  public Map<String, String> getDocumentStatusDescription(String instanceCode) {
    if (documentStatusDescription == null) {
      DocumentStatusService documentStatusService = getSpringBean(DocumentStatusService.class, DocumentStatusService.SERVICE_NAME);
      documentStatusDescription = documentStatusService.getDocumentsStatuses(instanceCode)
          .stream()
          .map(s ->
          {
            Map<Locale, String> labels = s.getValues()
                .get(instanceCode)
                .getLabels();
            String label = labels.get(JSFHelper.getLocale()) != null ? labels.get(JSFHelper.getLocale()) : labels.get(ENGLISH);
            label = label != null ? label : s.getStatusCode();
            return new SimpleEntry<String, String>(label, s.getStatusCode());
          })
          .sorted((e1, e2) -> e1.getKey()
              .compareTo(e2.getKey()))
          .collect(toMap(o -> o.getKey(), o -> o.getValue(), (e1, e2) -> e1, TreeMap::new));
    }

    return documentStatusDescription;
  }
}
