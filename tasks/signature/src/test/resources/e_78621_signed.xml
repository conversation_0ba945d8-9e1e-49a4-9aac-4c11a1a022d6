<?xml version="1.0" encoding="UTF-8"?><Order xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/ordermanagement/v1_0/ordermanagement.xsd" xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" xmlns:dgs="http://www.w3.org/2000/09/xmldsig#">
    <OrderHeader>
        <OrderNumber>
            <BuyerOrderNumber>140329401_100</BuyerOrderNumber>
        </OrderNumber>
        <OrderIssueDate>2014-11-05T12:20:00</OrderIssueDate>
        <Purpose>
            <core:PurposeCoded>Original</core:PurposeCoded>
        </Purpose>
        <OrderType>
            <core:OrderTypeCoded>NewOrder</core:OrderTypeCoded>
        </OrderType>
        <OrderCurrency>
            <core:CurrencyCoded>EUR</core:CurrencyCoded>
        </OrderCurrency>
        <OrderLanguage>
            <core:LanguageCoded>it</core:LanguageCoded>
        </OrderLanguage>
        <OrderDates>
            <RequestedDeliverByDate>2014-11-13T00:00:00</RequestedDeliverByDate>
        </OrderDates>
        <OrderParty>
            <BuyerParty>
                <core:PartyID>
                    <core:Ident>08746440018</core:Ident>
                </core:PartyID>
                <core:NameAddress>
                    <core:Name1>ASL 1 BRINDISI</core:Name1>
                    <core:Street>S.S. 7 PER MESAGNE</core:Street>
                    <core:Department>ITC</core:Department>
                    <core:PostalCode>72100</core:PostalCode>
                    <core:City>BRINDISI-BR</core:City>
                </core:NameAddress>
                <core:PrimaryContact>
                    <core:ContactName>VILLANOVAG</core:ContactName>
                </core:PrimaryContact>
            </BuyerParty>
            <SellerParty>
                <core:PartyID>
                    <core:Ident>02747191217</core:Ident>
                </core:PartyID>
                <core:NameAddress>
                    <core:Name1>PAPPAGALLO SERGIO</core:Name1>
                    <core:Street>POST. 20-21</core:Street>
                    <core:PostalCode>70056</core:PostalCode>
                    <core:City>MOLFETTA-BA</core:City>
                </core:NameAddress>
            </SellerParty>
            <ShipToParty>
                <core:PartyID>
                    <core:Ident>001</core:Ident>
                </core:PartyID>
                <core:NameAddress>
                    <core:Name1>ASL BR 1</core:Name1>
                    <core:Street>S.S. 7 PER MESAGNE</core:Street>
                    <core:PostalCode>72100</core:PostalCode>
                    <core:City>BRINDISI-BR</core:City>
                </core:NameAddress>
            </ShipToParty>
            <SoldToParty>
                <core:PartyID>
                    <core:Ident>05351490965</core:Ident>
                </core:PartyID>
                <core:NameAddress>
                    <core:Name1>GEMEAZ ELIOR S.P.A.</core:Name1>
                    <core:Street>VIA PRIVATA VENEZIA GIULIA, 5/A</core:Street>
                    <core:PostalCode>20157</core:PostalCode>
                    <core:City>MILANO-MI</core:City>
                </core:NameAddress>
            </SoldToParty>
            <ListOfPartyCoded>
                <core:PartyCoded>
                    <core:PartyID>
                        <core:Ident>ELIOR_SPA</core:Ident>
                    </core:PartyID>
                    <core:PartyRoleCoded>Owner</core:PartyRoleCoded>
                </core:PartyCoded>
            </ListOfPartyCoded>
        </OrderParty>
        <OrderHeaderNote>CDC-F35321-ASL BR</OrderHeaderNote>
    </OrderHeader>
    <OrderDetail>
        <ListOfItemDetail>
            <ItemDetail>
                <BaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>1</core:BuyerLineItemNum>
                    </LineItemNum>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:SellerPartNumber>
                                <core:PartID>AV100057</core:PartID>
                            </core:SellerPartNumber>
                            <core:BuyerPartNumber>
                                <core:PartID>AV100057</core:PartID>
                            </core:BuyerPartNumber>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>Bar-CodedSerialNumber</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifier>             </core:ProductIdentifier>
                            </core:StandardPartNumber>
                            <core:SubstitutePartNumbers>
                                <core:ProductIdentifierCoded>
                                    <core:ProductIdentifierQualifierCoded>PartNumberDescription</core:ProductIdentifierQualifierCoded>
                                    <core:ProductIdentifier>PATATE NAZ</core:ProductIdentifier>
                                </core:ProductIdentifierCoded>
                            </core:SubstitutePartNumbers>
                        </core:PartNumbers>
                        <core:ItemDescription>PATATE NAZIONALI</core:ItemDescription>
                    </ItemIdentifiers>
                    <TotalQuantity>
                        <core:QuantityValue>150</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>Other</core:UOMCoded>
                            <core:UOMCodedOther>KG</core:UOMCodedOther>
                        </core:UnitOfMeasurement>
                    </TotalQuantity>
                </BaseItemDetail>
                <PricingDetail>
                    <core:ListOfPrice>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>UnitCostPrice</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>1</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>NetItemPrice</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>0.5</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                    </core:ListOfPrice>
                    <core:LineItemTotal>
                        <core:MonetaryAmount>75</core:MonetaryAmount>
                    </core:LineItemTotal>
                </PricingDetail>
                <DeliveryDetail>
                    <core:ListOfScheduleLine>
                        <core:ScheduleLine>
                            <core:Quantity>
                                <core:QuantityValue>150</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>Other</core:UOMCoded>
                                    <core:UOMCodedOther>KG</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:Quantity>
                            <core:RequestedDeliveryDate>2014-11-13T00:00:00</core:RequestedDeliveryDate>
                        </core:ScheduleLine>
                    </core:ListOfScheduleLine>
                </DeliveryDetail>
            </ItemDetail>
            <ItemDetail>
                <BaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>2</core:BuyerLineItemNum>
                    </LineItemNum>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:SellerPartNumber>
                                <core:PartID>AV100103</core:PartID>
                            </core:SellerPartNumber>
                            <core:BuyerPartNumber>
                                <core:PartID>AV100103</core:PartID>
                            </core:BuyerPartNumber>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>Bar-CodedSerialNumber</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifier>             </core:ProductIdentifier>
                            </core:StandardPartNumber>
                            <core:SubstitutePartNumbers>
                                <core:ProductIdentifierCoded>
                                    <core:ProductIdentifierQualifierCoded>PartNumberDescription</core:ProductIdentifierQualifierCoded>
                                    <core:ProductIdentifier>INSALATA TROCADERO</core:ProductIdentifier>
                                </core:ProductIdentifierCoded>
                            </core:SubstitutePartNumbers>
                        </core:PartNumbers>
                        <core:ItemDescription>INSALATA TROCADERO</core:ItemDescription>
                    </ItemIdentifiers>
                    <TotalQuantity>
                        <core:QuantityValue>30</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>Other</core:UOMCoded>
                            <core:UOMCodedOther>KG</core:UOMCodedOther>
                        </core:UnitOfMeasurement>
                    </TotalQuantity>
                </BaseItemDetail>
                <PricingDetail>
                    <core:ListOfPrice>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>UnitCostPrice</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>1</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>NetItemPrice</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>0.8</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                    </core:ListOfPrice>
                    <core:LineItemTotal>
                        <core:MonetaryAmount>24</core:MonetaryAmount>
                    </core:LineItemTotal>
                </PricingDetail>
                <DeliveryDetail>
                    <core:ListOfScheduleLine>
                        <core:ScheduleLine>
                            <core:Quantity>
                                <core:QuantityValue>30</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>Other</core:UOMCoded>
                                    <core:UOMCodedOther>KG</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:Quantity>
                            <core:RequestedDeliveryDate>2014-11-13T00:00:00</core:RequestedDeliveryDate>
                        </core:ScheduleLine>
                    </core:ListOfScheduleLine>
                </DeliveryDetail>
            </ItemDetail>
            <ItemDetail>
                <BaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>3</core:BuyerLineItemNum>
                    </LineItemNum>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:SellerPartNumber>
                                <core:PartID>AV100134</core:PartID>
                            </core:SellerPartNumber>
                            <core:BuyerPartNumber>
                                <core:PartID>AV100134</core:PartID>
                            </core:BuyerPartNumber>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>Bar-CodedSerialNumber</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifier>             </core:ProductIdentifier>
                            </core:StandardPartNumber>
                            <core:SubstitutePartNumbers>
                                <core:ProductIdentifierCoded>
                                    <core:ProductIdentifierQualifierCoded>PartNumberDescription</core:ProductIdentifierQualifierCoded>
                                    <core:ProductIdentifier>INSALATA RUCOLA</core:ProductIdentifier>
                                </core:ProductIdentifierCoded>
                            </core:SubstitutePartNumbers>
                        </core:PartNumbers>
                        <core:ItemDescription>INSALATA RUCOLA</core:ItemDescription>
                    </ItemIdentifiers>
                    <TotalQuantity>
                        <core:QuantityValue>1</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>Other</core:UOMCoded>
                            <core:UOMCodedOther>KG</core:UOMCodedOther>
                        </core:UnitOfMeasurement>
                    </TotalQuantity>
                </BaseItemDetail>
                <PricingDetail>
                    <core:ListOfPrice>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>UnitCostPrice</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>1</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>NetItemPrice</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>0.41</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                    </core:ListOfPrice>
                    <core:LineItemTotal>
                        <core:MonetaryAmount>0.41</core:MonetaryAmount>
                    </core:LineItemTotal>
                </PricingDetail>
                <DeliveryDetail>
                    <core:ListOfScheduleLine>
                        <core:ScheduleLine>
                            <core:Quantity>
                                <core:QuantityValue>1</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>Other</core:UOMCoded>
                                    <core:UOMCodedOther>KG</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:Quantity>
                            <core:RequestedDeliveryDate>2014-11-13T00:00:00</core:RequestedDeliveryDate>
                        </core:ScheduleLine>
                    </core:ListOfScheduleLine>
                </DeliveryDetail>
            </ItemDetail>
            <ItemDetail>
                <BaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>4</core:BuyerLineItemNum>
                    </LineItemNum>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:SellerPartNumber>
                                <core:PartID>AV100300</core:PartID>
                            </core:SellerPartNumber>
                            <core:BuyerPartNumber>
                                <core:PartID>AV100300</core:PartID>
                            </core:BuyerPartNumber>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>Bar-CodedSerialNumber</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifier>             </core:ProductIdentifier>
                            </core:StandardPartNumber>
                            <core:SubstitutePartNumbers>
                                <core:ProductIdentifierCoded>
                                    <core:ProductIdentifierQualifierCoded>PartNumberDescription</core:ProductIdentifierQualifierCoded>
                                    <core:ProductIdentifier>LIMONI</core:ProductIdentifier>
                                </core:ProductIdentifierCoded>
                            </core:SubstitutePartNumbers>
                        </core:PartNumbers>
                        <core:ItemDescription>LIMONI</core:ItemDescription>
                    </ItemIdentifiers>
                    <TotalQuantity>
                        <core:QuantityValue>15</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>Other</core:UOMCoded>
                            <core:UOMCodedOther>KG</core:UOMCodedOther>
                        </core:UnitOfMeasurement>
                    </TotalQuantity>
                </BaseItemDetail>
                <PricingDetail>
                    <core:ListOfPrice>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>UnitCostPrice</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>1</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>NetItemPrice</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>0.59</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                    </core:ListOfPrice>
                    <core:LineItemTotal>
                        <core:MonetaryAmount>8.85</core:MonetaryAmount>
                    </core:LineItemTotal>
                </PricingDetail>
                <DeliveryDetail>
                    <core:ListOfScheduleLine>
                        <core:ScheduleLine>
                            <core:Quantity>
                                <core:QuantityValue>15</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>Other</core:UOMCoded>
                                    <core:UOMCodedOther>KG</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:Quantity>
                            <core:RequestedDeliveryDate>2014-11-13T00:00:00</core:RequestedDeliveryDate>
                        </core:ScheduleLine>
                    </core:ListOfScheduleLine>
                </DeliveryDetail>
            </ItemDetail>
            <ItemDetail>
                <BaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>5</core:BuyerLineItemNum>
                    </LineItemNum>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:SellerPartNumber>
                                <core:PartID>AV100309</core:PartID>
                            </core:SellerPartNumber>
                            <core:BuyerPartNumber>
                                <core:PartID>AV100309</core:PartID>
                            </core:BuyerPartNumber>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>Bar-CodedSerialNumber</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifier>             </core:ProductIdentifier>
                            </core:StandardPartNumber>
                            <core:SubstitutePartNumbers>
                                <core:ProductIdentifierCoded>
                                    <core:ProductIdentifierQualifierCoded>PartNumberDescription</core:ProductIdentifierQualifierCoded>
                                    <core:ProductIdentifier>PERE 140-160 GR</core:ProductIdentifier>
                                </core:ProductIdentifierCoded>
                            </core:SubstitutePartNumbers>
                        </core:PartNumbers>
                        <core:ItemDescription>PERE GR 140/160</core:ItemDescription>
                    </ItemIdentifiers>
                    <TotalQuantity>
                        <core:QuantityValue>200</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>Other</core:UOMCoded>
                            <core:UOMCodedOther>KG</core:UOMCodedOther>
                        </core:UnitOfMeasurement>
                    </TotalQuantity>
                </BaseItemDetail>
                <PricingDetail>
                    <core:ListOfPrice>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>UnitCostPrice</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>1</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>NetItemPrice</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>1.1</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                    </core:ListOfPrice>
                    <core:LineItemTotal>
                        <core:MonetaryAmount>220</core:MonetaryAmount>
                    </core:LineItemTotal>
                </PricingDetail>
                <DeliveryDetail>
                    <core:ListOfScheduleLine>
                        <core:ScheduleLine>
                            <core:Quantity>
                                <core:QuantityValue>200</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>Other</core:UOMCoded>
                                    <core:UOMCodedOther>KG</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:Quantity>
                            <core:RequestedDeliveryDate>2014-11-13T00:00:00</core:RequestedDeliveryDate>
                        </core:ScheduleLine>
                    </core:ListOfScheduleLine>
                </DeliveryDetail>
            </ItemDetail>
            <ItemDetail>
                <BaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>6</core:BuyerLineItemNum>
                    </LineItemNum>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:SellerPartNumber>
                                <core:PartID>AV100329</core:PartID>
                            </core:SellerPartNumber>
                            <core:BuyerPartNumber>
                                <core:PartID>AV100329</core:PartID>
                            </core:BuyerPartNumber>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>Bar-CodedSerialNumber</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifier>             </core:ProductIdentifier>
                            </core:StandardPartNumber>
                            <core:SubstitutePartNumbers>
                                <core:ProductIdentifierCoded>
                                    <core:ProductIdentifierQualifierCoded>PartNumberDescription</core:ProductIdentifierQualifierCoded>
                                    <core:ProductIdentifier>MELE GOLDEN DELICIOUS 140-160 GR</core:ProductIdentifier>
                                </core:ProductIdentifierCoded>
                            </core:SubstitutePartNumbers>
                        </core:PartNumbers>
                        <core:ItemDescription>MELE GOLDEN GR 140/160</core:ItemDescription>
                    </ItemIdentifiers>
                    <TotalQuantity>
                        <core:QuantityValue>250</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>Other</core:UOMCoded>
                            <core:UOMCodedOther>KG</core:UOMCodedOther>
                        </core:UnitOfMeasurement>
                    </TotalQuantity>
                </BaseItemDetail>
                <PricingDetail>
                    <core:ListOfPrice>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>UnitCostPrice</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>1</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>NetItemPrice</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>1</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                    </core:ListOfPrice>
                    <core:LineItemTotal>
                        <core:MonetaryAmount>250</core:MonetaryAmount>
                    </core:LineItemTotal>
                </PricingDetail>
                <DeliveryDetail>
                    <core:ListOfScheduleLine>
                        <core:ScheduleLine>
                            <core:Quantity>
                                <core:QuantityValue>250</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>Other</core:UOMCoded>
                                    <core:UOMCodedOther>KG</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:Quantity>
                            <core:RequestedDeliveryDate>2014-11-13T00:00:00</core:RequestedDeliveryDate>
                        </core:ScheduleLine>
                    </core:ListOfScheduleLine>
                </DeliveryDetail>
            </ItemDetail>
            <ItemDetail>
                <BaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>7</core:BuyerLineItemNum>
                    </LineItemNum>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:SellerPartNumber>
                                <core:PartID>AV100402</core:PartID>
                            </core:SellerPartNumber>
                            <core:BuyerPartNumber>
                                <core:PartID>AV100402</core:PartID>
                            </core:BuyerPartNumber>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>Bar-CodedSerialNumber</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifier>             </core:ProductIdentifier>
                            </core:StandardPartNumber>
                            <core:SubstitutePartNumbers>
                                <core:ProductIdentifierCoded>
                                    <core:ProductIdentifierQualifierCoded>PartNumberDescription</core:ProductIdentifierQualifierCoded>
                                    <core:ProductIdentifier>PREZZEMOLO MAZZO</core:ProductIdentifier>
                                </core:ProductIdentifierCoded>
                            </core:SubstitutePartNumbers>
                        </core:PartNumbers>
                        <core:ItemDescription>PREZZEMOLO</core:ItemDescription>
                    </ItemIdentifiers>
                    <TotalQuantity>
                        <core:QuantityValue>2</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>Other</core:UOMCoded>
                            <core:UOMCodedOther>NR</core:UOMCodedOther>
                        </core:UnitOfMeasurement>
                    </TotalQuantity>
                </BaseItemDetail>
                <PricingDetail>
                    <core:ListOfPrice>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>UnitCostPrice</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>1</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>NetItemPrice</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>0.9</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                    </core:ListOfPrice>
                    <core:LineItemTotal>
                        <core:MonetaryAmount>1.8</core:MonetaryAmount>
                    </core:LineItemTotal>
                </PricingDetail>
                <DeliveryDetail>
                    <core:ListOfScheduleLine>
                        <core:ScheduleLine>
                            <core:Quantity>
                                <core:QuantityValue>2</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>Other</core:UOMCoded>
                                    <core:UOMCodedOther>NR</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:Quantity>
                            <core:RequestedDeliveryDate>2014-11-13T00:00:00</core:RequestedDeliveryDate>
                        </core:ScheduleLine>
                    </core:ListOfScheduleLine>
                </DeliveryDetail>
            </ItemDetail>
            <ItemDetail>
                <BaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>8</core:BuyerLineItemNum>
                    </LineItemNum>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:SellerPartNumber>
                                <core:PartID>AV102067</core:PartID>
                            </core:SellerPartNumber>
                            <core:BuyerPartNumber>
                                <core:PartID>AV102067</core:PartID>
                            </core:BuyerPartNumber>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>Bar-CodedSerialNumber</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifier>             </core:ProductIdentifier>
                            </core:StandardPartNumber>
                            <core:SubstitutePartNumbers>
                                <core:ProductIdentifierCoded>
                                    <core:ProductIdentifierQualifierCoded>PartNumberDescription</core:ProductIdentifierQualifierCoded>
                                    <core:ProductIdentifier>CAROTE BIO</core:ProductIdentifier>
                                </core:ProductIdentifierCoded>
                            </core:SubstitutePartNumbers>
                        </core:PartNumbers>
                        <core:ItemDescription>CAROTE BIO</core:ItemDescription>
                    </ItemIdentifiers>
                    <TotalQuantity>
                        <core:QuantityValue>20</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>Other</core:UOMCoded>
                            <core:UOMCodedOther>KG</core:UOMCodedOther>
                        </core:UnitOfMeasurement>
                    </TotalQuantity>
                </BaseItemDetail>
                <PricingDetail>
                    <core:ListOfPrice>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>UnitCostPrice</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>1</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>NetItemPrice</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>1.1</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                    </core:ListOfPrice>
                    <core:LineItemTotal>
                        <core:MonetaryAmount>22</core:MonetaryAmount>
                    </core:LineItemTotal>
                </PricingDetail>
                <DeliveryDetail>
                    <core:ListOfScheduleLine>
                        <core:ScheduleLine>
                            <core:Quantity>
                                <core:QuantityValue>20</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>Other</core:UOMCoded>
                                    <core:UOMCodedOther>KG</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:Quantity>
                            <core:RequestedDeliveryDate>2014-11-13T00:00:00</core:RequestedDeliveryDate>
                        </core:ScheduleLine>
                    </core:ListOfScheduleLine>
                </DeliveryDetail>
            </ItemDetail>
            <ItemDetail>
                <BaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>9</core:BuyerLineItemNum>
                    </LineItemNum>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:SellerPartNumber>
                                <core:PartID>AV102075</core:PartID>
                            </core:SellerPartNumber>
                            <core:BuyerPartNumber>
                                <core:PartID>AV102075</core:PartID>
                            </core:BuyerPartNumber>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>Bar-CodedSerialNumber</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifier>             </core:ProductIdentifier>
                            </core:StandardPartNumber>
                            <core:SubstitutePartNumbers>
                                <core:ProductIdentifierCoded>
                                    <core:ProductIdentifierQualifierCoded>PartNumberDescription</core:ProductIdentifierQualifierCoded>
                                    <core:ProductIdentifier>CIPOLLE BIO</core:ProductIdentifier>
                                </core:ProductIdentifierCoded>
                            </core:SubstitutePartNumbers>
                        </core:PartNumbers>
                        <core:ItemDescription>CIPOLLE BIO</core:ItemDescription>
                    </ItemIdentifiers>
                    <TotalQuantity>
                        <core:QuantityValue>20</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>Other</core:UOMCoded>
                            <core:UOMCodedOther>KG</core:UOMCodedOther>
                        </core:UnitOfMeasurement>
                    </TotalQuantity>
                </BaseItemDetail>
                <PricingDetail>
                    <core:ListOfPrice>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>UnitCostPrice</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>1</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>NetItemPrice</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>1.2</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                    </core:ListOfPrice>
                    <core:LineItemTotal>
                        <core:MonetaryAmount>24</core:MonetaryAmount>
                    </core:LineItemTotal>
                </PricingDetail>
                <DeliveryDetail>
                    <core:ListOfScheduleLine>
                        <core:ScheduleLine>
                            <core:Quantity>
                                <core:QuantityValue>20</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>Other</core:UOMCoded>
                                    <core:UOMCodedOther>KG</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:Quantity>
                            <core:RequestedDeliveryDate>2014-11-13T00:00:00</core:RequestedDeliveryDate>
                        </core:ScheduleLine>
                    </core:ListOfScheduleLine>
                </DeliveryDetail>
            </ItemDetail>
            <ItemDetail>
                <BaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>10</core:BuyerLineItemNum>
                    </LineItemNum>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:SellerPartNumber>
                                <core:PartID>AV102097</core:PartID>
                            </core:SellerPartNumber>
                            <core:BuyerPartNumber>
                                <core:PartID>AV102097</core:PartID>
                            </core:BuyerPartNumber>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>Bar-CodedSerialNumber</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifier>             </core:ProductIdentifier>
                            </core:StandardPartNumber>
                            <core:SubstitutePartNumbers>
                                <core:ProductIdentifierCoded>
                                    <core:ProductIdentifierQualifierCoded>PartNumberDescription</core:ProductIdentifierQualifierCoded>
                                    <core:ProductIdentifier>SEDANO VERDE BIO</core:ProductIdentifier>
                                </core:ProductIdentifierCoded>
                            </core:SubstitutePartNumbers>
                        </core:PartNumbers>
                        <core:ItemDescription>SEDANO VERDE BIO</core:ItemDescription>
                    </ItemIdentifiers>
                    <TotalQuantity>
                        <core:QuantityValue>20</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>Other</core:UOMCoded>
                            <core:UOMCodedOther>KG</core:UOMCodedOther>
                        </core:UnitOfMeasurement>
                    </TotalQuantity>
                </BaseItemDetail>
                <PricingDetail>
                    <core:ListOfPrice>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>UnitCostPrice</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>1</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>NetItemPrice</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>1.6</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                    </core:ListOfPrice>
                    <core:LineItemTotal>
                        <core:MonetaryAmount>32</core:MonetaryAmount>
                    </core:LineItemTotal>
                </PricingDetail>
                <DeliveryDetail>
                    <core:ListOfScheduleLine>
                        <core:ScheduleLine>
                            <core:Quantity>
                                <core:QuantityValue>20</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>Other</core:UOMCoded>
                                    <core:UOMCodedOther>KG</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:Quantity>
                            <core:RequestedDeliveryDate>2014-11-13T00:00:00</core:RequestedDeliveryDate>
                        </core:ScheduleLine>
                    </core:ListOfScheduleLine>
                </DeliveryDetail>
            </ItemDetail>
        </ListOfItemDetail>
    </OrderDetail>
    <OrderSummary>
        <NumberOfLines>10</NumberOfLines>
        <OrderTotal>
            <core:MonetaryAmount>658.06</core:MonetaryAmount>
        </OrderTotal>
    </OrderSummary>
<ds:Signature xmlns:ds="http://www.w3.org/2000/09/xmldsig#" Id="id-37670357c45406d3518ee3a1333bb236"><ds:SignedInfo><ds:CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/><ds:SignatureMethod Algorithm="http://www.w3.org/2001/04/xmldsig-more#rsa-sha256"/><ds:Reference Id="r-id-1" Type="" URI=""><ds:Transforms><ds:Transform Algorithm="http://www.w3.org/TR/1999/REC-xpath-19991116"><ds:XPath>not(ancestor-or-self::ds:Signature)</ds:XPath></ds:Transform><ds:Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/></ds:Transforms><ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/><ds:DigestValue>NCXoPCxvKvtrbW8sEpug/+ZCdU7cnRXh9D7CTtNRkCg=</ds:DigestValue></ds:Reference><ds:Reference Type="http://uri.etsi.org/01903#SignedProperties" URI="#xades-id-37670357c45406d3518ee3a1333bb236"><ds:Transforms><ds:Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/></ds:Transforms><ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/><ds:DigestValue>pr5iPdqc74Z0U+d+edYCL0kssqPIN9DmGUahgeS1Giw=</ds:DigestValue></ds:Reference></ds:SignedInfo><ds:SignatureValue Id="value-id-37670357c45406d3518ee3a1333bb236">FM1+uVrWTxt6Va42FtiiX5ne9C+K76XhiL0qyBe7a2lOWtoKw1f5xgxVtqZU8U7ZC7JhaAhoByVxNlKiYwsAwyrbRkW1baXyAoLevGIjrO86V6Fg9B9LJg/0g1URQS69zUSQ5+6NP0p/sk0hcwhDVNS43OwqqDBnMbqDFv7N4BitGtc/ZniGJ4XdlALKpgp77WejIikepv7COTD1u7e5K0PkyVcph6Lgf6f4HllcMX2IVT6+yS8xYhdpk5BlxuPA9rR0gD8Gs2AfXFf3O+sKUcVoOxmDVhIBWRDZG7vj763Kp/7w2/7SrNn8BsTClKwfXnX8iJ3kRIGG0Pq3OPPgxA==</ds:SignatureValue><ds:KeyInfo><ds:X509Data><ds:X509Certificate>MIIGljCCBX6gAwIBAgIQSVZTyobrV7UldaIB+KyLozANBgkqhkiG9w0BAQsFADCBkDELMAkGA1UEBhMCR0IxGzAZBgNVBAgTEkdyZWF0ZXIgTWFuY2hlc3RlcjEQMA4GA1UEBxMHU2FsZm9yZDEaMBgGA1UEChMRQ09NT0RPIENBIExpbWl0ZWQxNjA0BgNVBAMTLUNPTU9ETyBSU0EgRG9tYWluIFZhbGlkYXRpb24gU2VjdXJlIFNlcnZlciBDQTAeFw0xNjA1MzAwMDAwMDBaFw0xODExMDkyMzU5NTlaMFwxITAfBgNVBAsTGERvbWFpbiBDb250cm9sIFZhbGlkYXRlZDEhMB8GA1UECxMYUG9zaXRpdmVTU0wgTXVsdGktRG9tYWluMRQwEgYDVQQDEwtnZW5lcml4LmJpejCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBANYzeSZipvyrjE90+9WHUmLBfJ9YCvaW/0RoUgpR+mzYsiZ7iYJ8qKGBMN4itQjeDAlbSP7JthV0FkqfMHSdGuG6zi8xy7fbBxDyENAZlqJK1ds6Yfql86mjnndcELdG/jF2cpzqPIwpgo0rT0mRfbMf8BsDQ2xQB7jeRYmt1mCgRBqlXeG1Ardgp+9zTQULP+WvnTirmeLuWi4Z6vdnRF/emliscqc3wdh/3eDyvzmtSh5SUqFwFsdXWN6JTIbdidvc2bO+o5TcS5QQDKc4Lx7cOexOJid+l6Zyj37lNJVzFtde7qrJhyogMv4WidRHn70dcTkEHDN/ivT+drQeQfMCAwEAAaOCAx0wggMZMB8GA1UdIwQYMBaAFJCvajqUWgvYkOoSVnPfQ7Q6KNrnMB0GA1UdDgQWBBTwbzgPHLnSgTxmwnaWAJ9L1OlJyjAOBgNVHQ8BAf8EBAMCBaAwDAYDVR0TAQH/BAIwADAdBgNVHSUEFjAUBggrBgEFBQcDAQYIKwYBBQUHAwIwTwYDVR0gBEgwRjA6BgsrBgEEAbIxAQICBzArMCkGCCsGAQUFBwIBFh1odHRwczovL3NlY3VyZS5jb21vZG8uY29tL0NQUzAIBgZngQwBAgEwVAYDVR0fBE0wSzBJoEegRYZDaHR0cDovL2NybC5jb21vZG9jYS5jb20vQ09NT0RPUlNBRG9tYWluVmFsaWRhdGlvblNlY3VyZVNlcnZlckNBLmNybDCBhQYIKwYBBQUHAQEEeTB3ME8GCCsGAQUFBzAChkNodHRwOi8vY3J0LmNvbW9kb2NhLmNvbS9DT01PRE9SU0FEb21haW5WYWxpZGF0aW9uU2VjdXJlU2VydmVyQ0EuY3J0MCQGCCsGAQUFBzABhhhodHRwOi8vb2NzcC5jb21vZG9jYS5jb20wggFpBgNVHREEggFgMIIBXIILZ2VuZXJpeC5iaXqCGyouYjJiLWdhbGVyaWVzbGFmYXlldHRlLmNvbYIWKi5jbG91ZG9wcy5nZW5lcml4LmJpeoINKi5nZW5lcml4LmJpeoIVKi5nZW5lcml4b25kZW1hbmQuY29tgg8qLmh1YnhwcmVzcy5uZXSCHCouaW52b2ljZW1hbmFnZXJvbmRlbWFuZC5jb22CDyoubmV0aXh2aWV3LmNvbYIZKi50cmFkZXhwcmVzc29uZGVtYW5kLmNvbYIZYjJiLWdhbGVyaWVzbGFmYXlldHRlLmNvbYIUY2xvdWRvcHMuZ2VuZXJpeC5iaXqCE2dlbmVyaXhvbmRlbWFuZC5jb22CDWh1YnhwcmVzcy5uZXSCGmludm9pY2VtYW5hZ2Vyb25kZW1hbmQuY29tgg1uZXRpeHZpZXcuY29tghd0cmFkZXhwcmVzc29uZGVtYW5kLmNvbTANBgkqhkiG9w0BAQsFAAOCAQEAWHk7UjOHQ1WgvXj1mrYj7ICZkhBFnz+T4SArviAZZmzHxQA3r+YK2ipaH2pBR4KJXpNnLSLXwKR8A9Qms4phxRig+i9erwkS50vlCKOUEWAxRTRZuj4VeacQDkGmFL+yPM5aSCjiyiL6XgCCMa/6xNG78qEwOy2tnBOAr5kJ2bdsl8d3aqgv1Qkm1K/r1AEI+LfFRn5AisoLUvGwBG/GQs43oON6hWMxoqIx3GJM8A6qHdtxDljoBg8rUgocHwPM5oV1ad4yK9treEAKy5aVoV6PAuZlVeRlWcCNVxHKyl3ns6DfNy67GzHzM1XpyC72kRCo0CrLuooCkTDkOpuvkQ==</ds:X509Certificate><ds:X509Certificate>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</ds:X509Certificate><ds:X509Certificate>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</ds:X509Certificate></ds:X509Data></ds:KeyInfo><ds:Object><xades:QualifyingProperties xmlns:xades="http://uri.etsi.org/01903/v1.3.2#" Target="#id-37670357c45406d3518ee3a1333bb236"><xades:SignedProperties Id="xades-id-37670357c45406d3518ee3a1333bb236"><xades:SignedSignatureProperties><xades:SigningTime>2017-12-06T15:54:19Z</xades:SigningTime><xades:SigningCertificateV2><xades:Cert><xades:CertDigest><ds:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha1"/><ds:DigestValue>AOEqM418hf7YF/tH9Wo+DnCYOoQ=</ds:DigestValue></xades:CertDigest><xades:IssuerSerialV2>MIGrMIGWpIGTMIGQMQswCQYDVQQGEwJHQjEbMBkGA1UECBMSR3JlYXRlciBNYW5jaGVzdGVyMRAwDgYDVQQHEwdTYWxmb3JkMRowGAYDVQQKExFDT01PRE8gQ0EgTGltaXRlZDE2MDQGA1UEAxMtQ09NT0RPIFJTQSBEb21haW4gVmFsaWRhdGlvbiBTZWN1cmUgU2VydmVyIENBAhBJVlPKhutXtSV1ogH4rIuj</xades:IssuerSerialV2></xades:Cert></xades:SigningCertificateV2></xades:SignedSignatureProperties><xades:SignedDataObjectProperties><xades:DataObjectFormat ObjectReference="#r-id-1"><xades:MimeType>text/xml</xades:MimeType></xades:DataObjectFormat></xades:SignedDataObjectProperties></xades:SignedProperties></xades:QualifyingProperties></ds:Object></ds:Signature></Order>
