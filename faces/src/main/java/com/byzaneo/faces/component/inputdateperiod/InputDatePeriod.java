package com.byzaneo.faces.component.inputdateperiod;

import static com.byzaneo.faces.util.FacesHelper.resolveLocale;
import static java.text.DateFormat.SHORT;
import static java.text.DateFormat.getDateInstance;
import static java.text.DateFormat.getDateTimeInstance;
import static java.util.Arrays.asList;
import static java.util.Collections.unmodifiableCollection;
import static java.util.stream.Stream.of;
import static javax.faces.component.UINamingContainer.getSeparatorChar;
import static javax.faces.event.PhaseId.APPLY_REQUEST_VALUES;
import static javax.faces.event.PhaseId.INVOKE_APPLICATION;
import static javax.faces.event.PhaseId.PROCESS_VALIDATIONS;
import static org.primefaces.util.Constants.RequestParams.PARTIAL_BEHAVIOR_EVENT_PARAM;

import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;
import java.util.stream.Collectors;

import javax.faces.application.ResourceDependencies;
import javax.faces.application.ResourceDependency;
import javax.faces.component.html.HtmlInputText;
import javax.faces.context.FacesContext;
import javax.faces.event.AjaxBehaviorEvent;
import javax.faces.event.FacesEvent;

import org.primefaces.component.api.InputHolder;
import org.primefaces.component.api.MixedClientBehaviorHolder;
import org.primefaces.component.api.Widget;
import org.primefaces.event.SelectEvent;
import org.primefaces.util.Constants;

import com.byzaneo.commons.bean.DatePeriod;

/**
 * See http://www.daterangepicker.com/
 *
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date Oct 12, 2015
 * @since 3.0 FCS-35
 */
@ResourceDependencies({
    @ResourceDependency(library = "primefaces", name = "primefaces.css"),
    @ResourceDependency(library = "primefaces", name = "jquery/jquery.js"),
    @ResourceDependency(library = "primefaces", name = "jquery/jquery-plugins.js"),
    @ResourceDependency(library = "primefaces", name = "primefaces.js")
    // , @ResourceDependency(library = "bootstrap", name = "js/bootstrap.min.js")
    // , @ResourceDependency(library = "bootstrap", name = "css/bootstrap.min.css")
    , @ResourceDependency(library = "byzaneo", name = "lib/moment/moment.min.js"),
    @ResourceDependency(library = "byzaneo", name = "lib/daterangepicker/daterangepicker.min.css"),
    @ResourceDependency(library = "byzaneo", name = "lib/daterangepicker/daterangepicker.min.js")
    // Production
    , @ResourceDependency(library = "byzaneo", name = "byzaneo.css"), @ResourceDependency(library = "byzaneo", name = "byzaneo.js")
    // Development
    // , @ResourceDependency(library = "byzaneo", name = "core/core.js")
    // , @ResourceDependency(library = "byzaneo", name = "inputdateperiod/inputdateperiod.css")
    // , @ResourceDependency(library = "byzaneo", name = "inputdateperiod/inputdateperiod.js")
})
public class InputDatePeriod extends HtmlInputText implements Widget, InputHolder, MixedClientBehaviorHolder {

  private enum Event {
    /** Triggered when the value is changed (thru the input or the picker) */
    change,
    // /** Triggered when the picker is shown */
    // show,
    // /** Triggered when the picker is hidden */
    // hide,
    // /** Triggered when the calendar(s) are shown */
    // showCalendar,
    // /** Triggered when the calendar(s) are hidden */
    // hideCalendar,
    /** Triggered when the apply button is clicked, or when a predefined range is clicked */
    apply,
    // /** Triggered when the cancel button is clicked */
    // cancel;
  }

  public static final String TYPE = "com.byzaneo.faces.component.InputDatePeriod";
  public static final String FAMILY = "com.byzaneo.faces.component";
  private static final String RENDERER = "com.byzaneo.faces.component.InputDatePeriodRenderer";

  public final static String STYLE_CLASS = "form-control ui-inputfield ui-inputdateperiod ui-widget ui-state-default ui-corner-all";
  public final static String STYLE_CLASS_CONTAINER = "ui-dateperiod";
  public static final String STYLE_CLASS_ICON = "glyphicon glyphicon-calendar fa fa-calendar";

  private Map<String, AjaxBehaviorEvent> customEvents = new HashMap<String, AjaxBehaviorEvent>();
  private Locale calculatedLocale;
  private String calculatedPattern;
  private TimeZone appropriateTimeZone;

  private enum PropertyKeys {

    placeholder,
    widgetVar,
    /** (DatePeriod) calendars boundaries */
    limit,
    /** (Number) max size of the selected date period in days */
    limitDays,
    /** (String) dates and times pattern */
    pattern,
    /** (String/Locale) locale applied to the dates and labels */
    locale,
    /** (String/TimeZone) time zone applied to the dates */
    timeZone,
    /** (Boolean) enable/disable direct input */
    readonlyInput,
    /** (Boolean) show/hide input's icon */
    showIcon,
    /** (String) Dates separator */
    separator,
    // time
    /** (Boolean) Allow selection of dates with times, not just dates */
    timePicker,
    /** (Number) Increment of the minutes selection list for times (i.e. 30 to allow only selection of times ending in 0 or 30) */
    timePickerIncrement,
    /** (Boolean) Use 24-hour instead of 12-hour times, removing the AM/PM selection */
    timePicker24Hour,
    /** (Boolean) Show seconds in the timePicker */
    timePickerSeconds,
    // labels
    applyLabel,
    cancelLabel,
    fromLabel,
    toLabel,
    customRangeLabel;
  }

  public InputDatePeriod() {
    setRendererType(RENDERER);
  }

  @Override
  public String getFamily() {
    return FAMILY;
  }

  public String getWidgetVar() {
    return (String) getStateHelper().eval(PropertyKeys.widgetVar, null);
  }

  public void setWidgetVar(String widgetVar) {
    getStateHelper().put(PropertyKeys.widgetVar, widgetVar);
  }

  // limit

  public DatePeriod getLimit() {
    return (DatePeriod) getStateHelper().eval(PropertyKeys.limit, null);
  }

  public void setMindate(DatePeriod limit) {
    getStateHelper().put(PropertyKeys.limit, limit);
  }

  public Integer getLimitDays() {
    return (Integer) getStateHelper().eval(PropertyKeys.limitDays, null);
  }

  public void setLimitDays(Integer limitDays) {
    getStateHelper().put(PropertyKeys.limitDays, limitDays);
  }

  // i18n

  public String getPattern() {
    return (String) getStateHelper().eval(PropertyKeys.pattern, null);
  }

  public void setPattern(String pattern) {
    getStateHelper().put(PropertyKeys.pattern, pattern);
  }

  public Object getLocale() {
    return (Object) getStateHelper().eval(PropertyKeys.locale, null);
  }

  public void setLocale(Object locale) {
    getStateHelper().put(PropertyKeys.locale, locale);
  }

  public Object getTimeZone() {
    return (Object) getStateHelper().eval(PropertyKeys.timeZone, null);
  }

  public void setTimeZone(Object timeZone) {
    getStateHelper().put(PropertyKeys.timeZone, timeZone);
  }

  // display

  public String getPlaceholder() {
    return (String) getStateHelper().eval(PropertyKeys.placeholder, null);
  }

  public void setPlaceholder(String placeholder) {
    getStateHelper().put(PropertyKeys.placeholder, placeholder);
  }

  public boolean isReadonlyInput() {
    return (Boolean) getStateHelper().eval(PropertyKeys.readonlyInput, false);
  }

  public void setReadonlyInput(boolean readonlyInput) {
    getStateHelper().put(PropertyKeys.readonlyInput, readonlyInput);
  }

  public boolean isShowIcon() {
    return (Boolean) getStateHelper().eval(PropertyKeys.showIcon, true);
  }

  public void setShowIcon(boolean showIcon) {
    getStateHelper().put(PropertyKeys.showIcon, showIcon);
  }

  public String getSeparator() {
    return (String) getStateHelper().eval(PropertyKeys.separator, " - ");
  }

  public void setSeparator(String pattern) {
    getStateHelper().put(PropertyKeys.separator, pattern);
  }

  // Time

  public boolean isTimePicker() {
    return (Boolean) getStateHelper().eval(PropertyKeys.timePicker, false);
  }

  public void setTimePicker(boolean timePicker) {
    getStateHelper().put(PropertyKeys.timePicker, timePicker);
  }

  public boolean isTimePicker24Hour() {
    return (Boolean) getStateHelper().eval(PropertyKeys.timePicker24Hour, false);
  }

  public void setTimePicker24Hour(boolean timePicker24Hour) {
    getStateHelper().put(PropertyKeys.timePicker24Hour, timePicker24Hour);
  }

  public boolean isTimePickerSeconds() {
    return (Boolean) getStateHelper().eval(PropertyKeys.timePickerSeconds, false);
  }

  public void setTimePickerSeconds(boolean timePickerSeconds) {
    getStateHelper().put(PropertyKeys.timePickerSeconds, timePickerSeconds);
  }

  public Integer getTimePickerIncrement() {
    return (Integer) getStateHelper().eval(PropertyKeys.timePickerIncrement, 1);
  }

  public void setTimePickerIncrement(Integer timePickerIncrement) {
    getStateHelper().put(PropertyKeys.timePickerIncrement, timePickerIncrement);
  }

  // Labels

  public String getApplyLabel() {
    return (String) getStateHelper().eval(PropertyKeys.applyLabel, null);
  }

  public void setApplyLabel(String applyLabel) {
    getStateHelper().put(PropertyKeys.applyLabel, applyLabel);
  }

  public String getCancelLabel() {
    return (String) getStateHelper().eval(PropertyKeys.cancelLabel, null);
  }

  public void setCancelLabel(String cancelLabel) {
    getStateHelper().put(PropertyKeys.cancelLabel, cancelLabel);
  }

  public String getFromLabel() {
    return (String) getStateHelper().eval(PropertyKeys.fromLabel, null);
  }

  public void setFromLabel(String fromLabel) {
    getStateHelper().put(PropertyKeys.fromLabel, fromLabel);
  }

  public String getToLabel() {
    return (String) getStateHelper().eval(PropertyKeys.toLabel, null);
  }

  public void setToLabel(String toLabel) {
    getStateHelper().put(PropertyKeys.toLabel, toLabel);
  }

  public String getCustomRangeLabel() {
    return (String) getStateHelper().eval(PropertyKeys.customRangeLabel, null);
  }

  public void setCustomRangeLabel(String customRangeLabel) {
    getStateHelper().put(PropertyKeys.customRangeLabel, customRangeLabel);
  }

  // Widget

  @Override
  public String resolveWidgetVar() {
    FacesContext context = getFacesContext();
    String userWidgetVar = (String) getAttributes().get("widgetVar");

    if (userWidgetVar != null)
      return userWidgetVar;
    else
      return "widget_" + getClientId(context).replaceAll("-|" + getSeparatorChar(context), "_");
  }

  // InputHolder

  @Override
  public String getInputClientId() {
    return this.getClientId(getFacesContext());
  }

  @Override
  public String getValidatableInputClientId() {
    return this.getClientId(getFacesContext());
  }

  @Override
  public void setLabelledBy(String labelledBy) {
    getStateHelper().put("labelledby", labelledBy);
  }

  @Override
  public String getLabelledBy() {
    return (String) getStateHelper().get("labelledby");
  }

  // Events

  @Override
  public Collection<String> getEventNames() {
    return of(Event.values())
        .map(Enum::name)
        .collect(Collectors.toList());
  }

  @Override
  public Collection<String> getUnobstrusiveEventNames() {
    return unmodifiableCollection(asList(Event.apply.name()));
  }

  @Override
  public void queueEvent(FacesEvent event) {
    FacesContext context = getFacesContext();

    if (this.isRequestSource(context) && (event instanceof AjaxBehaviorEvent)) {
      Map<String, String> params = context.getExternalContext()
          .getRequestParameterMap();
      String eventName = params.get(PARTIAL_BEHAVIOR_EVENT_PARAM);

      if (eventName != null) {
        if (eventName.equals(Event.apply.name())) {
          customEvents.put(Event.apply.name(), (AjaxBehaviorEvent) event);
        }
        else {
          super.queueEvent(event); // regular events
        }
      }
    }
    else {
      super.queueEvent(event); // valueChange
    }
  }

  @Override
  public void validate(FacesContext context) {
    super.validate(context);

    if (isValid() && isRequestSource(context)) {
      for (Iterator<String> customEventIter = customEvents.keySet()
          .iterator(); customEventIter.hasNext();) {
        AjaxBehaviorEvent behaviorEvent = customEvents.get(customEventIter.next());
        SelectEvent selectEvent = new SelectEvent(this, behaviorEvent.getBehavior(), this.getValue());

        if (behaviorEvent.getPhaseId()
            .equals(APPLY_REQUEST_VALUES)) {
          selectEvent.setPhaseId(PROCESS_VALIDATIONS);
        }
        else {
          selectEvent.setPhaseId(INVOKE_APPLICATION);
        }

        super.queueEvent(selectEvent);
      }
    }
  }

  // Convert

  private boolean conversionFailed = false;

  public void setConversionFailed(boolean value) {
    this.conversionFailed = value;
  }

  public boolean isConversionFailed() {
    return this.conversionFailed;
  }

  // Utils

  public boolean hasTime() {
    return calculatePattern().contains(":");
  }

  public boolean has24HourTime() {
    return calculatePattern().contains("H");
  }

  public String calculatePattern() {
    if (calculatedPattern == null) {
      calculatedPattern = this.getPattern();
      Locale locale = this.calculateLocale();

      // locale pattern
      if (calculatedPattern == null) {
        calculatedPattern = this.isTimePicker()
            ? ((SimpleDateFormat) getDateTimeInstance(SHORT, SHORT, locale)).toPattern()
            : ((SimpleDateFormat) getDateInstance(SHORT, locale)).toPattern();
      }
      // 24 hour pattern
      if (this.isTimePicker24Hour() && !calculatedPattern.contains("H")) {
        calculatedPattern = calculatedPattern
            .replaceFirst("\\s?a\\s?", "") // pm/am
            .replaceFirst("hh?", "HH"); // 24 hour
      }
      // seconds pattern
      if (this.isTimePickerSeconds() && !calculatedPattern.contains("s")) {
        calculatedPattern = calculatedPattern
            .replaceFirst(":(m*)", ":$1:ss"); // adds seconds after minutes
      }
    }

    return calculatedPattern;
  }

  public TimeZone calculateTimeZone() {
    if (appropriateTimeZone == null) {
      Object usertimeZone = getTimeZone();
      if (usertimeZone != null) {
        if (usertimeZone instanceof String)
          appropriateTimeZone = java.util.TimeZone.getTimeZone((String) usertimeZone);
        else if (usertimeZone instanceof java.util.TimeZone)
          appropriateTimeZone = (java.util.TimeZone) usertimeZone;
        else
          throw new IllegalArgumentException("TimeZone could be either String or java.util.TimeZone");
      }
      else {
        appropriateTimeZone = java.util.TimeZone.getDefault();
      }
    }

    return appropriateTimeZone;
  }

  public Locale calculateLocale() {
    if (this.calculatedLocale == null) {
      this.calculatedLocale = resolveLocale(getLocale(), getClientId());
    }
    return calculatedLocale;
  }

  private boolean isRequestSource(FacesContext context) {
    return this.getClientId(context)
        .equals(context.getExternalContext()
            .getRequestParameterMap()
            .get(Constants.RequestParams.PARTIAL_SOURCE_PARAM));
  }
}