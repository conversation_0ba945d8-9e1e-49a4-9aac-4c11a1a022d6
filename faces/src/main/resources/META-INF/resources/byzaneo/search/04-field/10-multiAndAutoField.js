/**
 * Byzaneo Query Field Multi and Auto
 *
 * Field for the String type with autocompletion and selectItem
 *
 * <AUTHOR> LEMESLE
 */

Byzaneo.Search.Field.MultiAndAutoField = Byzaneo.Search.Field.MultiselectField.extend({

    init : function(option, that) {
        this._super(option, that);
    },

    /**
     * Creates a multiselect element for string and attaches it to the div
     */
    attachMultiselectItem: function() {
        /* Html Component */
        this.bqlFieldName.component.setAttribute("multiple", "multiple");
        this.bqlFieldName.component.setAttribute("autocomplete", "false"); // prepare pour ajouter l'aucomplétion

        this._super();
        this.autocompleteMethod();
    },

    setConfig: function () {
        this._super();
        this.config.nonSelectedText = this.copyValue(this.labels.multiselect.nonSelectedTextShort);

        var that = this;
        this.config.onChange = function(element, checked) {
            that.bqlFieldName.jsComponent.onChange(that.bqlFieldName, element.val(), checked, that);
        };
    },

    /**
     * Adds options for the component
     *
     * @param ajaxSuggestions : suggestions array from server
     */
    buildOption: function(ajaxSuggestions) {
        this._super();

        /* If no result, add option 'no result' */
        if(!ajaxSuggestions || ajaxSuggestions.length == 0) {
            var option = Byzaneo.Search.ComponentHelper.prototype.createHtmlOption(
                this.labels.multiselect.noResult);
            $(option).attr('disabled', 'disabled');
            $(this.bqlFieldName.component).append(option);
        }
        else {
            var cpt = 0;
            var i = 0;
            var value;
            while(cpt<15 && i < ajaxSuggestions.length) {
                value = typeof ajaxSuggestions[i].value == 'undefined'
                    ? ajaxSuggestions[i]
                    : ajaxSuggestions[i].value;
                if (value != "") {
                    $(this.bqlFieldName.component).append(
                        Byzaneo.Search.ComponentHelper.prototype.createHtmlOption(value));
                    cpt = cpt + 1;
                }
                i = i+1;
            }
        }
    },

    /**
     * Rebuilds the plugin multiselect
     */
    buildMultiselect: function() {
        this._super();

        this.bindAutocompleteMethod();
    },

    rebuildWithOption: function(ajaxSuggestions) {
        $(this.bqlFieldName.component).multiselect('destroy');
        this.buildOption(ajaxSuggestions);
        this.buildMultiselect();
    },

    autocompleteMethod: function() {
        var that = this;
        this._makeRequest({
            value: $(that.bqlFieldName.htmlComponent.htmlContent).find(".multiselect-search").val(),
            success: function (responseXML, status, xhr) {
                var value = $(that.bqlFieldName.htmlComponent.htmlContent).find(".multiselect-search").val();
                var content = responseXML.getElementById(that.widgetInstance.id).textContent;
                content = content.split("<ul>").join("");
                content = content.split("</ul>").join("");
                content = content.split("<li>").join("");
                content = content.split("</li>");
                that.rebuildWithOption(content);
                that.focusOnField(value);
                return true;
            }
        });
    },

    _makeRequest: function (options) {
        var that = this;
        var opts = {
            source : this.widgetInstance.instance.id,
            update : this.widgetInstance.instance.id,
            formId : this.widgetInstance.instance.formId,
            onsuccess: options.success
        };
        opts.params = [ {
            name : this.widgetInstance.id + '_fieldname',
            value : this.bqlFieldName.displayName
        }];
        opts.params.push({
            name : this.widgetInstance.id + '_fieldvalue',
            value : options.value
        });
        PrimeFaces.ajax.AjaxRequest(opts);
    },

    bindAutocompleteMethod: function() {
        var that = this;
        $(this.bqlFieldName.htmlComponent.htmlContent).find(".multiselect-search").on({
            "keyup": function (e) {
                that.autocompleteMethod();
            }
        });
    },

    /**
     * Keep fieldValue and focus on search input
     */
    focusOnField: function(value) {
        if($(this.bqlFieldName.component).attr('autocomplete') == "true") {
            $(this.bqlFieldName.htmlComponent.htmlContent).find('button.multiselect').click();
            $(this.bqlFieldName.htmlComponent.htmlContent).find('input.multiselect-search').val(value);
            $(this.bqlFieldName.htmlComponent.htmlContent).find('input.multiselect-search').focus();
        }
        else {
            $(this.bqlFieldName.component).attr('autocomplete', 'true');
        }
    }
});