/**
 * Byzaneo Query Date Field
 *
 * Field for the date type
 *
 * <AUTHOR> LEMESLE
 */

Byzaneo.Search.Field.DateField = Byzaneo.Search.Field.extend({

	init : function(option, that) {
		this._super(option, that);

		/* Format */
		this.format = this.widgetInstance.instance.patternDate;
		// Encode '+' for BQL
		this.timeZone = this.widgetInstance.instance.timeZone.replace("+", "\\u002b");

		// Create Component
        this.attachDateRangePicker();
	},

	/**
	 * Creates a daterangepicker element
	 */
	attachDateRangePicker : function() {
        /* Html Component */
        this.bqlFieldName.component.setAttribute("name", "daterange");

		this.setLabels();
		this.setConfig();
		this.setData();

        // Build
        $(this.bqlFieldName.component).daterangepicker(this.config);
	},

    /**
     * Sets the param label with the configuration of daterangepicker
     */
    setLabels : function() {
        if(this.labels)
        	return;

        moment.locale(this.widgetInstance.cfg.locale);
        moment.localeData()._invalidDate = '';

        this.labels = {};
        this.labels.daterangepicker = Byzaneo.configureLocale('InputDatePeriod', this.widgetInstance.cfg, moment.locale(), false);
    },

    /**
	 * Sets the configuration of DateRangePicker
     */
	setConfig : function () {
        this.config = {
            autoUpdateInput: false,
            opens: "center",
            showDropdowns: true,
            locale: {
                format: this.format,
                applyLabel: this.labels.daterangepicker.applyLabel,
                cancelLabel: this.labels.daterangepicker.cancelLabel,
                fromLabel: this.labels.daterangepicker.fromLabel,
                toLabel: this.labels.daterangepicker.toLabel,
                customRangeLabel: this.labels.daterangepicker.customRangeLabel
            }
        };

        /* If selectItem */
        if(this.bqlFieldName.selectItems)
            this.config.ranges = $.parseJSON(this.bqlFieldName.selectItems);
    },

    /**
     * Sets the data of DateRangePicker
     */
	setData : function () {
        if(this.bqlFieldName.data) {
            var arrayOfDate = this.bqlFieldName.data.split("-");
            this.bqlFieldName.valueField = arrayOfDate;
            this.config.startDate = arrayOfDate[0];
            this.config.endDate = arrayOfDate[1];
        }
    },

    /**
     * Rebuilds this field
     */
    rebuild : function() {
        this.attachDateRangePicker();
    }
});