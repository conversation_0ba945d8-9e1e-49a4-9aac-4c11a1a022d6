/**
 * 
 */
package com.byzaneo.faces.util;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Locale;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @company Generix Group
 * @date 9 oct. 2019
 */
class FacesHelperTest {

  @Test
  void getMessagesWhenLocalIsNull() {
    assertNull(FacesHelper.getMessagesByLocal("label.search", null));
  }

  @Test
  void getMessagesInEnglish() {
    assertTrue(StringUtils.isNotEmpty(FacesHelper.getMessagesByLocal("labels.search", Locale.ENGLISH)));
  }

  @Test
  void getMessagesInFrench() {
    assertTrue(StringUtils.isNotEmpty(FacesHelper.getMessagesByLocal("labels.search", Locale.FRENCH)));
  }
}
